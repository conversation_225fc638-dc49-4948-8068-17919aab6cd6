<!doctype html>
<html lang="zxx">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Ideas we like to share with you">
        <meta name="keywords" content="Ed-admin blog">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="../../assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="../../assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="../../assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="../../assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="../../assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="../../assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="../../assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="../../assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="../../assets/css/responsive.css">

        <title>Ed-admin: Ideas we like to share with you</title>

        <link rel="icon" type="image/png" href="../../assets/img/favicon-Edadmin.ico">

        <!-- Calendly link widget begin -->
        <link href="https://assets.calendly.com/assets/external/widget.css" rel="stylesheet">
        <script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript" async></script>
        <!-- Calendly link widget end -->

        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>

        <style>
            .post-image img {
                height: 210px;
                width: 100%;
                object-fit: cover; /* Optional to maintain aspect ratio */
            }
            /* Hover effects for tags */
            .tag {
                border: black 1px solid;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s, color 0.3s;
                /* For the trending tags with remove icon, we'll use inline-flex */
                display: inline-flex;
                align-items: center;
            }
            .tag:hover {
                background-color: #FFA616;
                color: white;
            }
            /* Highlight selected tag */
            .tag.selected {
                background-color: #FFA616;
                color: white;
            }
            /* The remove icon (SVG) – hidden by default, with extra left margin */
            .tag .remove-tag {
                display: none;
                margin-left: 10px;
                cursor: pointer;
            }
            /* Improve blog card hover effect */
            .blog-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            }
            /* Pagination (Accordion) Styles */
            #pagination {
                margin-top: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                font-size: 16px;
                user-select: none;
            }
            #pagination .page-arrow,
            #pagination .page-number {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                transition: background-color 0.3s, color 0.3s;
            }
            #pagination .page-arrow:hover,
            #pagination .page-number:hover {
                background-color: #FFA616;
                color: #fff;
            }
            #pagination .page-number.active {
                background-color: #006EB3;
                color: #fff;
            }
            /* Ellipsis styling */
            #pagination .ellipsis {
                padding: 0 5px;
                cursor: default;
            }


            #trending-tags {
                width: 70%;
                }

        </style>
    </head>

    <body>
        <span data-menuid="Resources blog" class="d-none"></span>

        <!-- Start PopUps Area -->
        <div data-include="/popups/demonow"></div> 
        <div data-include="/popups/bookdemo"></div>
        <div data-include="/popups/downloadnow"></div>
        <div data-include="/popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="/common/header2"></div>
        <!-- End Header Area -->

        <div style="width: 100%; text-align: center;">
            <h1 style="
                display: inline-block; /* Make the element shrink to fit the text */
                background: linear-gradient(102.33deg, #006EB3 -17.27%, #FF9300 106.25%);
                background-size: 100%; /* Ensure the background fits the element */
                background-position: center; /* Center the gradient */
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                color: transparent;">
              Ed-admin <br>Blog
            </h1>
        </div>

        <!-- Dropdown Menu for Tags -->
        <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
            <div style="width: 300px; margin-top: 20px;">
                <select
                    id="tagDropdown"
                    onchange="handleDropdownChange(this.value)"
                    style="
                        /* Size & layout */
                        width: 100%;
                        padding: 10px 40px 10px 12px; 
                        font-size: 16px;
                        color: #555;
                        
                        /* Border & background color */
                        border: 1px solid #ccc;
                        border-radius: 8px;
                        background-color: #fff;
            
                        /* Remove native arrow and use custom arrow */
                        -webkit-appearance: none;
                        -moz-appearance: none;
                        appearance: none;
            
                        /* Use an arrow image from an external URL */
                        background: url('../../assets/arrow-down.png') 
                                    no-repeat right 12px center / 10px 10px, 
                                    #fff;
                    "
                >
                    <option value="All">Select Subject</option>
                    <!-- Additional options inserted here -->
                </select>
            </div>
        </div>
        
        <!-- Recently Added Section -->
        <div id="recently-added-section" style="width: 100%; text-align: center; margin-top: 40px;">
            <section style="width: 100%; text-align: center; margin-top: 40px;">
                <h2 style="margin-bottom: 80px;">Recently Added</h2>
                <div id="recently-added-1" style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap;">
                    <!-- Dynamic blog cards for first 3 blogs will be injected here -->
                </div>
            </section>
        </div>
        <!-- End Recently Added Section -->

        <!-- Trending Tags Section -->
        <section style="width: 100%; text-align: center; margin-top: 60px; display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <h2 style="margin-bottom: 25px;">Trending Tags</h2>
            <div
                id="trending-tags"
               
                >
                <!-- Fixed trending tags will be injected here -->
            </div>
        </section>

        <!-- All Blogs Section -->
        <section style="width: 100%; text-align: center; margin-top: 20px; display: flex; justify-content: center; align-items: center; flex-direction: column; margin-bottom: 40px;">
            <h2 style="margin-bottom: 50px;">All Blogs</h2>
            <div id="recently-added-2" style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap; width: 60%;">
                <!-- Dynamic blog cards for "All Blogs" will be injected here -->
            </div>
            <!-- Pagination/Accordion Controls -->
            <div id="pagination"></div>
        </section>

        <!-- Start Free Trial Area -->
        <section class="free-trial-area ptb-100 bg-f4f5fe">
            <div class="container">
                <div class="free-trial-content video-box">
                    <h2>Like to see Ed-admin in action? Ask for a demo!</h2>
                    <a href="#" class="default-btn" style="padding-left: 25px;" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/ed-admin/free-demo'});return false;">
                        Free Demo <span></span>
                    </a>
                </div>
            </div>

            <div class="shape10"><img src="../../assets/img/shape/10.png" alt="image"></div>
            <div class="shape11"><img src="../../assets/img/shape/7.png" alt="image"></div>
            <div class="shape12"><img src="../../assets/img/shape/11.png" alt="image"></div>
            <div class="shape13"><img src="../../assets/img/shape/12.png" alt="image"></div>
        </section>
        <!-- End Free Trial Area -->

        <!-- Start Footer Area -->
        <div data-include="/common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>
        
        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="../../assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="../../assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="../../assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="../../assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="../../assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="../../assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="../../assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="../../assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="../../assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="../../assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="../../assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="../../assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="../../assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="../../assets/js/particles.min.js"></script>
        <script src="../../assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="../../assets/js/main.js"></script>

        <!-- Custom Script to Fetch and Display Blogs and Tags with Pagination -->
        <script>
            // Global variables
            let allBlogs = [];
            let otherBlogs = []; // everything after the first 3
            let currentPage = 1; // used for the "All Blogs" pagination
            const blogsPerPage = 9; // show 9 per page in "All Blogs"

            // Function to fetch blogs from WordPress API
            async function fetchBlogs() {
                const recentlyAdded1Container = document.getElementById('recently-added-1');
                const recentlyAdded2Container = document.getElementById('recently-added-2');
                const trendingTagsContainer = document.getElementById('trending-tags');
                const recentlyAddedSection = document.getElementById('recently-added-section');
                
                if (!recentlyAdded1Container || !recentlyAdded2Container || !trendingTagsContainer || !recentlyAddedSection) {
                    console.error('One or more containers not found');
                    return;
                }

                // Show temporary "loading" placeholders
                recentlyAdded1Container.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading blogs...
                    </div>
                `;
                recentlyAdded2Container.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading blogs...
                    </div>
                `;
                trendingTagsContainer.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading tags...
                    </div>
                `;

                try {
                    // Increased per_page from 20 to 100
                    const response = await fetch(
                        'https://public-api.wordpress.com/wp/v2/sites/edadmin7.wordpress.com/posts?_embed=true&per_page=100'
                    );
                    if (!response.ok) throw new Error('Failed to fetch blogs');
                    const blogs = await response.json();

                    // Filter posts so that only those in the category "Blogs" are stored
                    allBlogs = blogs.filter(blog => {
                        if (blog._embedded && blog._embedded['wp:term']) {
                            return blog._embedded['wp:term'].some(termGroup =>
                                termGroup.some(term => term.taxonomy === 'category' && term.name === 'Blogs')
                            );
                        }
                        return false;
                    });

                    // Clear "loading" messages
                    recentlyAdded1Container.innerHTML = '';
                    recentlyAdded2Container.innerHTML = '';
                    trendingTagsContainer.innerHTML = '';

                    // Display first 3 blogs in the "Recently Added" section
                    const firstThreeBlogs = allBlogs.slice(0, 3);
                    displayBlogs(firstThreeBlogs, recentlyAdded1Container);

                    // The remaining blogs will be paginated
                    otherBlogs = allBlogs.slice(3);

                    // Display the first page (9 blogs) in "All Blogs"
                    displayPaginatedBlogs(1);

                    // --- Fixed Trending Tags Section ---
                    const fixedTags = [
                        "Education Technology",
                        "School Management Software",
                        "Academic Management",
                        "Education Innovation",
                        "International Education Solutions",
                        "School Operations",
                        "Ed-admin Multi-Portal App",
                        "Student Support Services",
                        "Digital Management",
                        "Digital Learning"
                    ];
                    // Use the updated displayTags with the X icon logic
                    displayTags(fixedTags, trendingTagsContainer);

                    // --- Populate Dropdown with All Unique Tags from WordPress ---
                    const wordpressTags = getUniqueTags(extractTags(allBlogs));
                    populateTagDropdown(wordpressTags);

                } catch (error) {
                    console.error('Error fetching blogs:', error);
                    recentlyAdded1Container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load blogs. Please try again later.
                        </div>
                    `;
                    recentlyAdded2Container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load blogs. Please try again later.
                        </div>
                    `;
                    trendingTagsContainer.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load tags. Please try again later.
                        </div>
                    `;
                }
            }

            // Show the "All Blogs" for a given page
            function displayPaginatedBlogs(page) {
                currentPage = page;

                const recentlyAdded2Container = document.getElementById('recently-added-2');
                if (!recentlyAdded2Container) return;

                // Slice the correct segment of otherBlogs
                const startIndex = (page - 1) * blogsPerPage;
                const endIndex = startIndex + blogsPerPage;
                const blogsToShow = otherBlogs.slice(startIndex, endIndex);

                // Display these blogs
                displayBlogs(blogsToShow, recentlyAdded2Container);

                // Also update the pagination UI
                displayPagination();
            }

            // Build a pagination (accordion) UI
            function displayPagination() {
                const paginationContainer = document.getElementById('pagination');
                if (!paginationContainer) return;

                const totalPosts = otherBlogs.length;
                const totalPages = Math.ceil(totalPosts / blogsPerPage);

                let paginationHTML = '';

                // Left arrow
                if (currentPage > 1) {
                    paginationHTML += `<span class="page-arrow" onclick="changePage(${currentPage - 1})">&larr;</span>`;
                } else {
                    paginationHTML += `<span class="page-arrow" style="opacity:0.5;cursor:not-allowed;">&larr;</span>`;
                }

                // Decide how to render page numbers
                const maxVisible = 5; // how many distinct page links we want to show

                if (totalPages <= maxVisible) {
                    for (let i = 1; i <= totalPages; i++) {
                        paginationHTML += getPageNumberHTML(i);
                    }
                } else {
                    if (currentPage <= 3) {
                        for (let i = 1; i <= 3; i++) {
                            paginationHTML += getPageNumberHTML(i);
                        }
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(totalPages - 1);
                        paginationHTML += getPageNumberHTML(totalPages);
                    } else if (currentPage >= totalPages - 2) {
                        paginationHTML += getPageNumberHTML(1);
                        paginationHTML += getPageNumberHTML(2);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        for (let i = totalPages - 2; i <= totalPages; i++) {
                            paginationHTML += getPageNumberHTML(i);
                        }
                    } else {
                        paginationHTML += getPageNumberHTML(1);
                        paginationHTML += getPageNumberHTML(2);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(currentPage);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(totalPages - 1);
                        paginationHTML += getPageNumberHTML(totalPages);
                    }
                }

                // Right arrow
                if (currentPage < totalPages) {
                    paginationHTML += `<span class="page-arrow" onclick="changePage(${currentPage + 1})">&rarr;</span>`;
                } else {
                    paginationHTML += `<span class="page-arrow" style="opacity:0.5;cursor:not-allowed;">&rarr;</span>`;
                }

                // Remove the check that hides pagination if there's only one page:
                // (This ensures the pagination is always visible.)
                paginationContainer.innerHTML = paginationHTML;
            }

            // Utility to produce a clickable or active page link
            function getPageNumberHTML(pageNumber) {
                const activeClass = (pageNumber === currentPage) ? 'active' : '';
                return `
                    <span 
                        class="page-number ${activeClass}"
                        onclick="changePage(${pageNumber})"
                    >
                        ${pageNumber}
                    </span>
                `;
            }

            // When a page number (or arrow) is clicked
            function changePage(newPage) {
                const totalPages = Math.ceil(otherBlogs.length / blogsPerPage);
                if (newPage < 1 || newPage > totalPages) return;
                displayPaginatedBlogs(newPage);
            }

            // Function to display an array of blog objects in a given container with alternating title background colors
            function displayBlogs(blogs, container) {
                if (!container) {
                    console.error('Blog container not found');
                    return;
                }
                try {
                    if (blogs.length === 0) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                No blogs found.
                            </div>
                        `;
                        return;
                    }

                    // Build blog cards. The inner title div will alternate its background color.
                    const blogsHTML = blogs.map((blog, index) => {
                        // Determine alternating background colors for the title div.
                        const titleBgColor = (index % 2 === 0) ? "#FFA616" : "#3F51B5";
                        return `
                        <div 
                            class="blog-card" 
                            style="width: 300px; height: auto; background-color: #EEEDED; border-radius: 30px; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 10px; cursor: pointer; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: transform 0.3s ease; margin-top: 50px;"
                            onclick="navigateToBlog('${blog.id}', '${getFeaturedImageUrl(blog)}')"
                        >
                            <img 
                                src="${getFeaturedImageUrl(blog)}" 
                                style="width: 90%; height: 180px; border-radius: 30px; margin-top: -70px;" 
                                alt="${blog.title.rendered || 'Blog post'}" 
                                onerror="this.src='../../assets/img/default-blog-image.jpg';" 
                            />
                            <!-- The inner title div now alternates its background color -->
                            <div style="width: 90%; background-color: ${titleBgColor}; border-radius: 15px; padding: 10px;">
                                <p style="color: white; font-weight: bold; margin: 0;">
                                    ${blog.title.rendered || 'Untitled Post'}
                                </p>
                            </div>
                            <p style="text-align: left; width: 90%; color: #555; margin-top: 10px; margin-bottom: 10px">
                                ${formatDate(blog.date)}
                            </p>
                        </div>
                        `;
                    }).join('');

                    container.innerHTML = blogsHTML;
                } catch (error) {
                    console.error('Error displaying blogs:', error);
                    container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Error displaying blogs. Please try again later.
                        </div>
                    `;
                }
            }

            // Helper function to format date
            function formatDate(dateString) {
                try {
                    const date = new Date(dateString);
                    const options = { year: 'numeric', month: 'long', day: 'numeric' };
                    return date.toLocaleDateString('en-US', options);
                } catch (error) {
                    console.error('Error formatting date:', error);
                    return dateString;
                }
            }

            // Helper function to get a blog’s featured image
            function getFeaturedImageUrl(post) {
                try {
                    if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                        const media = post._embedded['wp:featuredmedia'][0];
                        if (media.media_details && media.media_details.sizes && media.media_details.sizes.medium) {
                            return media.media_details.sizes.medium.source_url;
                        }
                        if (media.source_url) {
                            return media.source_url;
                        }
                    }
                    if (post.jetpack_featured_media_url) {
                        return post.jetpack_featured_media_url;
                    }
                    return '../../assets/img/default-blog-image.jpg';
                } catch (error) {
                    console.error('Error getting featured image:', error);
                    return '../../assets/img/default-blog-image.jpg';
                }
            }

            // Helper function to extract all tags from the blogs array
            function extractTags(blogs) {
                const tags = [];
                blogs.forEach(blog => {
                    if (blog._embedded && blog._embedded['wp:term']) {
                        blog._embedded['wp:term'].forEach(termGroup => {
                            termGroup.forEach(term => {
                                if (term.taxonomy === 'post_tag') {
                                    tags.push(term.name);
                                }
                            });
                        });
                    }
                });
                return tags;
            }

            // Helper function to return a list of unique tags
            function getUniqueTags(tagsArray) {
                const uniqueTagsSet = new Set(tagsArray.map(tag => tag.trim()).filter(tag => tag));
                return Array.from(uniqueTagsSet);
            }

            /* 
             * Updated displayTags: Now outputs each tag with a remove (X) icon (except the "All" option)
             * and uses the toggleTrendingTag() function as its click handler.
             */
            function displayTags(tags, container) {
                if (!container) {
                    console.error('Tags container not found');
                    return;
                }
                try {
                    const allTagHTML = `
                        <p class="tag" onclick="toggleTrendingTag(this, 'All')">
                            <span class="tag-text">All</span>
                        </p>
                    `;
                    const tagsHTML = tags.map(tag => `
                        <p class="tag" onclick="toggleTrendingTag(this, '${tag}')">
                            <span class="tag-text">${tag}</span>
                            <span class="remove-tag">
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 50 50">
                                    <path fill="#ffffff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                                </svg>
                            </span>
                        </p>
                    `).join('');
                    container.innerHTML = allTagHTML + tagsHTML;
                } catch (error) {
                    console.error('Error displaying tags:', error);
                    container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Error displaying tags. Please try again later.
                        </div>
                    `;
                }
            }

            // Populate the dropdown menu with all unique tags (from WordPress)
            function populateTagDropdown(tags) {
                const dropdown = document.getElementById("tagDropdown");
                if (!dropdown) return;
                dropdown.innerHTML = '<option value="All">Select Subject</option>';
                tags.forEach(tag => {
                    dropdown.innerHTML += `<option value="${tag}">${tag}</option>`;
                });
            }

            // Handle dropdown change event by filtering blogs based on the selected tag
            function handleDropdownChange(selectedTag) {
                // Clear any trending tag selection when using the dropdown
                clearTrendingTagSelection();
                filterByTag(selectedTag);
            }

            // Filter blogs by tag (overrides the "All Blogs" pagination to show only filtered results)
            function filterByTag(tag) {
                const trendingTagsContainer = document.getElementById('trending-tags');
                const tagElements = trendingTagsContainer.getElementsByClassName('tag');
                const recentlyAddedSection = document.getElementById('recently-added-section');
                const recentlyAdded1Container = document.getElementById('recently-added-1');
                const recentlyAdded2Container = document.getElementById('recently-added-2');

                Array.from(tagElements).forEach(element => {
                    element.classList.remove('selected');
                });

                if (tag === 'All') {
                    recentlyAddedSection.style.display = 'block';
                    const firstThree = allBlogs.slice(0, 3);
                    displayBlogs(firstThree, recentlyAdded1Container);
                    otherBlogs = allBlogs.slice(3);
                    currentPage = 1;
                    displayPaginatedBlogs(currentPage);
                    return;
                }

                const clickedTagElement = Array.from(tagElements).find(el => el.textContent.trim() === tag);
                if (clickedTagElement) {
                    clickedTagElement.classList.add('selected');
                }

                recentlyAddedSection.style.display = 'none';

                const filteredBlogs = allBlogs.filter(blog => {
                    if (blog._embedded && blog._embedded['wp:term']) {
                        for (const termGroup of blog._embedded['wp:term']) {
                            for (const term of termGroup) {
                                if (term.taxonomy === 'post_tag' && term.name === tag) {
                                    return true;
                                }
                            }
                        }
                    }
                    return false;
                });

                displayBlogs(filteredBlogs, recentlyAdded2Container);
                document.getElementById('pagination').innerHTML = '';
            }

            // Toggle trending tag selection for blogs (shows/hides the X icon and filters accordingly)
            function toggleTrendingTag(element, tag) {
                if (element.classList.contains('selected')) {
                    clearTrendingTagSelection();
                    filterByTag('All');
                } else {
                    clearTrendingTagSelection();
                    element.classList.add('selected');
                    const removeSpan = element.querySelector('.remove-tag');
                    if (removeSpan) {
                        removeSpan.style.display = 'inline';
                    }
                    filterByTag(tag);
                }
            }

            // Clear any trending tag selection (removes the 'selected' class and hides all X icons)
            function clearTrendingTagSelection() {
                const trendingTagsContainer = document.getElementById('trending-tags');
                if (trendingTagsContainer) {
                    const tagElements = trendingTagsContainer.getElementsByClassName('tag');
                    Array.from(tagElements).forEach(el => {
                        el.classList.remove('selected');
                        const removeSpan = el.querySelector('.remove-tag');
                        if (removeSpan) {
                            removeSpan.style.display = 'none';
                        }
                    });
                }
            }

            // Navigate to read.html
            function navigateToBlog(blogId, featuredImageUrl) {
                try {
                    localStorage.setItem('selectedBlogId', blogId);
                    localStorage.setItem('selectedBlogImage', featuredImageUrl);
                    window.location.href = 'read.html';
                } catch (error) {
                    console.error('Error navigating to blog:', error);
                    alert('Error opening blog post. Please try again.');
                }
            }

            // On DOM ready, fetch the blogs
            document.addEventListener('DOMContentLoaded', function() {
                fetchBlogs();
            });
        </script>
    </body>
</html>
