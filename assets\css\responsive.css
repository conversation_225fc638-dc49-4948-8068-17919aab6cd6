/* Max width 767px */
@media only screen and (max-width: 767px) {
  body {
    font-size: 14px;
  }
  p {
    font-size: 14px;
  }
  .ptb-100 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .pt-100 {
    padding-top: 60px;
  }
  .pb-100 {
    padding-bottom: 60px;
  }
  .pt-70 {
    padding-top: 30px;
  }
  .pb-70 {
    padding-bottom: 30px;
  }
  .plr-15 {
    padding-left: 15px;
    padding-right: 15px;
  }
  .section-title {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 40px;
  }
  .section-title h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .default-btn {
    font-size: 13px;
    padding-left: 45px;
    padding-right: 20px;
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .optional-btn {
    font-size: 13px;
    padding-left: 45px;
    padding-right: 20px;
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .default-btn i {
    font-size: 18px;
    left: 20px;
  }
  .navbar-area .container {
    max-width: 100%;
  }
  .navbar-area.navbar-style-two .container {
    max-width: 100%;
  }
  .main-banner {
    padding-top: 60px;
  }
  .main-banner-content {
    height: auto;
  }
  .main-banner-content .content {
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    text-align: center;
    margin-left: 0;
    margin-top: 0;
  }
  .main-banner-content .content h1 {
    font-size: 30px;
  }
  .main-banner-content .content h2 {
    font-size: 25px;
  }
  .main-banner-content .content h3 {
    font-size: 20px;
  }
  .main-banner-content .content p {
    margin-top: 15px;
    max-width: 100%;
  }
  .main-banner-content .content .default-btn {
    margin-top: 10px;
  }
  .main-banner-content .content .optional-btn {
    margin-top: 10px;
  }
  .banner-content {
    padding-top: 60px;
    padding-bottom: 0;
  }
  .banner-content .content {
    max-width: 100%;
    padding-right: 0;
    text-align: center;
    margin-left: 0;
  }
  .banner-content .content h1 {
    font-size: 30px;
  }
  .banner-content .content p {
    margin-top: 15px;
    max-width: 100%;
  }
  .banner-content .content .default-btn {
    margin-top: 10px;
  }
  .banner-image {
    margin-top: 60px;
    height: auto;
    border-radius: 0;
    background-image: unset !important;
    padding-top: 50px;
  }
  .banner-image img {
    display: inline-block;
  }
  .banner-img {
    margin-top: 60px;
    height: auto;
    border-radius: 0;
    background-image: unset !important;
    padding-top: 50px;
  }
  .banner-img img {
    display: inline-block;
  }
  .banner-content-slides.owl-theme .owl-nav {
    text-align: center;
    margin-top: 35px;
  }
  .banner-content-slides.owl-theme .owl-nav [class*=owl-] {
    font-size: 20px;
    margin: 0 4px;
    width: 35px;
    height: 35px;
    line-height: 40px;
  }
  .chat-wrapper {
    position: relative;
    left: 0;
    top: 0;
    -webkit-transform: unset;
            transform: unset;
    width: auto;
    height: auto;
    margin-bottom: -35px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .chat-wrapper .chat-container {
    height: 365px;
  }
  .message-right .message-text {
    max-width: 100%;
  }
  .message-left .message-text {
    max-width: 100%;
  }
  .partner-area .container {
    max-width: 100%;
  }
  .partner-title {
    text-align: center;
    margin-bottom: 30px;
  }
  .partner-title h3 {
    font-size: 18px;
  }
  .services-area.bg-right-color::before {
    display: none;
  }
  .services-area.bg-left-color::before {
    display: none;
  }
  .services-content {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
  .services-content .content {
    max-width: 100%;
    text-align: center;
  }
  .services-content .content.left-content {
    margin-left: 0;
  }
  .services-content .content h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .services-content .content p {
    max-width: 100%;
    margin-top: 15px;
  }
  .services-content .content .default-btn {
    margin-top: 10px;
  }
  .services-image {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
  .services-image .image {
    margin-top: 30px;
  }
  .video-box {
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
  .video-box .video-btn {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    font-size: 50px;
  }
  .video-box .video-btn::after, .video-box .video-btn::before {
    border-radius: 50%;
  }
  .shape1, .shape2, .shape3, .shape4, .shape5, .shape6, .shape7, .shape8, .shape9, .shape10, .shape11, .shape12, .shape13, .shape20, .shape21, .shape22, .shape23, .shape24, .shape26, .shape14, .shape15, .shape16, .shape17, .shape18 {
    display: none;
  }
  .about-content {
    text-align: center;
  }
  .about-content h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .about-image {
    margin-top: 30px;
  }
  .funfacts-inner {
    max-width: 100%;
    margin-top: 40px;
    margin-left: 0;
    margin-right: 0;
  }
  .single-funfacts h3 {
    margin-bottom: 2px;
    font-size: 24px;
  }
  .single-funfacts h3 .sign-icon {
    font-size: 22px;
  }
  .contact-cta-box {
    max-width: 100%;
    padding: 25px;
    text-align: center;
    margin-left: 0;
    margin-top: 10px;
    margin-right: 0;
  }
  .contact-cta-box h3 {
    font-size: 18px;
  }
  .contact-cta-box p {
    margin-top: 10px;
  }
  .contact-cta-box .default-btn {
    position: relative;
    right: 0;
    top: 0;
    -webkit-transform: unset;
            transform: unset;
    margin-top: 15px;
  }
  .single-features-box {
    margin-top: 32px;
  }
  .single-features-box h3 {
    font-size: 18px;
  }
  .single-features-box .icon {
    width: 65px;
    height: 65px;
    font-size: 35px;
    bottom: 32px;
  }
  .single-features-box .icon::before {
    right: -10px;
    bottom: -10px;
    border-width: 3px;
  }
  .features-box {
    text-align: center;
    padding: 20px;
  }
  .features-box .icon {
    width: 70px;
    height: 70px;
    font-size: 35px;
    margin-left: auto;
    margin-right: auto;
  }
  .features-box h3 {
    font-size: 18px;
  }
  .single-feedback-item {
    margin-top: 0;
  }
  .single-feedback-item img {
    display: none !important;
  }
  .single-feedback-item .feedback-desc {
    margin-left: 0;
    padding: 25px;
  }
  .single-feedback-item .feedback-desc .rating i {
    font-size: 14px;
  }
  .single-feedback-item .feedback-desc .client-info h3 {
    font-size: 16px;
  }
  .single-feedback-item .feedback-desc .client-info span {
    font-size: 13px;
    margin-top: 5px;
  }
  .feedback-slides.owl-theme .owl-stage-outer {
    padding-top: 30px;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: -15px;
    margin-right: -15px;
    margin-top: -30px;
  }
  .feedback-slides.owl-theme .owl-nav.disabled + .owl-dots {
    margin-top: 0;
  }
  .single-testimonials-item {
    padding: 20px;
  }
  .single-testimonials-item .client-info {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 15px;
  }
  .single-testimonials-item .client-info h3 {
    font-size: 16px;
  }
  .single-testimonials-item .client-info span {
    font-size: 13px;
    margin-top: 5px;
  }
  .single-testimonials-item .testimonials-desc {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    text-align: center;
    padding-left: 0;
  }
  .single-testimonials-item .testimonials-desc .rating i {
    font-size: 14px;
  }
  .single-team-box .content h3 {
    font-size: 18px;
  }
  .single-team-box .content span {
    font-size: 13px;
  }
  .pricing-list-tab .tabs li a {
    font-size: 14px;
  }
  .pricing-list-tab .tabs li a i {
    font-size: 16px;
  }
  .single-pricing-table {
    padding-bottom: 25px;
  }
  .single-pricing-table .pricing-header {
    padding-top: 20px;
    padding-bottom: 15px;
  }
  .single-pricing-table .pricing-header h3 {
    font-size: 18px;
  }
  .single-pricing-table .price {
    font-size: 30px;
  }
  .single-pricing-table .price sup {
    top: -15px;
    font-size: 14px;
  }
  .single-pricing-table .price sub {
    bottom: 2px;
    font-size: 13px;
  }
  .single-pricing-table .pricing-features {
    margin-left: 30px;
    margin-right: 30px;
  }
  .faq-accordion {
    margin-left: 0;
    max-width: 100%;
    text-align: center;
  }
  .faq-accordion h2 {
    margin-bottom: 30px;
    font-size: 24px;
    line-height: 1.3;
  }
  .faq-accordion .accordion {
    text-align: left;
  }
  .faq-accordion .accordion .accordion-title {
    padding: 10px 40px 10px 15px;
    font-size: 13px;
  }
  .faq-accordion .accordion .accordion-title i {
    right: 15px;
  }
  .faq-accordion .accordion .accordion-content {
    font-size: 13px;
  }
  .faq-image {
    margin-top: 35px;
  }
  .single-clients-logo {
    -ms-flex: 0 0 50%;
    -webkit-box-flex: 0;
            flex: 0 0 50%;
    max-width: 50%;
  }
  .single-clients-logo:nth-child(6) {
    margin-left: 0;
  }
  .single-clients-logo:nth-child(10) {
    margin-left: 0;
  }
  .single-clients-logo a {
    padding: 15px;
  }
  .free-trial-content h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .free-trial-content p {
    margin-top: 15px;
  }
  .free-trial-content .default-btn {
    margin-top: 5px;
  }
  .single-blog-post .post-image .date {
    padding: 8px 20px 0;
    font-size: 14px;
  }
  .single-blog-post .post-content {
    padding: 20px;
  }
  .single-blog-post .post-content h3 {
    font-size: 18px;
  }
  .single-blog-post .post-content .post-info .post-by {
    -ms-flex: 0 0 55%;
    -webkit-box-flex: 0;
            flex: 0 0 55%;
    max-width: 55%;
  }
  .single-blog-post .post-content .post-info .post-by h6 {
    font-size: 14px;
  }
  .single-blog-post .post-content .post-info .details-btn {
    -ms-flex: 0 0 45%;
    -webkit-box-flex: 0;
            flex: 0 0 45%;
    max-width: 45%;
  }
  .single-blog-post .post-content .post-info .details-btn a {
    width: 35px;
    height: 35px;
    font-size: 20px;
  }
  .blog-notes {
    margin-top: 0;
  }
  .blog-notes p {
    line-height: 1.8;
  }
  .blog-details-desc .article-content h1 {
    margin-bottom: 13px;
    font-size: 18px;
    line-height: 1.3;
  }
  .blog-details-desc .article-content h2 {
    margin-bottom: 13px;
    font-size: 16px;
    line-height: 1.2;
  }
  .blog-details-desc .article-footer {
    margin-top: 25px;
  }
  .blog-details-desc .article-footer .article-tags a {
    font-size: 13px;
    margin-top: 5px;
  }
  blockquote, .blockquote {
    padding: 20px !important;
  }
  blockquote p, .blockquote p {
    font-size: 15px !important;
  }
  .prev-link-wrapper {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    padding-right: 0;
  }
  .prev-link-wrapper .prev-title {
    font-size: 14px;
  }
  .prev-link-wrapper .meta-wrapper {
    font-size: 13px;
  }
  .next-link-wrapper {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    padding-left: 0;
    margin-top: 30px;
  }
  .next-link-wrapper .next-title {
    font-size: 14px;
  }
  .next-link-wrapper .meta-wrapper {
    font-size: 13px;
  }
  .comments-area .comment-body {
    padding-left: 0;
  }
  .comments-area .comment-body .reply a {
    font-size: 12px;
  }
  .comments-area .comments-title {
    margin-bottom: 25px;
    font-size: 18px;
  }
  .comments-area .comment-author {
    font-size: 15px;
  }
  .comments-area .comment-author .avatar {
    left: 0;
    position: relative;
    display: block;
    margin-bottom: 10px;
  }
  .comments-area .comment-respond .comment-reply-title {
    font-size: 18px;
  }
  .comments-area .comment-respond .comment-form-author {
    width: 100%;
    padding-right: 0;
  }
  .comments-area .comment-respond .comment-form-email {
    width: 100%;
    padding-left: 0;
  }
  .comments-area .comment-respond .form-submit input {
    padding: 10px 25px;
    font-size: 14px;
  }
  .app-download-content {
    text-align: center;
    margin-top: 30px;
  }
  .app-download-content .sub-title {
    padding: 4px 20px;
    margin-bottom: 12px;
    font-size: 13px;
  }
  .app-download-content h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .app-download-content .btn-box {
    margin-top: 25px;
  }
  .app-download-content .btn-box .apple-store-btn {
    text-align: left;
    margin-right: 0;
    display: block;
    width: 100%;
    padding: 12px 25px 12px 74px;
    font-size: 13px;
  }
  .app-download-content .btn-box .apple-store-btn span {
    font-size: 18px;
  }
  .app-download-content .btn-box .play-store-btn {
    text-align: left;
    display: block;
    width: 100%;
    padding: 12px 25px 12px 74px;
    font-size: 13px;
    margin-top: 10px;
  }
  .app-download-content .btn-box .play-store-btn span {
    font-size: 18px;
  }
  .subscribe-content {
    padding: 35px 25px;
  }
  .subscribe-content h2 {
    max-width: 100%;
    font-size: 25px;
    line-height: 1.3;
    margin-left: 0;
    margin-right: 0;
  }
  .subscribe-content form {
    max-width: 100%;
    margin-top: 30px;
    margin-left: 0;
    margin-right: 0;
  }
  .subscribe-content form button {
    font-size: 13px;
    margin-top: 15px;
  }
  .page-title-area {
    padding-top: 60px;
    padding-bottom: 50px;
  }
  .page-title-content h2 {
    font-size: 30px;
  }
  .page-title-content p {
    margin-top: 10px;
  }
  .login-image {
    height: auto;
    background-image: unset !important;
  }
  .login-image img {
    display: inline-block;
  }
  .login-content {
    height: auto;
    padding: 60px 15px;
  }
  .login-content .login-form {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .login-content .login-form .logo {
    margin-bottom: 25px;
  }
  .login-content .login-form h3 {
    font-size: 24px;
  }
  .login-content .login-form form .form-control {
    height: 50px;
    font-size: 13px;
  }
  .signup-image {
    height: auto;
    background-image: unset !important;
  }
  .signup-image img {
    display: inline-block;
  }
  .signup-content {
    height: auto;
    padding: 60px 15px;
  }
  .signup-content .signup-form {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .signup-content .signup-form .logo {
    margin-bottom: 25px;
  }
  .signup-content .signup-form h3 {
    font-size: 24px;
  }
  .signup-content .signup-form form .form-control {
    height: 50px;
    font-size: 13px;
  }
  .error-area {
    height: 100vh;
    padding-bottom: 120px;
  }
  .error-content {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .error-content h3 {
    font-size: 25px;
    margin-top: 30px;
    margin-bottom: 10px;
  }
  .pagination-area {
    margin-top: 25px;
  }
  .pagination-area .page-numbers {
    width: 40px;
    height: 40px;
    margin: 0 2px;
    line-height: 41px;
    font-size: 18px;
  }
  .widget-area {
    margin-top: 40px;
  }
  .widget-area .widget .widget-title {
    font-size: 18px;
  }
  .widget-area .widget_spacle_posts_thumb .item .info time {
    margin-top: 0;
  }
  .widget-area .widget_spacle_posts_thumb .item .title {
    font-size: 14px;
  }
  .widget-area .widget_categories ul li {
    font-size: 14px;
  }
  .widget-area .widget_archive ul li {
    font-size: 14px;
  }
  .widget-area .widget_meta ul li {
    font-size: 14px;
  }
  .widget-area .tagcloud a {
    font-size: 13px !important;
  }
  .contact-inner {
    padding: 25px 20px;
  }
  .contact-features-list {
    padding-right: 0;
    border-right: none;
    text-align: center;
  }
  .contact-features-list h3 {
    font-size: 18px;
    line-height: 1.3;
  }
  .contact-features-list ul {
    text-align: left;
  }
  .contact-form {
    text-align: center;
    margin-top: 30px;
  }
  .contact-form h3 {
    margin-bottom: 20px;
    font-size: 20px;
  }
  .contact-form form .form-group {
    margin-bottom: 13px;
  }
  .contact-form form .form-control {
    height: 45px;
    font-size: 13px;
    padding: 0 10px;
  }
  .contact-info {
    margin-top: 40px;
  }
  .contact-info .contact-info-content h3 {
    font-size: 18px;
    line-height: 1.3;
  }
  .contact-info .contact-info-content h2 {
    font-size: 24px;
  }
  .contact-info .contact-info-content h2 span {
    margin-bottom: 3px;
    font-size: 13px;
  }
  .contact-info .contact-info-content .social {
    margin-top: 20px;
  }
  .contact-info .contact-info-content .social li a {
    width: 35px;
    height: 35px;
    font-size: 18px;
  }
  .footer-area {
    padding-top: 60px;
  }
  .single-footer-widget h3 {
    margin-bottom: 20px;
    font-size: 18px;
  }
  .single-footer-widget .social li a {
    width: 32px;
    height: 32px;
    line-height: 36px;
    font-size: 20px;
  }
  .divider {
    display: none;
  }
  .copyright-area {
    margin-top: 30px;
  }
  .go-top {
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 36px;
  }
  .go-top.active {
    bottom: 10px;
  }
  .main-banner-one .banner-image {
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .main-banner-one .banner-image .mbanner-img {
    display: none;
  }
  .single-features-card h3 {
    font-size: 20px;
  }
  .feature-box {
    padding: 12px 18px;
  }
  .services-content .content.left-content {
    padding-right: 0;
    padding-left: 0;
  }
  .funfact-style-two i {
    font-size: 25px;
    width: 60px;
    height: 60px;
    line-height: 60px;
  }
  .features-box-one h3 {
    font-size: 20px;
  }
  .banner-image.mbanner-bg-one .animate-banner-image {
    margin-top: 0;
  }
  .main-banner-two .banner-image-slider .banner-image {
    padding-top: 0;
    height: 400px;
  }
  .main-banner-two .banner-image-slider .banner-image img {
    display: block;
  }
  .banner-img.banner-video {
    height: 400px;
    padding-top: 0;
  }
  .single-pricing-table.left-align .pricing-header, .single-pricing-table.left-align .price {
    padding-left: 25px;
    padding-right: 25px;
  }
  .single-pricing-table.left-align .pricing-features {
    margin-left: 25px;
    margin-right: 25px;
  }
  .banner-content-slides {
    padding-right: 0;
  }
  /* SaaS home page */
  .saas-banner {
    padding-bottom: 60px;
    height: 100%;
    text-align: center;
  }
  .saas-banner .saas-image.mt-70 {
    margin-top: 0;
  }
  .saas-banner .saas-image img {
    display: none;
  }
  .saas-banner .saas-image img:last-child {
    display: block;
    position: relative;
    margin: auto;
  }
  .saas-banner .hero-content h1 {
    font-size: 30px;
    line-height: 1.3;
  }
  .saas-banner .hero-content p {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 25px;
  }
  .saas-banner .hero-content .banner-btn {
    max-width: 330px;
    margin: auto;
  }
  .saas-banner .hero-content .video-btn {
    margin-left: 10px;
    font-size: 14px;
  }
  .saas-banner .hero-content .video-btn i {
    font-size: 17px;
    height: 30px;
    width: 30px;
    line-height: 30px;
    padding-left: 4px;
    margin-right: 5px;
  }
  .saas-banner .hero-content.pl-4 {
    padding: 0 !important;
    margin-top: 30px;
  }
  .shape-rotate {
    display: none;
  }
  .features-inner-content .features-item {
    padding-left: 70px;
  }
  .features-inner-content .features-item h3 {
    font-size: 20px;
  }
  .features-inner-content .features-item i {
    width: 55px;
    height: 55px;
    line-height: 55px;
    font-size: 25px;
    border-radius: 100% !important;
  }
  .overview-left-img {
    margin-bottom: 30px;
  }
  .overview-right-img {
    margin-top: 30px;
  }
  .overview-item {
    margin-bottom: 30px;
    padding-bottom: 30px;
  }
  .overview-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
  }
  /* End SaaS home page */
  /* Service details */
  .service-details-content h1, .service-details-content h2, .service-details-content h3, .service-details-content h4 {
    font-size: 20px;
  }
  .service-details-content .service-details-info .single-info-box {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .service-details-content .service-details-info .single-info-box:last-child {
    margin-bottom: 0;
  }
  /* End Service details */
  .section-title.text-left {
    text-align: center !important;
    max-width: 100%;
  }
  .digital-agency-banner {
    position: relative;
    z-index: 1;
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .digital-agency-banner::before {
    content: '';
    background-color: #000000;
    z-index: -1;
    position: absolute;
    opacity: .40;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .digital-agency-banner .container {
    max-width: 100%;
  }
  .digital-agency-banner-content {
    text-align: center;
    max-width: 100%;
  }
  .digital-agency-banner-content h1 {
    font-size: 30px;
    margin-bottom: 20px;
  }
  .digital-agency-banner-content .default-btn {
    margin-top: 10px;
  }
  .single-featured-box h3 {
    font-size: 18px;
  }
  .about-area .section-title.text-left {
    margin-bottom: 40px;
  }
  .about-area .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
  .about-inner-area {
    padding-top: 0;
    padding-bottom: 0;
  }
  .about-inner-image {
    height: auto;
    background-image: unset !important;
  }
  .about-inner-image img {
    display: inline-block;
  }
  .about-inner-content {
    padding: 30px 25px;
    margin-top: 0;
    margin-bottom: 0;
  }
  .about-inner-content .content {
    max-width: 100%;
    text-align: center;
  }
  .about-inner-content .content h2 {
    font-size: 20px;
  }
  .about-inner-content .content .features-list {
    margin-top: 20px;
  }
  .about-inner-content .content .features-list li {
    font-size: 15px;
  }
  .about-inner-content .content .features-list li i {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 23px;
    top: 0;
    display: block;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 12px;
  }
  .single-services-box .content {
    padding: 25px 20px;
  }
  .single-services-box .content h3 {
    font-size: 18px;
    margin-right: 0;
    margin-bottom: 9px;
  }
  .single-services-box .row .col-lg-6:nth-child(1) {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
  .single-services-box .row .col-lg-6:nth-child(2) {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
  .single-services-box .image {
    height: auto;
    -webkit-clip-path: unset !important;
    clip-path: unset !important;
    background-image: unset !important;
  }
  .single-services-box .image img {
    display: inline-block;
  }
  .case-studies-area .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
  .case-studies-slides.owl-theme {
    left: 0;
  }
  .case-studies-slides.owl-theme .owl-nav {
    position: relative;
    right: 0;
    top: 0;
    margin-bottom: 30px;
  }
  .case-studies-slides.owl-theme .owl-nav [class*=owl-] {
    font-size: 25px;
    width: 35px;
    height: 35px;
    line-height: 35px;
    margin-left: 4px !important;
    margin-right: 4px !important;
  }
  .single-case-studies-item .content {
    padding: 30px 20px 20px;
  }
  .single-case-studies-item .content h3 {
    font-size: 18px;
  }
  .feedback-area .section-title.text-left {
    margin-bottom: 40px;
  }
  .single-testimonials-box {
    text-align: center;
  }
  .single-testimonials-box p {
    font-size: 14px;
  }
  .single-testimonials-box .client-info {
    margin-top: 20px;
    text-align: left;
  }
  .single-testimonials-box .client-info .d-flex {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .single-testimonials-box .client-info .title h3 {
    font-size: 16px;
  }
  .testimonials-slides-two.owl-theme .owl-nav.disabled + .owl-dots {
    text-align: center;
  }
  .team-area-two .section-title {
    margin-bottom: 40px;
  }
  .single-team-member .content {
    padding: 30px 15px 15px;
  }
  .single-team-member .content h3 {
    font-size: 18px;
  }
  .single-team-member .image .social-link li a {
    -webkit-transform: scale(1) !important;
            transform: scale(1) !important;
  }
  .col-lg-3:nth-child(1) .single-team-member {
    margin-top: 0;
  }
  .col-lg-3:nth-child(2) .single-team-member {
    margin-top: 0;
  }
  .col-lg-3:nth-child(3) .single-team-member {
    margin-top: 0;
  }
  .single-blog-post-item .post-content .category {
    margin-bottom: 8px;
    font-size: 13px;
  }
  .single-blog-post-item .post-content h3 {
    font-size: 18px;
  }
  .blog-slides.owl-theme .owl-nav {
    position: relative;
    right: 0;
    top: 0;
    margin-bottom: 30px;
  }
  .blog-slides.owl-theme .owl-nav [class*=owl-] {
    font-size: 25px;
    width: 35px;
    height: 35px;
    line-height: 35px;
    margin-left: 4px !important;
    margin-right: 4px !important;
  }
  .company-preview-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .company-preview-video .video-btn {
    width: 70px;
    height: 70px;
    font-size: 60px;
  }
  .lets-talk-content {
    text-align: center;
  }
  .lets-talk-content h2 {
    margin-bottom: 12px;
    font-size: 24px;
  }
  .lets-talk-btn {
    text-align: center;
    margin-top: 18px;
  }
  .banner_images{
    height: 300px;
    max-width: 300px;
    margin: 0 auto;
  }
}

/* Min width 576px to Max width 767px */
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .navbar-area .container {
    max-width: 540px;
  }
  .navbar-area.navbar-style-two .container {
    max-width: 540px;
  }
  .main-banner-content .content {
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  .banner-content .content {
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  .chat-wrapper {
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  .partner-area .container {
    max-width: 540px;
  }
  .single-feedback-item {
    margin-top: 130px;
  }
  .single-feedback-item img {
    display: inline-block !important;
  }
  .single-feedback-item .feedback-desc {
    padding: 25px 25px 25px 160px;
    margin-left: 45px;
  }
  .feedback-slides.owl-theme .owl-stage-outer {
    padding-top: 0;
    padding-left: 25px;
    padding-right: 25px;
    margin-left: -25px;
    margin-right: -25px;
    margin-top: 0;
  }
  .faq-area .container-fluid {
    max-width: 540px;
  }
  .single-clients-logo {
    -ms-flex: 0 0 33.3333333333%;
    -webkit-box-flex: 0;
            flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .app-download-content .btn-box {
    margin-top: 20px;
  }
  .app-download-content .btn-box .apple-store-btn {
    margin-right: 2px;
    display: inline-block;
    width: auto;
  }
  .app-download-content .btn-box .play-store-btn {
    margin-left: 2px;
    display: inline-block;
    width: auto;
  }
  .login-content .login-form {
    max-width: 410px;
    margin-left: auto;
    margin-right: auto;
  }
  .signup-content .signup-form {
    max-width: 410px;
    margin-left: auto;
    margin-right: auto;
  }
  .prev-link-wrapper {
    -ms-flex: 0 0 50%;
    -webkit-box-flex: 0;
            flex: 0 0 50%;
    max-width: 50%;
    padding-right: 10px;
  }
  .next-link-wrapper {
    margin-top: 0;
    -ms-flex: 0 0 50%;
    -webkit-box-flex: 0;
            flex: 0 0 50%;
    max-width: 50%;
    padding-left: 10px;
  }
  .contact-inner {
    padding: 35px 30px;
  }
  /* Service details */
  .service-details-content .service-details-info .single-info-box {
    -ms-flex: 0 0 50%;
    -webkit-box-flex: 0;
            flex: 0 0 50%;
    max-width: 50%;
    margin-bottom: 30px;
  }
  .service-details-content .service-details-info .single-info-box:last-child {
    margin-bottom: 0;
  }
  /* End Service details */
  .digital-agency-banner .container {
    max-width: 540px;
  }
  .about-area .container-fluid {
    max-width: 540px;
  }
  .banner_images{
    height: 400px;
    max-width: 400px;
    margin: 0 auto;
  }

}

/* Min width 768px to Max width 991px */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  body {
    font-size: 14px;
  }
  p {
    font-size: 14px;
  }
  .ptb-100 {
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .pt-100 {
    padding-top: 80px;
  }
  .pb-100 {
    padding-bottom: 80px;
  }
  .pt-70 {
    padding-top: 50px;
  }
  .pb-70 {
    padding-bottom: 50px;
  }
  .section-title {
    margin-bottom: 45px;
  }
  .section-title h2 {
    font-size: 30px;
  }
  .default-btn {
    font-size: 14px;
  }
  .navbar-area .container {
    max-width: 720px;
  }
  .navbar-area.navbar-style-two .container {
    max-width: 720px;
  }
  .main-banner-content {
    height: auto;
    padding-top: 60px;
  }
  .main-banner-content .content {
    max-width: 620px;
    text-align: center;
    padding-right: 0;
    margin-left: auto;
    margin-top: 0;
    margin-right: auto;
  }
  .main-banner-content .content h1 {
    font-size: 40px;
  }
  .main-banner-content .content p {
    margin-top: 15px;
    max-width: 100%;
  }
  .main-banner-content .content .default-btn {
    margin-top: 10px;
  }
  .main-banner-content .content .optional-btn {
    margin-top: 10px;
  }
  .banner-content {
    padding-top: 80px;
    padding-bottom: 0;
  }
  .banner-content .content {
    max-width: 620px;
    text-align: center;
    padding-right: 0;
    margin-left: auto;
    margin-right: auto;
  }
  .banner-content .content h1 {
    font-size: 40px;
    font-weight: 500;
  }
  .banner-content .content p {
    margin-top: 15px;
    max-width: 100%;
  }
  .banner-content .content .default-btn {
    margin-top: 10px;
  }
  .banner-image {
    margin-top: 60px;
    height: auto;
    border-radius: 0;
    background-image: unset !important;
    padding-top: 50px;
  }
  .banner-image img {
    display: inline-block;
    width: 100%;
  }
  .banner-img {
    margin-top: 60px;
    height: auto;
    border-radius: 0;
    background-image: unset !important;
    padding-top: 50px;
  }
  .banner-img img {
    display: inline-block;
    width: 100%;
  }
  .banner-content-slides.owl-theme .owl-nav {
    text-align: center;
    margin-top: 35px;
  }
  .banner-content-slides.owl-theme .owl-nav [class*=owl-] {
    font-size: 22px;
    width: 40px;
    height: 40px;
    line-height: 45px;
  }
  .shape1, .shape2, .shape3, .shape4, .shape5, .shape6, .shape7, .shape8, .shape9, .shape10, .shape11, .shape12, .shape13, .shape20, .shape21, .shape22, .shape23, .shape24, .shape26, .shape14, .shape15, .shape16, .shape17, .shape18 {
    display: none;
  }
  .chat-wrapper {
    position: relative;
    left: 0;
    top: 0;
    -webkit-transform: unset;
            transform: unset;
    width: auto;
    height: auto;
    max-width: 720px;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: -35px;
  }
  .partner-area .container {
    max-width: 720px;
  }
  .partner-title {
    text-align: center;
    margin-bottom: 45px;
  }
  .partner-title h3 {
    font-size: 20px;
  }
  .services-area.bg-right-color::before {
    display: none;
  }
  .services-area.bg-left-color::before {
    display: none;
  }
  .services-area .container-fluid {
    max-width: 720px;
  }
  .services-content {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
  
  .services-content .content {
    max-width: 650px;
    padding-left: 0;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }
  .services-content .content.left-content {
    padding-right: 0;
    margin-left: auto;
  }
  .services-content .content h2 {
    font-size: 30px;
  }
  .services-content .content p {
    max-width: 100%;
    margin-top: 15px;
  }
  .services-content .content .default-btn {
    margin-top: 10px;
  }
  .services-image {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
    margin-top: 35px;
  }
  .about-content {
    text-align: center;
  }
  .about-content h2 {
    font-size: 30px;
  }
  .about-image {
    margin-top: 30px;
  }
  .video-box {
    max-width: 660px;
  }
  .funfacts-inner {
    max-width: 720px;
    margin-top: 80px;
  }
  .contact-cta-box {
    max-width: 720px;
    padding: 30px 200px 30px 30px;
    margin-top: 30px;
  }
  .contact-cta-box h3 {
    font-size: 20px;
  }
  .contact-cta-box .default-btn {
    right: 30px;
  }
  .single-features-box h3 {
    font-size: 20px;
  }
  .features-box {
    padding: 25px;
  }
  .features-box h3 {
    font-size: 20px;
  }
  .features-box .icon {
    width: 80px;
    height: 80px;
    font-size: 40px;
  }
  .single-feedback-item {
    margin-top: 125px;
  }
  .single-feedback-item .feedback-desc .client-info h3 {
    font-size: 17px;
  }
  .single-feedback-item .feedback-desc .client-info span {
    font-size: 13px;
  }
  .single-testimonials-item .client-info {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 15px;
  }
  .single-testimonials-item .client-info h3 {
    font-size: 17px;
  }
  .single-testimonials-item .client-info span {
    font-size: 13px;
  }
  .single-testimonials-item .testimonials-desc {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
            flex: 0 0 100%;
    max-width: 100%;
    padding-left: 0;
    text-align: center;
  }
  .single-pricing-table .pricing-header h3 {
    font-size: 20px;
  }
  .single-pricing-table .price {
    font-size: 30px;
  }
  .single-pricing-table .price sup {
    top: -13px;
    font-size: 16px;
  }
  .faq-area .container-fluid {
    max-width: 720px;
  }
  .faq-accordion {
    margin-left: 0;
    max-width: 100%;
    text-align: center;
  }
  .faq-accordion h2 {
    font-size: 30px;
  }
  .faq-accordion .accordion {
    text-align: left;
  }
  .faq-accordion .accordion .accordion-title {
    font-size: 15px;
  }
  .faq-image {
    margin-top: 30px;
  }
  .single-clients-logo a {
    padding: 20px 15px;
  }
  .free-trial-content {
    max-width: 520px;
  }
  .free-trial-content h2 {
    font-size: 30px;
  }
  .app-download-content {
    text-align: center;
    max-width: 615px;
    margin-top: 30px;
    margin-left: auto;
    margin-right: auto;
  }
  .app-download-content h2 {
    font-size: 30px;
  }
  .app-download-content .btn-box {
    margin-top: 20px;
  }
  .app-download-content .btn-box .apple-store-btn {
    text-align: left;
  }
  .app-download-content .btn-box .apple-store-btn span {
    font-size: 18px;
  }
  .app-download-content .btn-box .play-store-btn {
    text-align: left;
  }
  .app-download-content .btn-box .play-store-btn span {
    font-size: 18px;
  }
  .subscribe-content {
    padding: 45px;
  }
  .subscribe-content h2 {
    max-width: 505px;
    font-size: 30px;
  }
  .subscribe-content form {
    max-width: 540px;
  }
  .single-blog-post .post-content h3 {
    font-size: 20px;
  }
  .single-blog-post .post-content .post-info .post-by {
    -ms-flex: 0 0 60%;
    -webkit-box-flex: 0;
            flex: 0 0 60%;
    max-width: 60%;
  }
  .single-blog-post .post-content .post-info .details-btn {
    -ms-flex: 0 0 40%;
    -webkit-box-flex: 0;
            flex: 0 0 40%;
    max-width: 40%;
  }
  .page-title-area {
    padding-top: 80px;
    padding-bottom: 70px;
  }
  .page-title-content h2 {
    font-size: 30px;
  }
  .page-title-content p {
    margin-top: 10px;
  }
  .login-image {
    height: auto;
    background-image: unset !important;
  }
  .login-image img {
    display: inline-block;
  }
  .login-content {
    height: auto;
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .login-content .login-form .logo {
    margin-bottom: 30px;
  }
  .login-content .login-form h3 {
    font-size: 30px;
  }
  .signup-image {
    height: auto;
    background-image: unset !important;
  }
  .signup-image img {
    display: inline-block;
  }
  .signup-content {
    height: auto;
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .signup-content .signup-form .logo {
    margin-bottom: 30px;
  }
  .signup-content .signup-form h3 {
    font-size: 30px;
  }
  .widget-area {
    margin-top: 40px;
  }
  .widget-area .widget .widget-title {
    font-size: 20px;
  }
  .blog-details-desc .article-content .entry-meta ul li::before {
    top: 10px;
  }
  .blog-details-desc .article-content h1 {
    font-size: 20px;
  }
  .blog-details-desc .article-content h1 {
    font-size: 17px;
  }
  blockquote p, .blockquote p {
    font-size: 20px !important;
  }
  .prev-link-wrapper .prev-title {
    font-size: 16px;
  }
  .next-link-wrapper .next-title {
    font-size: 16px;
  }
  .comments-area .comments-title {
    font-size: 20px;
  }
  .comments-area .comment-author {
    font-size: 15px;
  }
  .comments-area .comment-respond .comment-reply-title {
    font-size: 20px;
  }
  .comments-area .comment-respond .form-submit input {
    padding: 10px 25px;
  }
  .contact-features-list h3 {
    font-size: 20px;
  }
  .contact-form {
    margin-top: 30px;
  }
  .contact-form h3 {
    margin-bottom: 20px;
    font-size: 25px;
  }
  .contact-info .contact-info-content h3 {
    font-size: 20px;
  }
  .contact-info .contact-info-content h2 {
    font-size: 30px;
  }
  .contact-info .contact-info-content h2 span {
    font-size: 14px;
  }
  .single-footer-widget h3 {
    margin-bottom: 28px;
    font-size: 20px;
  }
  .divider {
    height: 80px;
    top: -80px;
  }
  .main-banner-one .banner-image {
    padding-top: 40px;
    padding-bottom: 40px;
    padding-left: 40px;
    padding-right: 40px;
  }
  .main-banner-one .banner-image .mbanner-img {
    display: none;
  }
  .banner-image.mbanner-bg-one .animate-banner-image {
    margin-top: 0;
  }
  .main-banner-two .banner-image-slider .banner-image {
    padding-top: 0;
    height: 450px;
  }
  .main-banner-two .banner-image-slider .banner-image img {
    display: block;
  }
  .single-pricing-table.center-align .pricing-features {
    margin-left: 35px;
    margin-right: 35px;
  }
  .banner-img.banner-video {
    height: 600px;
    padding-top: 0;
  }
  .banner-content-slides {
    padding-right: 0;
  }
  /* SaaS home page */
  .saas-banner {
    padding-bottom: 60px;
    height: 100%;
    text-align: center;
  }
  .saas-banner .saas-image.mt-70 {
    margin-top: 0;
  }
  .saas-banner .saas-image img {
    display: none;
  }
  .saas-banner .saas-image img:last-child {
    display: block;
    position: relative;
    margin: auto;
  }
  .saas-banner .hero-content h1 {
    font-size: 30px;
    line-height: 1.3;
  }
  .saas-banner .hero-content p {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 25px;
  }
  .saas-banner .hero-content .banner-btn {
    max-width: 330px;
    margin: auto;
  }
  .saas-banner .hero-content .video-btn {
    margin-left: 10px;
    font-size: 14px;
  }
  .saas-banner .hero-content .video-btn i {
    font-size: 17px;
    height: 30px;
    width: 30px;
    line-height: 30px;
    padding-left: 4px;
    margin-right: 5px;
  }
  .saas-banner .hero-content.pl-4 {
    padding: 0 !important;
    margin-top: 30px;
  }
  .shape-rotate {
    display: none;
  }
  .overview-left-img {
    margin-bottom: 30px;
  }
  .overview-right-img {
    margin-top: 30px;
  }
  .overview-item {
    margin-bottom: 30px;
    padding-bottom: 30px;
  }
  .overview-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
  }
  .overview-content.pl-3 {
    padding-left: 0 !important;
  }
  /* End SaaS home page */
  /* Service details */
  .service-details-content .service-details-info {
    padding: 30px 20px 0;
  }
  .service-details-content .service-details-info .single-info-box {
    -ms-flex: 0 0 33%;
    -webkit-box-flex: 0;
            flex: 0 0 33%;
    max-width: 33%;
    margin-bottom: 30px;
  }
  /* End Service details */
  .section-title.text-left {
    text-align: center !important;
    margin-left: auto;
    margin-right: auto;
  }
  .digital-agency-banner {
    padding-top: 150px;
    padding-bottom: 150px;
  }
  .digital-agency-banner .container {
    max-width: 720px;
  }
  .digital-agency-banner-content h1 {
    margin-bottom: 20px;
    font-size: 45px;
  }
  .digital-agency-banner-content .default-btn {
    margin-top: 10px;
  }
  .single-featured-box h3 {
    font-size: 20px;
  }
  .about-area .section-title.text-left {
    margin-bottom: 45px;
  }
  .about-area .container-fluid {
    max-width: 720px;
  }
  .about-inner-area {
    padding-top: 0;
    padding-bottom: 0;
  }
  .about-inner-image {
    height: auto;
    background-image: unset !important;
  }
  .about-inner-image img {
    display: inline-block;
  }
  .about-inner-content {
    padding: 40px;
    margin-top: 0;
    margin-bottom: 0;
  }
  .about-inner-content .content {
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  .about-inner-content .content h2 {
    font-size: 26px;
  }
  .about-inner-content .content .features-list {
    margin-top: 20px;
  }
  .about-inner-content .content .features-list li {
    font-size: 15.5px;
  }
  .about-inner-content .content .features-list li i {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 23px;
    top: 2.5px;
  }
  .single-services-box .content {
    padding: 25px 20px;
  }
  .single-services-box .content h3 {
    font-size: 20px;
    margin-right: 0;
  }
  .single-services-box .row .col-lg-6:nth-child(1) {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
  .single-services-box .row .col-lg-6:nth-child(2) {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
  .single-services-box .image {
    height: auto;
    -webkit-clip-path: unset !important;
    clip-path: unset !important;
    background-image: unset !important;
  }
  .single-services-box .image img {
    display: inline-block;
  }
  .case-studies-area .container-fluid {
    max-width: 720px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .case-studies-slides.owl-theme {
    left: 0;
  }
  .case-studies-slides.owl-theme .owl-nav {
    position: relative;
    right: 0;
    top: 0;
    margin-bottom: 30px;
    margin-top: 10px;
  }
  .case-studies-slides.owl-theme .owl-nav [class*=owl-] {
    margin-left: 5px !important;
    margin-right: 5px !important;
  }
  .single-case-studies-item .content {
    padding: 30px 20px 25px;
  }
  .single-case-studies-item .content h3 {
    font-size: 20px;
  }
  .feedback-area .section-title.text-left {
    margin-bottom: 45px;
  }
  .testimonials-slides-two {
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  .team-area-two .section-title {
    margin-bottom: 45px;
  }
  .single-team-member .content h3 {
    font-size: 20px;
  }
  .col-lg-3:nth-child(1) .single-team-member {
    margin-top: 0;
  }
  .col-lg-3:nth-child(2) .single-team-member {
    margin-top: 0;
  }
  .col-lg-3:nth-child(3) .single-team-member {
    margin-top: 0;
  }
  .single-blog-post-item .post-content h3 {
    font-size: 20px;
  }
  .blog-slides.owl-theme .owl-nav {
    position: relative;
    right: 0;
    top: 0;
    margin-bottom: 30px;
    margin-top: 10px;
  }
  .blog-slides.owl-theme .owl-nav [class*=owl-] {
    margin-left: 5px !important;
    margin-right: 5px !important;
  }
  .company-preview-area {
    padding-top: 150px;
    padding-bottom: 150px;
  }
  .company-preview-video .video-btn {
    width: 90px;
    height: 90px;
    font-size: 80px;
  }
  .lets-talk-content {
    text-align: center;
  }
  .lets-talk-content h2 {
    font-size: 30px;
  }
  .lets-talk-btn {
    text-align: center;
    margin-top: 20px;
  }
  .banner_images{
    height: 500px;
    max-width: 500px;
    margin: 0 auto;
  }
}

/* Min width 992px to Max width 1199px */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title h2 {
    font-size: 34px;
  }
  .navbar-area {
    position: relative;
    background-color: #ffffff;
  }
  .navbar-area .container {
    max-width: 960px;
  }
  .navbar-area.navbar-style-two .container {
    max-width: 960px;
  }
  .spacle-nav .navbar .navbar-nav {
    margin-left: auto;
  }
  .spacle-nav .navbar .others-options {
    margin-left: 25px;
  }
  .spacle-nav .navbar .others-options .optional-btn {
    display: none;
  }
  .main-banner-content .content {
    max-width: 550px;
    padding-right: 80px;
    margin-top: 0;
  }
  .main-banner-content .content h1 {
    font-size: 42px;
  }
  .banner-content {
    padding-top: 145px;
    padding-bottom: 130px;
  }
  .banner-content .content {
    max-width: 550px;
    padding-right: 60px;
  }
  .banner-content .content h1 {
    font-size: 42px;
  }
  .banner-image {
    height: 770px;
  }
  .chat-wrapper {
    width: 460px;
  }
  .partner-area .container {
    max-width: 960px;
  }
  .services-content .content {
    max-width: 485px;
    padding-left: 15px;
  }
  .services-content .content .icon {
    margin-bottom: 18px;
  }
  .services-content .content h2 {
    font-size: 33px;
  }
  .services-content .content .default-btn {
    margin-top: 5px;
  }
  .services-content .content.left-content {
    padding-right: 15px;
  }
  .features-box {
    padding: 30px 19px;
  }
  .features-box h3 {
    font-size: 20px;
  }
  .faq-accordion {
    max-width: 570px;
  }
  .single-blog-post .post-content .post-info .post-by {
    -ms-flex: 0 0 65%;
    -webkit-box-flex: 0;
            flex: 0 0 65%;
    max-width: 65%;
  }
  .single-blog-post .post-content .post-info .details-btn {
    -ms-flex: 0 0 35%;
    -webkit-box-flex: 0;
            flex: 0 0 35%;
    max-width: 35%;
  }
  .shape10, .shape11, .shape12, .shape13, .shape21, .shape22 {
    display: none;
  }
  .single-feedback-item {
    margin-top: 40px;
  }
  .app-download-content h2 {
    font-size: 34px;
  }
  .free-trial-content h2 {
    font-size: 34px;
  }
  .about-content h2 {
    font-size: 34px;
  }
  .faq-accordion h2 {
    font-size: 34px;
  }
  .contact-form h3 {
    font-size: 30px;
  }
  .contact-info .contact-info-content h2 {
    font-size: 34px;
  }
  .login-content .login-form {
    max-width: 450px;
  }
  .signup-content .signup-form {
    max-width: 450px;
  }
  .single-features-card {
    padding: 20px;
  }
  .single-features-card h3 {
    font-size: 18px;
  }
  .features-box-one h3 {
    font-size: 20px;
  }
  .single-blog-post .post-content h3 {
    font-size: 18px;
  }
  .banner-image.mbanner-bg-one .animate-banner-image {
    margin-top: 0;
  }
  .single-pricing-table.center-align .pricing-features {
    margin-left: 30px;
    margin-right: 30px;
  }
  .services-area.bg-right-shape::before {
    width: 67%;
  }
  .services-area.bg-left-shape::before {
    width: 63%;
  }
  .chatbot-main-banner .main-banner-content .content {
    padding-right: 115px;
  }
  /* SaaS Home */
  .saas-banner {
    height: 750px;
  }
  .saas-banner .row.align-items-center.pt-5 {
    padding: 0 !important;
  }
  .saas-banner .hero-content h1 {
    font-size: 40px;
    line-height: 1.3;
  }
  .saas-banner .hero-content p {
    font-size: 17px;
    margin-top: 20px;
    margin-bottom: 25px;
  }
  /* End SaaS Home */
  /* Service details */
  .service-details-content .service-details-info .single-info-box .social li {
    display: inline-block;
    margin-right: 2px;
  }
  /* End Service details */
  .navbar-area.p-relative .spacle-nav .container {
    max-width: 960px;
  }
  .digital-agency-banner {
    padding-top: 200px;
    padding-bottom: 200px;
  }
  .digital-agency-banner .container {
    max-width: 960px;
  }
  .digital-agency-banner-content h1 {
    font-size: 50px;
  }
  .about-inner-content {
    padding: 40px;
  }
  .about-inner-content .content {
    max-width: 100%;
  }
  .about-inner-content .content h2 {
    font-size: 30px;
  }
  .about-inner-content .content .features-list li {
    font-size: 16px;
  }
  .about-inner-content .content .features-list li i {
    width: 30px;
    height: 30px;
    line-height: 31px;
    font-size: 25px;
    top: 4px;
  }
  .single-featured-box h3 {
    font-size: 21px;
  }
  .single-services-box .content h3 {
    font-size: 20px;
  }
  .case-studies-area .container-fluid {
    max-width: 960px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .case-studies-slides.owl-theme {
    left: 0;
  }
  .case-studies-slides.owl-theme .owl-nav {
    right: 0;
  }
  .single-case-studies-item .content {
    padding: 30px 15px 20px;
  }
  .single-case-studies-item .content h3 {
    font-size: 20px;
  }
  .single-team-member .content {
    padding: 30px 18px 15px;
  }
  .single-team-member .content h3 {
    font-size: 20px;
  }
  .single-blog-post-item .post-content h3 {
    font-size: 20px;
  }
  .company-preview-area {
    padding-top: 200px;
    padding-bottom: 200px;
  }
  .lets-talk-content h2 {
    font-size: 34px;
  }

  .banner_images{
    height: 450px;
    margin-top: 10px;
  }
}

/* Min width 1200px to Max width 1300px */
@media only screen and (min-width: 1200px) and (max-width: 1300px) {
  .navbar-area .container {
    max-width: 1240px;
  }
  .spacle-nav .navbar .navbar-nav {
    margin-left: 70px;
  }
  .main-banner-content .content {
    max-width: 675px;
    padding-right: 150px;
  }
  .main-banner-content .content h1 {
    font-size: 50px;
  }
  .banner-content .content {
    max-width: 675px;
    padding-right: 150px;
  }
  .banner-content .content h1 {
    font-size: 50px;
  }
  .shape21 {
    display: none;
  }
  .partner-area .container {
    max-width: 1140px;
  }
}

/*Min width 991px*/
@media only screen and (max-width:991px){
  .integration-block{
    margin-top: unset;
  }
  .integration-block>.content{
    margin-left: unset;
  } 
  .single-clients-logo:nth-child(4) ,.single-clients-logo:nth-child(7) {
    margin-right: unset;
  }
  
}

/* Min width 1550px */
@media only screen and (min-width: 1550px) {
  .main-banner-content .content {
    max-width: 785px;
    padding-right: 170px;
  }
  .banner-content {
    padding-top: 290px;
    padding-bottom: 200px;
  }
  .banner-content .content {
    max-width: 785px;
  }
  .case-studies-slides.owl-theme .owl-nav {
    right: 41.7%;
  }
}

/* Min width 1800px */
@media only screen and (min-width: 1800px) {
  .services-area.bg-left-shape::before {
    width: 42%;
  }
  .services-area.bg-right-shape::before {
    width: 42%;
  }
  .saas-banner {
    height: 1000px;
  }
}
/*# sourceMappingURL=responsive.css.map */