<svg width="206" height="257" viewBox="0 0 206 257" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M98.0853 166.273L64.0853 213.273C60.0853 220.273 50.0853 218.273 49.0853 210.273L6.08532 11.2732C5.08532 3.2732 13.0853 -2.7268 19.0853 1.2732L193.085 106.273C200.085 110.273 199.085 120.273 191.085 122.273L134.085 139.273L186.085 209.273C189.085 213.273 188.085 218.273 185.085 221.273L160.085 239.273C156.085 242.273 151.085 241.273 148.085 237.273L98.0853 166.273Z" fill="white"/>
<path d="M101.356 163.97L98.1291 159.388L94.8444 163.929L60.8444 210.929L60.7187 211.102L60.6124 211.289C58.5195 214.951 53.5776 213.963 53.0544 209.777L53.0325 209.601L52.9951 209.428L10.0366 10.6207C9.78506 8.16761 10.8719 6.10736 12.3845 4.95493C13.9073 3.79467 15.5371 3.71512 16.8665 4.6014L16.9415 4.65139L17.0187 4.69795L191.019 109.698L191.059 109.723L191.101 109.746C193.184 110.937 193.932 112.875 193.737 114.565C193.548 116.203 192.457 117.807 190.115 118.393L190.028 118.414L189.942 118.44L132.942 135.44L127.468 137.073L130.874 141.658L182.874 211.658L182.88 211.666L182.885 211.673C184.442 213.748 184.113 216.413 182.452 218.24L157.748 236.027L157.716 236.05L157.685 236.073C155.659 237.593 153.14 237.31 151.318 234.916L101.356 163.97Z" stroke="black" stroke-width="8"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="205.766" height="256.018" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="8"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
