/**
 * Ed-admin Jobs Server Warm-up Script
 * 
 * This script silently warms up the jobs API server in the background
 * to ensure it's ready when users navigate to the careers section.
 * 
 * The server takes approximately 55 seconds to boot up from a cold start,
 * so this script initiates the warm-up process as soon as any page loads.
 */

(function() {
    'use strict';
    
    const JOBS_API_BASE = 'https://job-post-backend-vnvs.onrender.com/api';
    const WARMUP_TIMEOUT = 60000; // 60 seconds timeout
    let warmupAttempted = false;
    
    /**
     * Silently warm up the server by making a lightweight request
     */
    function warmUpServer() {
        // Prevent multiple warm-up attempts
        if (warmupAttempted) {
            return;
        }
        warmupAttempted = true;
        
        console.log('🔥 Warming up jobs server...');
        
        // Create a controller to handle timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), WARMUP_TIMEOUT);
        
        // Make a lightweight health check request
        fetch(`${JOBS_API_BASE}/health`, {
            method: 'GET',
            mode: 'cors',
            cache: 'no-cache',
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (response.ok) {
                console.log('🚀 Jobs server is now ready!');
                
                // Optionally pre-fetch jobs data to cache it
                return fetch(`${JOBS_API_BASE}/jobs`, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'default'
                });
            }
        })
        .then(response => {
            if (response && response.ok) {
                console.log('📋 Jobs data pre-cached successfully');
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                console.log('⏰ Server warm-up timed out (server may still be starting)');
            } else {
                console.log('🔄 Server warm-up initiated (may take up to 60 seconds)');
            }
        });
    }
    
    /**
     * Set up event listeners for career-related interactions
     */
    function setupCareerListeners() {
        // Warm up when user hovers over career-related links
        const careerSelectors = [
            'a[href*="join-our-team"]',
            'a[href*="career"]', 
            'a[href*="job"]',
            'a[href*="Job-apply"]',
            'a[href*="job-details"]'
        ];
        
        const careerLinks = document.querySelectorAll(careerSelectors.join(', '));
        
        careerLinks.forEach(link => {
            // Warm up on hover (user intent to navigate)
            link.addEventListener('mouseenter', warmUpServer, { once: true });
            
            // Also warm up on focus (keyboard navigation)
            link.addEventListener('focus', warmUpServer, { once: true });
        });
        
        // Warm up when user hovers over "About" dropdown (contains Join Our Team)
        const aboutDropdowns = document.querySelectorAll('a[href="#"]:has(+ ul a[href*="join-our-team"])');
        aboutDropdowns.forEach(dropdown => {
            dropdown.addEventListener('mouseenter', warmUpServer, { once: true });
        });
    }
    
    /**
     * Initialize the warm-up process
     */
    function init() {
        // Immediate warm-up on page load
        warmUpServer();
        
        // Set up listeners for user interactions
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupCareerListeners);
        } else {
            setupCareerListeners();
        }
        
        // Additional warm-up triggers
        
        // Warm up when user scrolls (indicates engagement)
        let scrollWarmed = false;
        window.addEventListener('scroll', function() {
            if (!scrollWarmed && window.scrollY > 100) {
                scrollWarmed = true;
                warmUpServer();
            }
        }, { once: true, passive: true });
        
        // Warm up after a delay (fallback)
        setTimeout(warmUpServer, 5000);
    }
    
    // Start the warm-up process
    init();
    
    // Expose a manual warm-up function for debugging
    window.edAdminWarmUp = warmUpServer;
    
})();
