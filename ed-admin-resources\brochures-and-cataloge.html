<!doctype html>
<html lang="zxx">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Read through our brochures and catalogues to learn about Ed-admin at your own pace">
    <meta name="keywords" content="Ed-admin brochures and catalogues">

    <!-- Bootstrap Min CSS -->
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <!-- Animate Min CSS -->
    <link rel="stylesheet" href="../assets/css/animate.min.css">
    <!-- BoxIcons Min CSS -->
    <link rel="stylesheet" href="../assets/css/boxicons.min.css">
    <!-- Owl Carousel Min CSS -->
    <link rel="stylesheet" href="../assets/css/owl.carousel.min.css">
    <!-- Odometer Min CSS -->
    <link rel="stylesheet" href="../assets/css/odometer.min.css">
    <!-- MeanMenu CSS -->
    <link rel="stylesheet" href="../assets/css/meanmenu.css">
    <!-- Magnific Popup Min CSS -->
    <link rel="stylesheet" href="../assets/css/magnific-popup.min.css">
    <!-- Style CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <style>
        /* Fullscreen modal dialog */
        .modal-fullscreen {
            width: 100vw;
            height: 100vh;
            margin: 0;
            max-width: 100%;
        }
        .modal-fullscreen .modal-content {
            height: 100%;
            border: 0;
            border-radius: 0;
        }
        /* When fullscreen, remove modal-body padding and let it fill the remaining space */
        .modal-fullscreen .modal-body {
            padding: 0;
            height: calc(100vh - 60px);
            overflow: hidden;
        }
        /* The iframe fills the modal body in fullscreen mode */
        .modal-fullscreen #pdfIframe {
            height: 100%;
        }
        /* Toast notification styling */
        #toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            padding: 10px 20px;
            border-radius: 5px;
            display: none;
            z-index: 9999;
            font-family: 'Montserrat', sans-serif;
        }
    </style>
        
    <title>Ed-admin: Brochures and Catalogues</title>

    <link rel="icon" type="image/png" href="../assets/img/favicon-Edadmin.ico">
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-B7QM9WG2P4');
    </script>
</head>

<body>
    <span data-menuid="Resources cataloge" class="d-none"></span>
    <!-- Start PopUps Area -->
    <div data-include="../popups/demonow"></div>
    <div data-include="../popups/bookdemo"></div>
    <div data-include="../popups/downloadnow"></div>
    <div data-include="../popups/freedemo"></div>
    <!-- End PopUps Area -->

    <!-- Start Header Area -->
    <div data-include="../common/header2"></div>
    <!-- End Header Area -->

    <!-- Start About Area -->
    <section class="about-area pt-70 background-image">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12 col-md-12">
                    <div class="about-content">
                        <h1 class="mb-3">Brochures and Catalogues</h1>
                        <h4>Read about Ed-admin at your own pace</h4>
                    </div>
                </div>
            </div>               
        </div>
    </section>    

    <!-- PDF Modal -->
    <div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
          <div class="modal-content">
            <div class="modal-header">
               <!-- Toggle button for full view / preview -->
               <button type="button" class="btn btn-link toggle-full-view">Full View</button>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                   <span aria-hidden="true">&times;</span>
               </button>
            </div>
            <div class="modal-body">
               <iframe id="pdfIframe" src="" frameborder="0" width="100%" height="600px"></iframe>
            </div>
          </div>
        </div>
    </div>
  
    <section class="about-area ptb-100">
        <div style="width: 100%; height: auto; display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 20px;">
            <div class="card-container-b">
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Introducing%20Ed-admin%20Catalogue%20.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-a frame-1"></div>
                            <div class="image-a frame-2"></div>
                            <div class="image-a frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <!-- The share icon will only trigger share/copy functionality -->
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Introducing Ed-admin Catalogue
                            </h3>
                        </div>
                    </div>
                </a>
        
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Multi-Portal%20App%20Brochure.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-b frame-1"></div>
                            <div class="image-b frame-2"></div>
                            <div class="image-b frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Multi-Portal App Brochure
                            </h3>
                        </div>
                    </div>
                </a>
        
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Learning%20Space%20Brochure.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-c frame-1"></div>
                            <div class="image-c frame-2"></div>
                            <div class="image-c frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Learning Space Brochure
                            </h3>
                        </div>
                    </div>
                </a>
            </div>
            <div class="circle-1"></div>

            <div class="card-container-b">
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Payment%20Gateway%20Integration%20Brochure.pdf" target="_blank">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-d frame-1"></div>
                            <div class="image-d frame-2"></div>
                            <div class="image-d frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Payment Gateway Integration Brochure
                            </h3>
                        </div>
                    </div>
                </a>
        
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Admission%20and%20Online%20Application%20Brochure.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-e frame-1"></div>
                            <div class="image-e frame-2"></div>
                            <div class="image-e frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Admission and Online Application Brochure
                            </h3>
                        </div>
                    </div>
                </a>
        
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Integration%20Brochure.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-f frame-1"></div>
                            <div class="image-f frame-2"></div>
                            <div class="image-f frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Integration Brochure
                            </h3>
                        </div>
                    </div>
                </a>
            </div>

            <div class="card-container-b">
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Corporate%20Version.pdf">
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-g frame-1"></div>
                            <div class="image-g frame-2"></div>
                            <div class="image-g frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Corporate Version Brochure
                            </h3>
                        </div>
                    </div>
                </a>
        
                <a href="https://s3.af-south-1.amazonaws.com/onlineguides/Updated%20Brochures/Ed-admin%20Google%20Integration%20Brochure.pdf"> 
                    <div class="card-1-b">
                        <div class="images-container">
                            <div class="image-h frame-1"></div>
                            <div class="image-h frame-2"></div>
                            <div class="image-h frame-3"></div>
                        </div>
                        <div>
                            <h3 class="subtitle-b">
                                <img class="share-icon" src="../assets/share.png" alt="share icon" style="margin-right: 8px; cursor: pointer; width: 25px; height: 25px;">
                                Ed-admin Google Integration Brochure
                            </h3>
                        </div>
                    </div>
                </a>
        
                <div class="no-show">
                    <!-- Additional content if needed -->
                </div>
            </div>

            <div class="circle-2"></div>
        </div>
        
        <style>
            .circle-1 {
                width: 330px;
                height: 330px;
                background-color: #FFB30033;
                border-radius: 50%;
                position: absolute;
                top: 40%;
                left: 70%;
                z-index: -1;
            }
            .no-show {
                width: 350px;
                height: 300px;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding-top: 10px;
            }
            @media (max-width: 1090px) {
                .no-show {
                    display: none;
                }
            }
            .circle-2 {
                width: 280px;
                height: 280px;
                gap: 0px;
                opacity: 0px;
                background-color: #00A1DE1A;
                border-radius: 50%;
                position: absolute;
                top: 900px;
                right: 70%;
                z-index: -1;
            }
            .card-container-b {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 20px;
                max-width: 100%;
                flex-wrap: wrap;
                border: 1px solid transparent;
            }
            @media (max-width: 1090px) {
                .card-container-b {
                    justify-content: space-evenly;
                }
            }
            .card-1-b {
                width: 350px;
                height: 300px;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding-top: 10px;
            }
            .subtitle-b {
                font-family: "Poppins", serif;
                font-weight: 500;
                font-style: normal;
                padding-top: 40px;
                padding-bottom: 20px;
                text-align: center;
                font-size: 20px;
            }
            .images-container {
                width: 75%;
                height: 150px;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .image-a,
            .image-b,
            .image-c, 
            .image-d, 
            .image-e,
            .image-f, 
            .image-g, 
            .image-h {
                width: 100%;
                height: 100%;
                background-position: center;
                background-size: cover;
                background-repeat: no-repeat;
                border-radius: 15px;
                position: absolute;
                transform-origin: top left;
                transition: transform 300ms ease-out;
                box-shadow: 4px 4px 4px rgba(0, 0, 0, 0.25);
                border: 1px solid #EAEAEA;
            }
            .images-container .image-a:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/introducing-image-3.png');
            }
            .images-container .image-a:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/introducing-image-2.png');
            }
            .images-container .image-a:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/introducing-image-1.png');
            }
            .images-container .image-b:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/emp-image-3.png');
            }
            .images-container .image-b:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/emp-image-2.png');
            }
            .images-container .image-b:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/emp-image-1.png');
            }
            .images-container .image-c:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/learning-image-3.png');
            }
            .images-container .image-c:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/learning-image-2.png');
            }
            .images-container .image-c:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/learning-image-1.png');
            }
            .images-container .image-d:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/payment-integration-image-3.png');
            }
            .images-container .image-d:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/payment-integration-image-2.png');
            }
            .images-container .image-d:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/payment-integration-image-1.png');
            }
            .images-container .image-e:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/admisson-image-3.png');
            }
            .images-container .image-e:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/admisson-image-2.png');
            }
            .images-container .image-e:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/admisson-image-1.png');
            }
            .images-container .image-f:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/Ed-admin-Integration-Brochure-3.png');
            }
            .images-container .image-f:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/Ed-admin-Integration-Brochure-2.png');
            }
            .images-container .image-f:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/Ed-admin-Integration-Brochure-1.png');
            }
            .images-container .image-g:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/corporate-version-image-3.png');
            }
            .images-container .image-g:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/corporate-version-image-2.png');
            }
            .images-container .image-g:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/corporate-version-image-1.png');
            }
            .images-container .image-h:nth-child(1) {
                background-image: url('../assets/img/brochure-and-catalogue/google-integration-brochure-3.png');
            }
            .images-container .image-h:nth-child(2) {
                background-image: url('../assets/img/brochure-and-catalogue/google-integration-brochure-2.png');
            }
            .images-container .image-h:nth-child(3) {
                background-image: url('../assets/img/brochure-and-catalogue/google-integration-brochure-1.png');
            }
            .frame-1 {
                top: 2px;
                left: 2px;
            }
            .frame-2 {
                top: 0;
                left: 0;
            }
            .frame-3 {
                top: -2px;
                left: -2px;
            }
            .images-container:hover .frame-2 {
                transform: rotate(-3deg) translateY(-2px);
            }
            .images-container:hover .frame-3 {
                transform: rotate(-6deg) translateY(-4px);
            }
        </style>
    </section>

    <style>
        .link-item-1 {
            color: #006EB3;
            transition: color 0.3s ease;
        }
        .link-item-1:hover {
            color: #080a3c;
            animation: shake 0.5s ease;
        }
    </style>
    <!-- End About Area -->

    <!-- Start Free Trial Area -->
    <section class="free-trial-area ptb-100 bg-f4f5fe">
        <div class="container">
            <div class="free-trial-content">
                <h2>Like to see Ed-admin in action? Ask for a demo!</h2>
                <a href="#" class="default-btn" style="padding-left: 25px;" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/ed-admin/free-demo'});return false;">
                    Free Demo <span></span>
                </a>
            </div>
        </div>
        <div class="shape10"><img src="../assets/img/shape/10.png" alt="image"></div>
        <div class="shape11"><img src="../assets/img/shape/7.png" alt="image"></div>
        <div class="shape12"><img src="../assets/img/shape/11.png" alt="image"></div>
        <div class="shape13"><img src="../assets/img/shape/12.png" alt="image"></div>
    </section>
    <!-- End Free Trial Area -->

    <!-- Start Footer Area -->
    <div data-include="../common/footer"></div>
    <!-- End Footer Area -->

    <div class="go-top"><i class='bx bx-chevron-up'></i></div>

    <!-- Toast Notification -->
    <div id="toast"></div>

    <script src="/assets/js/cookie.js" type="text/javascript"></script>
    <!-- jQuery Min JS -->
    <script src="../assets/js/jquery.min.js"></script>
    <!-- Popper Min JS -->
    <script src="../assets/js/popper.min.js"></script>
    <!-- Bootstrap Min JS -->
    <script src="../assets/js/bootstrap.min.js"></script>
    <!-- Magnific Popup Min JS -->
    <script src="../assets/js/jquery.magnific-popup.min.js"></script>
    <!-- Appear Min JS -->
    <script src="../assets/js/jquery.appear.min.js"></script>
    <!-- Odometer Min JS -->
    <script src="../assets/js/odometer.min.js"></script>
    <!-- Owl Carousel Min JS -->
    <script src="../assets/js/owl.carousel.min.js"></script>
    <!-- MeanMenu JS -->
    <script src="../assets/js/jquery.meanmenu.js"></script>
    <!-- WOW Min JS -->
    <script src="../assets/js/wow.min.js"></script>
    <!-- Message Conversation JS -->
    <script src="../assets/js/conversation.js"></script>
    <!-- AjaxChimp Min JS -->
    <script src="../assets/js/jquery.ajaxchimp.min.js"></script>
    <!-- Form Validator Min JS -->
    <script src="../assets/js/form-validator.min.js"></script>
    <!-- Contact Form Min JS -->
    <script src="../assets/js/contact-form-script.js"></script>
    <!-- Particles Min JS -->
    <script src="../assets/js/particles.min.js"></script>
    <script src="../assets/js/coustom-particles.js"></script>
    <!-- Main JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function(){
            // Share icon functionality: on click, prevent propagation so the modal doesn't open
            $('.share-icon').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                var pdfLink = $(this).closest('a').attr('href');
                
                // If mobile and Web Share API is available, use it
                if (navigator.share && /Mobi|Android/i.test(navigator.userAgent)) {
                    navigator.share({
                        title: document.title,
                        url: pdfLink
                    })
                    .then(() => console.log('Shared successfully'))
                    .catch((error) => console.error('Error sharing:', error));
                } else {
                    // Otherwise copy the link to the clipboard and show toast notification
                    navigator.clipboard.writeText(pdfLink).then(function() {
                        $("#toast").text("Link copied to clipboard!").fadeIn(400).delay(2000).fadeOut(400);
                    }, function(err) {
                        console.error('Could not copy text: ', err);
                    });
                }
            });

            // PDF Modal: When any anchor with the PDF URL is clicked (except for share icon clicks), open the modal
            $('a[href*="s3.af-south-1.amazonaws.com"]').on('click', function(e) {
                e.preventDefault();
                var pdfUrl = $(this).attr('href');
                $('#pdfIframe').attr('src', pdfUrl);
                $('#pdfModal .modal-dialog').removeClass('modal-fullscreen');
                $('.toggle-full-view').text('Full View');
                $('#pdfModal').modal('show');
            });
            
            // Toggle full view / preview mode in the modal
            $('.toggle-full-view').on('click', function(e){
                e.preventDefault();
                var $modalDialog = $('#pdfModal .modal-dialog');
                if ($modalDialog.hasClass('modal-fullscreen')) {
                     $modalDialog.removeClass('modal-fullscreen');
                     $(this).text('Full View');
                } else {
                     $modalDialog.addClass('modal-fullscreen');
                     $(this).text('Return to Preview');
                }
            });
        });
    </script>
        
</body>

</html>
