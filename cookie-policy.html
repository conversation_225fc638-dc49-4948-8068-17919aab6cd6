<!doctype html>
<html lang="zxx">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Ed-admin: All-in-one School Management Software</title>

        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">
        <style>
            .pricing-list-tab .tabs li.current a{
                padding-top: 15px;
                padding-bottom: 15px;
            }
        </style>
    </head>

    <body>
        <!-- Start Preloader Area -->
        <!-- <div class="preloader-area">
            <div class="spinner">
                <div class="inner">
                    <div class="disc"></div>
                    <div class="disc"></div>
                    <div class="disc"></div>
                </div>
            </div>
        </div> -->
        <!-- End Preloader Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <!-- Start Pricing Area -->
        <section class="pricing-area pt-100 pb-70 bg-f4f5fe">
            <div class="container">
                <div class="tab pricing-list-tab">
                    <p class="text-center px-4">
                        At Ed-admin, we believe in being open and clear about how we use data related to you. 
                        This document provides information about what cookies this website uses. You can also change your cookie preferences. 
                        If you like to know how we handle your information, 
                        please visit our <a href="#">Privacy Policy page</a>. <br>
                        Your current status is:
                    </p>
                    <ul class="tabs">
                        <li id="accept-cookie"><a href="#" style="padding-left: 30px;">
                            I accept all cookies
                        </a></li>
                        
                        <li id="decline-cookie"><a href="#" style="padding-left: 30px;">
                            I decline all cookies
                        </a></li>
                    </ul>

                    <div>
                        <div class="tabs_item">
                            <div class="row justify-content-center">
                                <div class="col-lg-10 col-md-10 col-sm-10">
                                    <div class="single-pricing-table">
                                        <div class="pricing-header">
                                            <h3>The types of cookies we use</h3>
                                        </div>

                                        <h5 class="px-3 text-left">
                                            Required
                                        </h5>
                                        <p class="px-3 text-left">
                                            We use cookies necessary to perform essential website functions. For example, they are used to save your language preferences, improve performance, route traffic between web servers, detect your screen size, determine loading pages, improving the user experience, and measuring the audience. These cookies are necessary for the operation of our websites.
                                        </p>
                                        <p class="px-3 text-left font-weight-bold">
                                            Please note that due to their essentiality, you may not reject these cookies even if you choose the “I decline all cookies” button.
                                        </p>
                                        <h5 class="px-3 text-left">
                                            Analytics
                                        </h5>
                                        <p class="px-3 text-left">
                                            We allow third parties to use analytics cookies to understand how you use our websites in order to improve them. Third parties may develop and improve their products which they may use on websites that are not operated by Ed-admin, or that Ed-admin does not own. For example, they are used to collect information about the pages you visit and the number of clicks you need to complete a task. We use analytics cookies for advertising purposes.
                                        </p>
                                        <h5 class="px-3 text-left">
                                            Publicity
                                        </h5>
                                        <p class="px-3 text-left">
                                            We and third parties use advertising and marketing cookies to display new advertisements by saving those you have already seen. They are also used to identify which advertisements you click or which purchases you make after clicking on an advertisement for payment purposes and to display advertisements that are more relevant to you. For example, they are used to detect when you click on an advertisement and display advertisements based on your social media interests and website browsing history.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Pricing Area -->

        <!-- Start Header Area -->
        <div data-include="common/footer"></div>
        <!-- End Header Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>
         
        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>
        
        <script>
            $(document).ready(function() {
                if(getAcceptCookie()){
                    $("#accept-cookie").addClass("current");
                    $("#decline-cookie").removeClass("current");
                }
                else{
                    $("#accept-cookie").removeClass("current");
                    $("#decline-cookie").addClass("current");
                }
            });
            $("#accept-cookie").click(function(){
                setAcceptCookie();
            });
            $("#decline-cookie").click(function(){
                setAcceptCookie(false);
            });
        </script>
    </body>
</html>