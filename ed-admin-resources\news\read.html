<!DOCTYPE html>
<html lang="zxx">
<head>
    <meta charset="utf-8">
    <meta name="description" content="Single news post">
    <meta name="keywords" content="Ed-admin news post">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- CSS imports -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/animate.min.css">
    <link rel="stylesheet" href="/assets/css/boxicons.min.css">
    <link rel="stylesheet" href="/assets/css/owl.carousel.min.css">
    <link rel="stylesheet" href="/assets/css/odometer.min.css">
    <link rel="stylesheet" href="/assets/css/meanmenu.css">
    <link rel="stylesheet" href="/assets/css/magnific-popup.min.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/responsive.css">

    <title>News Post - Ed-admin</title>

    <style>
        .news-details-desc {
            max-width: 900px;
            margin: 0 auto;
        }

        .article-image {
            text-align: center;
            margin-bottom: 2rem;
        }

        .article-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .article-content {
            font-size: 16px;
            line-height: 1.8;
            color: #333;
        }

        .article-content h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2d3748;
        }

        .article-content blockquote {
            position: relative;
            padding: 2rem 3rem;
            background-color: #f8f9fa;
            margin: 2rem 0;
            border-left: 4px solid #3498db;
        }

        .article-meta {
            margin-bottom: 2rem;
            color: #666;
            font-size: 0.9rem;
        }

        .article-meta i {
            margin-right: 5px;
            color: #3498db;
        }

        .loading-container {
            text-align: left;
            padding: 50px;
        }

        .error-message {
            text-align: center;
            padding: 50px;
            background: #fff5f5;
            border-radius: 8px;
            color: #e53e3e;
        }
    </style>
</head>
<body>
    <!-- Include your header -->
    <div data-include="/common/header2"></div>

    <!-- News Details Area -->
    <section class="news-details-area ptb-100">
        <div class="container">
            <!-- This is where the featured image goes (dynamically set below) -->
            <div style="width: 100%; display: flex; justify-content: center; align-items: center;">
                <img 
                    id="dynamic-featured-image" 
                    src="" 
                    alt="featured-image" 
                    style="width: 80%; height: 400px; object-fit: cover;"
                    onerror="this.src='/assets/img/default-blog-image.jpg';"
                />
            </div>

            <div id="news-content" class="loading-container">
                <div class="spinner">
                    <div class="inner">
                        <div class="disc"></div>
                        <div class="disc"></div>
                        <div class="disc"></div>
                    </div>
                </div>
                <p>Loading news content...</p>
            </div>
        </div>
    </section>

    <!-- Free Trial Area -->
    <section class="free-trial-area ptb-100 bg-f4f5fe">
        <div class="container">
            <div class="free-trial-content">
                <h2>Like to see Ed-admin in action? Ask for a demo!</h2>
                <a href="#" class="default-btn" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/ed-admin/free-demo'});return false;">
                    Free Demo <span></span>
                </a>
            </div>
        </div>

        <div class="shape10"><img src="/assets/img/shape/10.png" alt="image"></div>
        <div class="shape11"><img src="/assets/img/shape/7.png" alt="image"></div>
        <div class="shape12"><img src="/assets/img/shape/11.png" alt="image"></div>
        <div class="shape13"><img src="/assets/img/shape/12.png" alt="image"></div>
    </section>

    <!-- Include your footer -->
    <div data-include="/common/footer"></div>

    <script>
    /**
     * Decodes HTML entities (e.g. &nbsp; becomes an actual space)
     */
    function decodeHtmlEntities(htmlString) {
        const txt = document.createElement("textarea");
        txt.innerHTML = htmlString;
        return txt.value;
    }

    /**
     * Retrieves the featured image URL and news ID from localStorage,
     * then sets the featured image and fetches the news content.
     */
    async function loadNewsDetails() {
        // Get data from localStorage
        const newsId = localStorage.getItem('selectedNewsId');
        const storedFeaturedImage = localStorage.getItem('selectedNewsImage');

        // If no ID is found, redirect to the listing page (or handle gracefully)
        if (!newsId) {
            window.location.href = 'index.html';
            return;
        }

        // Immediately set the <img> src to the stored featured image
        const featuredImgTag = document.getElementById('dynamic-featured-image');
        if (featuredImgTag && storedFeaturedImage) {
            featuredImgTag.src = storedFeaturedImage;
        }

        // Fetch the news post details from WordPress (or your news API)
        const contentContainer = document.getElementById('news-content');

        try {
            const response = await fetch(
                `https://public-api.wordpress.com/wp/v2/sites/edadmin7.wordpress.com/posts/${newsId}?_embed=true`
            );
            if (!response.ok) throw new Error('Failed to fetch news details');
            const news = await response.json();

            // Format the publish date
            const publishDate = new Date(news.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // Populate the news content
            contentContainer.innerHTML = `
                <div class="row">
                    <div class="col-md-12">
                        <div class="news-details-desc">
                            <div class="article-content">
                                <div class="article-meta">
                                    <i class='bx bx-calendar'></i>
                                    <span>${publishDate}</span>
                                </div>
                                <h1>${news.title.rendered}</h1>
                                ${news.content.rendered}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Decode HTML entities and generate a clean slug from the news title
            const decodedTitle = decodeHtmlEntities(news.title.rendered);
            const newsSlug = decodedTitle
                .normalize("NFD")                  // Decompose accented characters
                .replace(/[\u0300-\u036f]/g, '')     // Remove diacritical marks
                .toLowerCase()
                .trim()
                .replace(/\s+/g, '-')               // Replace spaces with hyphens
                .replace(/[^\w\-]+/g, '');           // Remove non-alphanumeric characters

            // Get current URL's directory by removing the filename (if any)
            const currentPath = window.location.pathname;
            const directory = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
            window.history.pushState(null, decodedTitle, directory + newsSlug);

            // Process any additional WordPress content formatting
            processNewsContent();

        } catch (error) {
            console.error('Error loading news:', error);
            contentContainer.innerHTML = `
                <div class="error-message">
                    <h2>Error loading news content</h2>
                    <p>Please try again later or go back to the news listing.</p>
                    <a href="index.html" class="default-btn">Return to News List</a>
                </div>
            `;
        }
    }

    /**
     * Example helper function to handle WordPress content formatting for news
     */
    function processNewsContent() {
        const content = document.querySelector('.article-content');
        if (!content) return;

        // Make images responsive
        content.querySelectorAll('img').forEach(img => {
            img.classList.add('img-fluid');
            const parent = img.parentElement;
            if (parent && parent.tagName === 'P') {
                parent.style.textAlign = 'center';
            }
        });

        // Make iframes responsive (e.g., embedded videos)
        content.querySelectorAll('iframe').forEach(iframe => {
            iframe.style.maxWidth = '100%';
            const wrapper = document.createElement('div');
            wrapper.style.position = 'relative';
            wrapper.style.paddingBottom = '56.25%'; // 16:9 aspect ratio
            wrapper.style.height = '0';
            iframe.style.position = 'absolute';
            iframe.style.top = '0';
            iframe.style.left = '0';
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.parentElement.insertBefore(wrapper, iframe);
            wrapper.appendChild(iframe);
        });
    }

    document.addEventListener('DOMContentLoaded', loadNewsDetails);
    </script>

    <script src="/assets/js/cookie.js" type="text/javascript"></script>
    <script>
        if(getAcceptCookie()){
            (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:2174932,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        }
    </script>

    <!-- JS imports -->
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/popper.min.js"></script>
    <script src="/assets/js/bootstrap.min.js"></script>
    <script src="/assets/js/jquery.magnific-popup.min.js"></script>
    <script src="/assets/js/jquery.appear.min.js"></script>
    <script src="/assets/js/odometer.min.js"></script>
    <script src="/assets/js/owl.carousel.min.js"></script>
    <script src="/assets/js/jquery.meanmenu.js"></script>
    <script src="/assets/js/wow.min.js"></script>
    <script src="/assets/js/main.js"></script>
</body>
</html>
