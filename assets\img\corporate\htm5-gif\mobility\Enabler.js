(function(){var DEPS_GRAPH={'enablermodule':[],'configurablemodule':['enablermodule'],'gdnmodule':['enablermodule'],'layoutsmodule':['enablermodule'],'videomodule':['enablermodule'],'configurablefillermodule':['configurablemodule','enablermodule'],'layoutsfillermodule':['enablermodule','layoutsmodule'],'rad_ui_videomodule':['videomodule'],'$weak$':['configurablefillermodule','configurablemodule','enablermodule','gdnmodule','layoutsfillermodule','layoutsmodule','rad_ui_videomodule','videomodule']};window.STUDIO_SDK_START=+new Date();/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var h,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},da=ca(this),ea=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
ea("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
ea("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});
var fa=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},ha=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}},ia="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;
if("function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var na=ja,k=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.J=b.prototype},oa=function(){this.l=!1;this.h=null;this.o=void 0;this.g=1;this.A=this.s=0;this.j=null},pa=function(a){if(a.l)throw new TypeError("Generator is already running");a.l=!0};oa.prototype.v=function(a){this.o=a};
var qa=function(a,b){a.j={Yd:b,xe:!0};a.g=a.s||a.A};oa.prototype.return=function(a){this.j={return:a};this.g=this.A};
var ra=function(a){this.g=new oa;this.h=a},ua=function(a,b){pa(a.g);var c=a.g.h;if(c)return sa(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return ta(a)},sa=function(a,b,c,d){try{var e=b.call(a.g.h,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.l=!1,e;var f=e.value}catch(g){return a.g.h=null,qa(a.g,g),ta(a)}a.g.h=null;d.call(a.g,f);return ta(a)},ta=function(a){for(;a.g.g;)try{var b=a.h(a.g);
if(b)return a.g.l=!1,{value:b.value,done:!1}}catch(c){a.g.o=void 0,qa(a.g,c)}a.g.l=!1;if(a.g.j){b=a.g.j;a.g.j=null;if(b.xe)throw b.Yd;return{value:b.return,done:!0}}return{value:void 0,done:!0}},va=function(a){this.next=function(b){pa(a.g);a.g.h?b=sa(a,a.g.h.next,b,a.g.v):(a.g.v(b),b=ta(a));return b};this.throw=function(b){pa(a.g);a.g.h?b=sa(a,a.g.h["throw"],b,a.g.v):(qa(a.g,b),b=ta(a));return b};this.return=function(b){return ua(a,b)};this[Symbol.iterator]=function(){return this}},wa=function(a,
b){b=new va(new ra(b));na&&a.prototype&&na(b,a.prototype);return b};
ea("Promise",function(a){function b(){this.g=null}function c(g){return g instanceof e?g:new e(function(l){l(g)})}if(a)return a;b.prototype.h=function(g){if(null==this.g){this.g=[];var l=this;this.j(function(){l.s()})}this.g.push(g)};var d=da.setTimeout;b.prototype.j=function(g){d(g,0)};b.prototype.s=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var l=0;l<g.length;++l){var m=g[l];g[l]=null;try{m()}catch(p){this.l(p)}}}this.g=null};b.prototype.l=function(g){this.j(function(){throw g;
})};var e=function(g){this.g=0;this.j=void 0;this.h=[];this.v=!1;var l=this.l();try{g(l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.l=function(){function g(p){return function(r){m||(m=!0,p.call(l,r))}}var l=this,m=!1;return{resolve:g(this.ha),reject:g(this.s)}};e.prototype.ha=function(g){if(g===this)this.s(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof e)this.K(g);else{a:switch(typeof g){case "object":var l=null!=g;break a;case "function":l=!0;break a;default:l=
!1}l?this.$(g):this.o(g)}};e.prototype.$=function(g){var l=void 0;try{l=g.then}catch(m){this.s(m);return}"function"==typeof l?this.H(l,g):this.o(g)};e.prototype.s=function(g){this.A(2,g)};e.prototype.o=function(g){this.A(1,g)};e.prototype.A=function(g,l){if(0!=this.g)throw Error("Cannot settle("+g+", "+l+"): Promise already settled in state"+this.g);this.g=g;this.j=l;2===this.g&&this.D();this.B()};e.prototype.D=function(){var g=this;d(function(){if(g.F()){var l=da.console;"undefined"!==typeof l&&
l.error(g.j)}},1)};e.prototype.F=function(){if(this.v)return!1;var g=da.CustomEvent,l=da.Event,m=da.dispatchEvent;if("undefined"===typeof m)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof l?g=new l("unhandledrejection",{cancelable:!0}):(g=da.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.j;return m(g)};e.prototype.B=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);
this.h=null}};var f=new b;e.prototype.K=function(g){var l=this.l();g.lb(l.resolve,l.reject)};e.prototype.H=function(g,l){var m=this.l();try{g.call(l,m.resolve,m.reject)}catch(p){m.reject(p)}};e.prototype.then=function(g,l){function m(P,Ba){return"function"==typeof P?function(Hb){try{p(P(Hb))}catch(Ja){r(Ja)}}:Ba}var p,r,H=new e(function(P,Ba){p=P;r=Ba});this.lb(m(g,p),m(l,r));return H};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.lb=function(g,l){function m(){switch(p.g){case 1:g(p.j);
break;case 2:l(p.j);break;default:throw Error("Unexpected state: "+p.g);}}var p=this;null==this.h?f.h(m):this.h.push(m);this.v=!0};e.resolve=c;e.reject=function(g){return new e(function(l,m){m(g)})};e.race=function(g){return new e(function(l,m){for(var p=ha(g),r=p.next();!r.done;r=p.next())c(r.value).lb(l,m)})};e.all=function(g){var l=ha(g),m=l.next();return m.done?c([]):new e(function(p,r){function H(Hb){return function(Ja){P[Hb]=Ja;Ba--;0==Ba&&p(P)}}var P=[],Ba=0;do P.push(void 0),Ba++,c(m.value).lb(H(P.length-
1),r),m=l.next();while(!m.done)})};return e});var xa=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
ea("WeakMap",function(a){function b(){}function c(m){var p=typeof m;return"object"===p&&null!==m||"function"===p}function d(m){if(!xa(m,f)){var p=new b;ba(m,f,{value:p})}}function e(m){var p=Object[m];p&&(Object[m]=function(r){if(r instanceof b)return r;Object.isExtensible(r)&&d(r);return p(r)})}if(function(){if(!a||!Object.seal)return!1;try{var m=Object.seal({}),p=Object.seal({}),r=new a([[m,2],[p,3]]);if(2!=r.get(m)||3!=r.get(p))return!1;r.delete(m);r.set(p,4);return!r.has(m)&&4==r.get(p)}catch(H){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,l=function(m){this.g=(g+=Math.random()+1).toString();if(m){m=ha(m);for(var p;!(p=m.next()).done;)p=p.value,this.set(p[0],p[1])}};l.prototype.set=function(m,p){if(!c(m))throw Error("Invalid WeakMap key");d(m);if(!xa(m,f))throw Error("WeakMap key fail: "+m);m[f][this.g]=p;return this};l.prototype.get=function(m){return c(m)&&xa(m,f)?m[f][this.g]:void 0};l.prototype.has=function(m){return c(m)&&xa(m,f)&&xa(m[f],
this.g)};l.prototype.delete=function(m){return c(m)&&xa(m,f)&&xa(m[f],this.g)?delete m[f][this.g]:!1};return l});
ea("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var l=Object.seal({x:4}),m=new a(ha([[l,"s"]]));if("s"!=m.get(l)||1!=m.size||m.get({x:4})||m.set({x:4},"t")!=m||2!=m.size)return!1;var p=m.entries(),r=p.next();if(r.done||r.value[0]!=l||"s"!=r.value[1])return!1;r=p.next();return r.done||4!=r.value[0].x||"t"!=r.value[1]||!p.next().done?!1:!0}catch(H){return!1}}())return a;var b=new WeakMap,c=function(l){this.h={};this.g=
f();this.size=0;if(l){l=ha(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};c.prototype.set=function(l,m){l=0===l?0:l;var p=d(this,l);p.list||(p.list=this.h[p.id]=[]);p.Y?p.Y.value=m:(p.Y={next:this.g,ua:this.g.ua,head:this.g,key:l,value:m},p.list.push(p.Y),this.g.ua.next=p.Y,this.g.ua=p.Y,this.size++);return this};c.prototype.delete=function(l){l=d(this,l);return l.Y&&l.list?(l.list.splice(l.index,1),l.list.length||delete this.h[l.id],l.Y.ua.next=l.Y.next,l.Y.next.ua=l.Y.ua,l.Y.head=
null,this.size--,!0):!1};c.prototype.clear=function(){this.h={};this.g=this.g.ua=f();this.size=0};c.prototype.has=function(l){return!!d(this,l).Y};c.prototype.get=function(l){return(l=d(this,l).Y)&&l.value};c.prototype.entries=function(){return e(this,function(l){return[l.key,l.value]})};c.prototype.keys=function(){return e(this,function(l){return l.key})};c.prototype.values=function(){return e(this,function(l){return l.value})};c.prototype.forEach=function(l,m){for(var p=this.entries(),r;!(r=p.next()).done;)r=
r.value,l.call(m,r[1],r[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(l,m){var p=m&&typeof m;"object"==p||"function"==p?b.has(m)?p=b.get(m):(p=""+ ++g,b.set(m,p)):p="p_"+m;var r=l.h[p];if(r&&xa(l.h,p))for(l=0;l<r.length;l++){var H=r[l];if(m!==m&&H.key!==H.key||m===H.key)return{id:p,list:r,index:l,Y:H}}return{id:p,list:r,index:-1,Y:void 0}},e=function(l,m){var p=l.g;return fa(function(){if(p){for(;p.head!=l.g;)p=p.ua;for(;p.next!=p.head;)return p=p.next,{done:!1,value:m(p)};
p=null}return{done:!0,value:void 0}})},f=function(){var l={};return l.ua=l.next=l.head=l},g=0;return c});var ya=function(a,b,c){if(null==a)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
ea("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=ya(this,b,"endsWith");b+="";void 0===c&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;0<e&&0<c;)if(d[--c]!=b[--e])return!1;return 0>=e}});ea("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ya(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
var za=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};ea("Array.prototype.keys",function(a){return a?a:function(){return za(this,function(b){return b})}});ea("Array.prototype.values",function(a){return a?a:function(){return za(this,function(b,c){return c})}});
ea("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(l){return l};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});ea("Array.prototype.entries",function(a){return a?a:function(){return za(this,function(b,c){return[b,c]})}});
ea("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});ea("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});ea("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==ya(this,b,"includes").indexOf(b,c||0)}});
ea("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)xa(b,d)&&c.push([d,b[d]]);return c}});
var n=this||self,q=function(a,b,c){a=a.split(".");c=c||n;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b},Aa=function(a,b){a=a.split(".");b=b||n;for(var c=0;c<a.length;c++)if(b=b[a[c]],null==b)return null;return b},Ca=function(){},Da=function(){throw Error("unimplemented abstract method");},Ea=function(a){a.Ga=void 0;a.Ea=function(){return a.Ga?a.Ga:a.Ga=new a}},Fa=
function(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"},Ga=function(a){var b=Fa(a);return"array"==b||"object"==b&&"number"==typeof a.length},t=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},Ka=function(a){return Object.prototype.hasOwnProperty.call(a,Ha)&&a[Ha]||(a[Ha]=++Ia)},Ha="closure_uid_"+(1E9*Math.random()>>>0),Ia=0,La=function(a,b,c){return a.call.apply(a.bind,arguments)},Ma=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=
Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},u=function(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?u=La:u=Ma;return u.apply(null,arguments)},Na=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},
Oa=function(){return Date.now()},v=function(a,b){function c(){}c.prototype=b.prototype;a.J=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ig=function(d,e,f){for(var g=Array(arguments.length-2),l=2;l<arguments.length;l++)g[l-2]=arguments[l];return b.prototype[e].apply(d,g)}},Pa=function(a){return a};var Qa=function(a,b){var c=void 0;return new (c||(c=Promise))(function(d,e){function f(m){try{l(b.next(m))}catch(p){e(p)}}function g(m){try{l(b["throw"](m))}catch(p){e(p)}}function l(m){m.done?d(m.value):(new c(function(p){p(m.value)})).then(f,g)}l((b=b.apply(a,void 0)).next())})};function w(a){if(Error.captureStackTrace)Error.captureStackTrace(this,w);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a))}v(w,Error);w.prototype.name="CustomError";var Ra;var Sa=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");w.call(this,c+a[d])};v(Sa,w);Sa.prototype.name="AssertionError";
var Ta=function(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new Sa(""+e,f||[]);},x=function(a,b,c){a||Ta("",null,b,Array.prototype.slice.call(arguments,2));return a},Ua=function(a,b){throw new Sa("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));},Va=function(a,b,c){"number"!==typeof a&&Ta("Expected number but got %s: %s.",[Fa(a),a],b,Array.prototype.slice.call(arguments,2));return a},Wa=function(a,b,c){"string"!==typeof a&&Ta("Expected string but got %s: %s.",
[Fa(a),a],b,Array.prototype.slice.call(arguments,2))},Xa=function(a,b,c){"function"!==typeof a&&Ta("Expected function but got %s: %s.",[Fa(a),a],b,Array.prototype.slice.call(arguments,2))},Za=function(a,b,c,d){a instanceof b||Ta("Expected instanceof %s but got %s.",[Ya(b),Ya(a)],c,Array.prototype.slice.call(arguments,3))},Ya=function(a){return a instanceof Function?a.displayName||a.name||"unknown type name":a instanceof Object?a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a):
null===a?"null":typeof a};var bb=function(a,b){this.g=a===$a&&b||"";this.h=ab};bb.prototype.Aa=!0;bb.prototype.ta=function(){return this.g};bb.prototype.toString=function(){return"Const{"+this.g+"}"};var cb=function(a){if(a instanceof bb&&a.constructor===bb&&a.h===ab)return a.g;Ua("expected object of type Const, got '"+a+"'");return"type_error:Const"},ab={},$a={};var db={};function eb(){var a="undefined"!==typeof window?window.trustedTypes:void 0;return null!==a&&void 0!==a?a:null}var fb;function gb(){var a,b;if(void 0===fb)try{fb=null!==(b=null===(a=eb())||void 0===a?void 0:a.createPolicy("google#safe",{createHTML:function(c){return c},createScript:function(c){return c},createScriptURL:function(c){return c}}))&&void 0!==b?b:null}catch(c){fb=null}return fb};var hb=function(){},ib=function(a,b){if(b!==db)throw Error("Bad secret");this.g=a};k(ib,hb);ib.prototype.toString=function(){return this.g.toString()};function jb(a){var b,c=null===(b=gb())||void 0===b?void 0:b.createScriptURL(a);return new ib(null!==c&&void 0!==c?c:a,db)}function kb(a){if(a instanceof ib)return a.g;throw Error("Unexpected type when unwrapping TrustedResourceUrl");}
function lb(a){var b;a=kb(a);return(null===(b=eb())||void 0===b?0:b.isScriptURL(a))?TrustedScriptURL.prototype.toString.apply(a):a};var mb=Array.prototype.indexOf?function(a,b){x(null!=a.length);return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},y=Array.prototype.forEach?function(a,b,c){x(null!=a.length);Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)},nb=Array.prototype.filter?
function(a,b,c){x(null!=a.length);return Array.prototype.filter.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=[],f=0,g="string"===typeof a?a.split(""):a,l=0;l<d;l++)if(l in g){var m=g[l];b.call(c,m,l,a)&&(e[f++]=m)}return e},ob=Array.prototype.map?function(a,b){x(null!=a.length);return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},pb=Array.prototype.some?function(a,
b,c){x(null!=a.length);return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};function qb(a){var b=n.performance.getEntriesByType("resource");a=rb(b,a,void 0);return 0>a?null:"string"===typeof b?b.charAt(a):b[a]}function rb(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return f;return-1}
function sb(a,b){b=mb(a,b);var c;(c=0<=b)&&tb(a,b);return c}function tb(a,b){x(null!=a.length);Array.prototype.splice.call(a,b,1)}function ub(a){return Array.prototype.concat.apply([],arguments)}function vb(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}function wb(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(Ga(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}}
function xb(a,b,c,d){x(null!=a.length);Array.prototype.splice.apply(a,yb(arguments,1))}function yb(a,b,c){x(null!=a.length);return 2>=arguments.length?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)}function zb(a,b){a.sort(b||Ab)}function Bb(a,b){for(var c=Array(a.length),d=0;d<a.length;d++)c[d]={index:d,value:a[d]};var e=b||Ab;zb(c,function(f,g){return e(f.value,g.value)||f.index-g.index});for(b=0;b<a.length;b++)a[b]=c[b].value}function Ab(a,b){return a>b?1:a<b?-1:0};function Cb(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function Db(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function Eb(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Fb(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b}function Gb(a,b){var c=Ga(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(null==a)return;a=a[d[c]]}return a}function Ib(a,b){return null!==a&&b in a}function Jb(a,b){for(var c in a)if(a[c]==b)return!0;return!1}
function Kb(a,b){return null!==a&&b in a?a[b]:void 0}function Lb(a,b,c){b in a||(a[b]=c)}function Mb(a){var b={},c;for(c in a)b[c]=a[c];return b}var Nb="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Ob(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Nb.length;f++)c=Nb[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var Pb,Qb=function(){if(void 0===Pb){var a=null,b=n.trustedTypes;if(b&&b.createPolicy){try{a=b.createPolicy("goog#html",{createHTML:Pa,createScript:Pa,createScriptURL:Pa})}catch(c){n.console&&n.console.error(c.message)}Pb=a}else Pb=a}return Pb};var Sb=function(a,b){this.g=b===Rb?a:""};Sb.prototype.Aa=!0;Sb.prototype.ta=function(){return this.g.toString()};Sb.prototype.toString=function(){return this.g+""};
var Tb=function(a){if(a instanceof Sb&&a.constructor===Sb)return a.g;Ua("expected object of type TrustedResourceUrl, got '"+a+"' of type "+Fa(a));return"type_error:TrustedResourceUrl"},Yb=function(a){var b=cb(Ub);if(!Vb.test(b))throw Error("Invalid TrustedResourceUrl format: "+b);var c=b.replace(Wb,function(d,e){if(!Object.prototype.hasOwnProperty.call(a,e))throw Error('Found marker, "'+e+'", in format string, "'+b+'", but no valid label mapping found in args: '+JSON.stringify(a));d=a[e];return d instanceof
bb?cb(d):encodeURIComponent(String(d))});return Xb(c)},Wb=/%{(\w+)}/g,Vb=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i"),Rb={},Xb=function(a){var b=Qb();a=b?b.createScriptURL(a):a;return new Sb(a,Rb)};var Zb=function(a,b){return 0==a.lastIndexOf(b,0)},$b=function(a){return/^[\s\xa0]*$/.test(a)},ac=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]},bc=/&/g,cc=/</g,dc=/>/g,ec=/"/g,fc=/'/g,gc=/\x00/g,hc=/[\x00&<>"']/,ic=function(a,b){return-1!=a.indexOf(b)},kc=function(a,b){var c=0;a=ac(String(a)).split(".");b=ac(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||
["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;c=jc(0==f[1].length?0:parseInt(f[1],10),0==g[1].length?0:parseInt(g[1],10))||jc(0==f[2].length,0==g[2].length)||jc(f[2],g[2]);f=f[3];g=g[3]}while(0==c)}return c},jc=function(a,b){return a<b?-1:a>b?1:0};var mc=function(a,b){this.g=b===lc?a:""};mc.prototype.Aa=!0;mc.prototype.ta=function(){return this.g.toString()};mc.prototype.toString=function(){return this.g.toString()};
var nc=function(a){if(a instanceof mc&&a.constructor===mc)return a.g;Ua("expected object of type SafeUrl, got '"+a+"' of type "+Fa(a));return"type_error:SafeUrl"},oc=RegExp('^(?:audio/(?:3gpp2|3gpp|aac|L16|midi|mp3|mp4|mpeg|oga|ogg|opus|x-m4a|x-matroska|x-wav|wav|webm)|font/\\w+|image/(?:bmp|gif|jpeg|jpg|png|tiff|webp|x-icon)|video/(?:mpeg|mp4|ogg|webm|quicktime|x-matroska))(?:;\\w+=(?:\\w+|"[\\w;,= ]+"))*$',"i"),pc=/^data:(.*);base64,[a-z0-9+\/]+=*$/i,qc=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i,
rc=function(a){if(a instanceof mc)return a;a="object"==typeof a&&a.Aa?a.ta():String(a);if(qc.test(a))a=new mc(a,lc);else{a=String(a);a=a.replace(/(%0A|%0D)/g,"");var b=a.match(pc);a=b&&oc.test(b[1])?new mc(a,lc):null}return a},sc=function(a){if(a instanceof mc)return a;a="object"==typeof a&&a.Aa?a.ta():String(a);x(qc.test(a),"%s does not match the safe URL pattern",a)||(a="about:invalid#zClosurez");return new mc(a,lc)},lc={},tc=new mc("about:invalid#zClosurez",lc);var uc={},vc=function(a,b){this.g=b===uc?a:"";this.Aa=!0};vc.prototype.ta=function(){return this.g};vc.prototype.toString=function(){return this.g.toString()};var wc=new vc("",uc);function xc(a){if(a instanceof mc)return'url("'+nc(a).replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")';a=a instanceof bb?cb(a):yc(String(a));if(/[{;}]/.test(a))throw new Sa("Value does not allow [{;}], got: %s.",[a]);return a}
function yc(a){var b=a.replace(zc,"$1").replace(zc,"$1").replace(Ac,"url");if(Bc.test(b)){if(Cc.test(a))return Ua("String value disallows comments, got: "+a),"zClosurez";for(var c=b=!0,d=0;d<a.length;d++){var e=a.charAt(d);"'"==e&&c?b=!b:'"'==e&&b&&(c=!c)}if(!b||!c)return Ua("String value requires balanced quotes, got: "+a),"zClosurez";if(!Dc(a))return Ua("String value requires balanced square brackets and one identifier per pair of brackets, got: "+a),"zClosurez"}else return Ua("String value allows only [-,.\"'%_!# a-zA-Z0-9\\[\\]] and simple functions, got: "+
a),"zClosurez";return Ec(a)}function Dc(a){for(var b=!0,c=/^[-_a-zA-Z0-9]$/,d=0;d<a.length;d++){var e=a.charAt(d);if("]"==e){if(b)return!1;b=!0}else if("["==e){if(!b)return!1;b=!1}else if(!b&&!c.test(e))return!1}return b}
var Bc=RegExp("^[-,.\"'%_!# a-zA-Z0-9\\[\\]]+$"),Ac=RegExp("\\b(url\\([ \t\n]*)('[ -&(-\\[\\]-~]*'|\"[ !#-\\[\\]-~]*\"|[!#-&*-\\[\\]-~]*)([ \t\n]*\\))","g"),zc=RegExp("\\b(calc|cubic-bezier|fit-content|hsl|hsla|linear-gradient|matrix|minmax|repeat|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?|var)\\([-+*/0-9a-z.%\\[\\], ]+\\)","g"),Cc=/\/\*/;
function Ec(a){return a.replace(Ac,function(b,c,d,e){var f="";d=d.replace(/^(['"])(.*)\1$/,function(g,l,m){f=l;return m});b=(rc(d)||tc).ta();return c+f+b+f+e})};var Fc={},Gc=function(a,b){this.g=b===Fc?a:"";this.Aa=!0},Hc=function(a,b){if(ic(a,"<"))throw Error("Selector does not allow '<', got: "+a);var c=a.replace(/('|")((?!\1)[^\r\n\f\\]|\\[\s\S])*\1/g,"");if(!/^[-_a-zA-Z0-9#.:* ,>+~[\]()=^$|]+$/.test(c))throw Error("Selector allows only [-_a-zA-Z0-9#.:* ,>+~[\\]()=^$|] and strings, got: "+a);a:{for(var d={"(":")","[":"]"},e=[],f=0;f<c.length;f++){var g=c[f];if(d[g])e.push(d[g]);else if(Jb(d,g)&&e.pop()!=g){c=!1;break a}}c=0==e.length}if(!c)throw Error("() and [] in selector must be balanced, got: "+
a);if(!(b instanceof vc)){c="";for(var l in b)if(Object.prototype.hasOwnProperty.call(b,l)){if(!/^[-_a-zA-Z0-9]+$/.test(l))throw Error("Name allows only [-_a-zA-Z0-9], got: "+l);d=b[l];null!=d&&(d=Array.isArray(d)?d.map(xc).join(" "):xc(d),c+=l+":"+d+";")}b=c?new vc(c,uc):wc}b instanceof vc&&b.constructor===vc?l=b.g:(Ua("expected object of type SafeStyle, got '"+b+"' of type "+Fa(b)),l="type_error:SafeStyle");a=a+"{"+l.replace(/</g,"\\3C ")+"}";return new Gc(a,Fc)};Gc.prototype.ta=function(){return this.g};
Gc.prototype.toString=function(){return this.g.toString()};var Ic;a:{var Jc=n.navigator;if(Jc){var Kc=Jc.userAgent;if(Kc){Ic=Kc;break a}}Ic=""}function z(a){return ic(Ic,a)};function Lc(){return z("Firefox")||z("FxiOS")}function Mc(){return(z("Chrome")||z("CriOS"))&&!z("Edge")};var Nc={},Oc=function(a,b,c){this.g=c===Nc?a:"";this.Aa=!0};Oc.prototype.ta=function(){return this.g.toString()};Oc.prototype.toString=function(){return this.g.toString()};function Pc(a){return a instanceof hb?kb(a):Tb(a)};/*

 Copyright 2021 The Safevalues Authors
 SPDX-License-Identifier: Apache-2.0
*/
function Qc(a){var b,c=(a.ownerDocument&&a.ownerDocument.defaultView||window).document,d=null===(b=c.querySelector)||void 0===b?void 0:b.call(c,"script[nonce]");(b=d?d.nonce||d.getAttribute("nonce")||"":"")&&a.setAttribute("nonce",b)};var Rc=function(){return null};var Sc=function(a,b){a.addEventListener&&a.addEventListener.call(a,"load",b,!1)};function Tc(){return z("iPhone")&&!z("iPod")&&!z("iPad")}function Uc(){return Tc()||z("iPad")||z("iPod")};var Vc=function(a){Vc[" "](a);return a};Vc[" "]=Ca;var Xc=function(a){var b=Wc;return Object.prototype.hasOwnProperty.call(b,"11")?b["11"]:b["11"]=a("11")};var Yc=z("Opera"),Zc=z("Trident")||z("MSIE"),$c=z("Edge"),ad=$c||Zc,bd=z("Gecko")&&!(ic(Ic.toLowerCase(),"webkit")&&!z("Edge"))&&!(z("Trident")||z("MSIE"))&&!z("Edge"),cd=ic(Ic.toLowerCase(),"webkit")&&!z("Edge"),dd;
a:{var ed="",fd=function(){var a=Ic;if(bd)return/rv:([^\);]+)(\)|;)/.exec(a);if($c)return/Edge\/([\d\.]+)/.exec(a);if(Zc)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(cd)return/WebKit\/(\S+)/.exec(a);if(Yc)return/(?:Version)[ \/]?(\S+)/.exec(a)}();fd&&(ed=fd?fd[1]:"");if(Zc){var gd,hd=n.document;gd=hd?hd.documentMode:void 0;if(null!=gd&&gd>parseFloat(ed)){dd=String(gd);break a}}dd=ed}var id=dd,Wc={},jd=function(){return Xc(function(){return 0<=kc(id,"11")})};var kd=function(a,b){a:{try{var c=a&&a.ownerDocument,d=c&&(c.defaultView||c.parentWindow);d=d||n;if(d.Element&&d.Location){var e=d;break a}}catch(g){}e=null}if(e&&"undefined"!=typeof e[b]&&(!a||!(a instanceof e[b])&&(a instanceof e.Location||a instanceof e.Element))){if(t(a))try{var f=a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a)}catch(g){f="<object could not be stringified>"}else f=void 0===a?"undefined":null===a?"null":typeof a;Ua("Argument is not a %s (or a non-Element, non-Location mock); got: %s",
b,f)}};var md=function(a,b){kd(a,"HTMLScriptElement");a.src=Tb(b);(b=ld("script[nonce]",a.ownerDocument&&a.ownerDocument.defaultView))&&a.setAttribute("nonce",b)},nd=function(a,b,c,d){a=a instanceof mc?a:sc(a);b=b||n;c=c instanceof bb?cb(c):c||"";return void 0!==d?b.open(nc(a),c,d):b.open(nc(a),c)},od=/^[\w+/_-]+[=]{0,2}$/,ld=function(a,b){b=(b||n).document;return b.querySelector?(a=b.querySelector(a))&&(a=a.nonce||a.getAttribute("nonce"))&&od.test(a)?a:"":""};var pd=function(a,b){this.width=a;this.height=b};h=pd.prototype;h.toString=function(){return"("+this.width+" x "+this.height+")"};h.aspectRatio=function(){return this.width/this.height};h.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};h.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};h.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var qd=function(a){return decodeURIComponent(a.replace(/\+/g," "))},rd=function(a,b){var c=a;0<a.length&&0<b&&(c=a.substr(0,0)+a.substr(0+b,a.length-0-b));return c},sd=function(a){return null==a?"":String(a)},td=function(a){return Array.prototype.join.call(arguments,"")},ud=function(a){for(var b=0,c=0;c<a.length;++c)b=31*b+a.charCodeAt(c)>>>0;return b},vd=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})},wd=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])",
"g"),function(b,c,d){return c+d.toUpperCase()})};var zd=function(a){return a?new xd(yd(a)):Ra||(Ra=new xd)},Bd=function(a,b){Cb(b,function(c,d){c&&"object"==typeof c&&c.Aa&&(c=c.ta());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:Ad.hasOwnProperty(d)?a.setAttribute(Ad[d],c):Zb(d,"aria-")||Zb(d,"data-")?a.setAttribute(d,c):a[d]=c})},Ad={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",
usemap:"useMap",valign:"vAlign",width:"width"},Cd=function(){var a=window.document;a="CSS1Compat"==a.compatMode?a.documentElement:a.body;return new pd(a.clientWidth,a.clientHeight)},Dd=function(a){return a.parentWindow||a.defaultView},A=function(a,b,c){return Ed(document,arguments)},Ed=function(a,b){var c=b[1],d=Fd(a,String(b[0]));c&&("string"===typeof c?d.className=c:Array.isArray(c)?d.className=c.join(" "):Bd(d,c));2<b.length&&Gd(a,d,b,2);return d},Gd=function(a,b,c,d){function e(l){l&&b.appendChild("string"===
typeof l?a.createTextNode(l):l)}for(;d<c.length;d++){var f=c[d];if(!Ga(f)||t(f)&&0<f.nodeType)e(f);else{a:{if(f&&"number"==typeof f.length){if(t(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}y(g?vb(f):f,e)}}},Fd=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)},Hd=function(a,b){x(null!=a&&null!=b,"goog.dom.appendChild expects non-null arguments");
a.appendChild(b)},Id=function(a,b){Gd(yd(a),a,arguments,1)},Jd=function(a){for(var b;b=a.firstChild;)a.removeChild(b)},Kd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null},yd=function(a){x(a,"Node cannot be null or undefined.");return 9==a.nodeType?a:a.ownerDocument||a.document},Ld=function(a,b){a&&(a=a.parentNode);for(var c=0;a;){x("parentNode"!=a.name);if(b(a))return a;a=a.parentNode;c++}return null},xd=function(a){this.g=a||n.document||document};
xd.prototype.getElement=function(a){return"string"===typeof a?this.g.getElementById(a):a};var Md=function(a,b){a=a.g;b=b&&"*"!=b?String(b).toUpperCase():"";a.querySelectorAll&&a.querySelector&&b?b=a.querySelectorAll(b+""):b=a.getElementsByTagName(b||"*");return b};xd.prototype.h=function(a,b,c){return Ed(this.g,arguments)};var Nd=function(a,b){return Fd(a.g,b)};xd.prototype.j=Hd;
xd.prototype.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var Od=function(a,b,c,d,e,f,g){var l="";a&&(l+=a+":");c&&(l+="//",b&&(l+=b+"@"),l+=c,d&&(l+=":"+d));e&&(l+=e);f&&(l+="?"+f);g&&(l+="#"+g);return l},Pd=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Qd=function(a){var b=a.indexOf("#");return 0>b?null:a.substr(b+1)},Rd=function(a){a=a.match(Pd);return Od(a[1],a[2],a[3],a[4])},Sd=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),
e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?qd(e):"")}}},Td=/#|$/,Ud=function(a,b){var c=a.search(Td);a:{var d=0;for(var e=b.length;0<=(d=a.indexOf(b,d))&&d<c;){var f=a.charCodeAt(d-1);if(38==f||63==f)if(f=a.charCodeAt(d+e),!f||61==f||38==f||35==f)break a;d+=e+1}d=-1}if(0>d)return null;e=a.indexOf("&",d);if(0>e||e>c)e=c;d+=b.length+1;return qd(a.substr(d,e-d))};var Vd=function(a){if(a.Z&&"function"==typeof a.Z)return a.Z();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(Ga(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return Fb(a)},Wd=function(a){if(a.Fa&&"function"==typeof a.Fa)return a.Fa();if(!a.Z||"function"!=typeof a.Z){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());if(!("undefined"!==typeof Set&&
a instanceof Set)){if(Ga(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Xd=function(a,b){return a.contains&&"function"==typeof a.contains?a.contains(b):a.Va&&"function"==typeof a.Va?a.Va(b):Ga(a)||"string"===typeof a?0<=mb(a,b):Jb(a,b)};var Yd=function(a){this.j=this.v=this.l="";this.A=null;this.o=this.g="";this.s=!1;var b;a instanceof Yd?(this.s=a.s,Zd(this,a.l),this.v=a.v,this.j=a.j,$d(this,a.A),this.g=a.g,ae(this,be(a.h)),this.o=a.o):a&&(b=String(a).match(Pd))?(this.s=!1,Zd(this,b[1]||"",!0),this.v=ce(b[2]||""),this.j=ce(b[3]||"",!0),$d(this,b[4]),this.g=ce(b[5]||"",!0),ae(this,b[6]||"",!0),this.o=ce(b[7]||"")):(this.s=!1,this.h=new de(null,this.s))};
Yd.prototype.toString=function(){var a=[],b=this.l;b&&a.push(ee(b,fe,!0),":");var c=this.j;if(c||"file"==b)a.push("//"),(b=this.v)&&a.push(ee(b,fe,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.A,null!=c&&a.push(":",String(c));if(c=this.g)this.j&&"/"!=c.charAt(0)&&a.push("/"),a.push(ee(c,"/"==c.charAt(0)?ge:he,!0));(c=this.h.toString())&&a.push("?",c);(c=this.o)&&a.push("#",ee(c,ie));return a.join("")};
Yd.prototype.resolve=function(a){var b=new Yd(this),c=!!a.l;c?Zd(b,a.l):c=!!a.v;c?b.v=a.v:c=!!a.j;c?b.j=a.j:c=null!=a.A;var d=a.g;if(c)$d(b,a.A);else if(c=!!a.g){if("/"!=d.charAt(0))if(this.j&&!this.g)d="/"+d;else{var e=b.g.lastIndexOf("/");-1!=e&&(d=b.g.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(ic(e,"./")||ic(e,"/.")){d=Zb(e,"/");e=e.split("/");for(var f=[],g=0;g<e.length;){var l=e[g++];"."==l?d&&g==e.length&&f.push(""):".."==l?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&
f.push("")):(f.push(l),d=!0)}d=f.join("/")}else d=e}c?b.g=d:c=""!==a.h.toString();c?ae(b,be(a.h)):c=!!a.o;c&&(b.o=a.o);return b};
var Zd=function(a,b,c){a.l=c?ce(b,!0):b;a.l&&(a.l=a.l.replace(/:$/,""))},$d=function(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.A=b}else a.A=null},ae=function(a,b,c){b instanceof de?(a.h=b,je(a.h,a.s)):(c||(b=ee(b,ke)),a.h=new de(b,a.s))},ce=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},ee=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,le),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},le=function(a){a=
a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},fe=/[#\/\?@]/g,he=/[#\?:]/g,ge=/[#\?]/g,ke=/[#\?@]/g,ie=/#/g,de=function(a,b){this.h=this.g=null;this.j=a||null;this.l=!!b},me=function(a){a.g||(a.g=new Map,a.h=0,a.j&&Sd(a.j,function(b,c){a.add(qd(b),c)}))};de.prototype.la=function(){me(this);return this.h};de.prototype.add=function(a,b){me(this);this.j=null;a=ne(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h=Va(this.h)+1;return this};
var oe=function(a,b){me(a);b=ne(a,b);a.g.has(b)&&(a.j=null,a.h=Va(a.h)-a.g.get(b).length,a.g.delete(b))};h=de.prototype;h.ka=function(a){me(this);a=ne(this,a);return this.g.has(a)};h.Va=function(a){var b=this.Z();return 0<=mb(b,a)};h.forEach=function(a,b){me(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};
h.Fa=function(){me(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};h.Z=function(a){me(this);var b=[];if("string"===typeof a)this.ka(a)&&(b=b.concat(this.g.get(ne(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
h.set=function(a,b){me(this);this.j=null;a=ne(this,a);this.ka(a)&&(this.h=Va(this.h)-this.g.get(a).length);this.g.set(a,[b]);this.h=Va(this.h)+1;return this};h.get=function(a,b){if(!a)return b;a=this.Z(a);return 0<a.length?String(a[0]):b};
h.toString=function(){if(this.j)return this.j;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Z(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.j=a.join("&")};
var be=function(a){var b=new de;b.j=a.j;a.g&&(b.g=new Map(a.g),b.h=a.h);return b},ne=function(a,b){b=String(b);a.l&&(b=b.toLowerCase());return b},je=function(a,b){b&&!a.l&&(me(a),a.j=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(oe(this,d),oe(this,e),0<c.length&&(this.j=null,this.g.set(ne(this,e),vb(c)),this.h=Va(this.h)+c.length))},a));a.l=b};var pe,qe,re,se,te,ue,ve=function(){return n.navigator?n.navigator.userAgent:""},we=function(a){return ic(ve(),a)},xe=we("(iPad")||we("(Macintosh")||we("(iPod")||we("(iPhone"),ye=we("Android"),ze=we("MSIE")||we("IEMobile")||we("Windows Phone"),Ae=function(){if(void 0===pe){var a=/Android\s+([0-9.]+)/.exec(ve());pe=a&&2==a.length?a[1]:""}return pe},Be=function(){void 0===se&&(se=we("afma-sdk-a")?!0:!1);return se},Ce=function(){if(void 0===te){a:{if(void 0===re){if(xe){var a=we("Safari");var b=(new Yd(window.location.href)).h.Z("js");
b:{if((b=b.length?b[0]:"")&&Zb(b,"afma-")){var c=b.lastIndexOf("v");if(-1<c&&(b=b.substr(c+1).match(/^(\d+\.\d+\.\d+|^\d+\.\d+|^\d+)(-.*)?$/))){b=b[1];break b}}b="0.0.0"}if(!a||"0.0.0"!==b){a=re=!0;break a}}re=!1}a=re}te=a||Be()}return te},De=function(a){var b;void 0===ue&&(ue=Be()?(b=ve().match(/afma\-sdk\-a\-v\.?([\d+\.]+)/))?b[1]:"":"");return(b=ue)?0<=kc(b,a):!1};var Ee=function(a,b){this.o=Math.random()<a;this.v=b;this.h=null;this.s=""};Ee.prototype.j=function(){return this.o&&null!==this.h?(this.v+"//pagead2.googlesyndication.com/pagead/gen_204/?id="+this.h+this.s).substring(0,2E3):""};var Fe=function(){},Ie=function(a){Ge=a|6;He(Ge)},He=function(a){Je(a,2,1);Je(a,1,2);Je(a,4,8);Je(a,8,4);Je(a,128,64);Je(a,64,128);Je(a,256,2)},Je=function(a,b,c){(a&b)==b&&(Ge|=b,Ge&=~c)};q("studio.common.Environment",Fe,void 0);Fe.Type={LIVE:1,LOCAL:2,BROWSER:4,IN_APP:8,LAYOUTS_PREVIEW:16,CREATIVE_TOOLSET:32,RENDERING_STUDIO:64,RENDERING_TEST:128,PREVIEW:256};var Ge=6;Fe.addType=function(a){Ge|=a;He(a)};Fe.setType=Ie;var B=function(a){return(Ge&a)==a};Fe.hasType=B;Fe.getValue=function(){return Ge};var Ke=function(a,b){Ee.call(this,a,b);this.l=this.g=null;this.h="rmad_mod"};k(Ke,Ee);Ke.prototype.j=function(){return null!==this.g&&null!==this.l?(this.s="&status="+this.g+"&type="+this.l,Ee.prototype.j.call(this)):""};var Le={Ff:"dcm",fg:"studio"};var Me=Object.freeze||function(a){return a};var Ne=function(a,b){this.name=a;this.value=b};Ne.prototype.toString=function(){return this.name};var Oe=new Ne("OFF",Infinity),Pe=new Ne("SHOUT",1200),Qe=new Ne("SEVERE",1E3),Re=new Ne("WARNING",900),Se=new Ne("INFO",800),Te=new Ne("CONFIG",700),Ue=new Ne("FINE",500),Ve=new Ne("FINER",400),We=new Ne("FINEST",300),Xe=new Ne("ALL",0),Ye=[Oe,Pe,Qe,Re,Se,Te,Ue,Ve,We,Xe],Ze=null,$e=function(){},af,bf=function(a,b,c){this.g=void 0;this.reset(a||Oe,b,c,void 0,void 0)};
bf.prototype.reset=function(a,b,c,d){this.j=d||Oa();this.l=a;this.s=b;this.h=c;this.g=void 0};
var cf=function(a,b){this.g=null;this.l=[];this.j=(void 0===b?null:b)||null;this.s=[];this.h={G:function(){return a}}},df=function(a){if(a.g)return a.g;if(a.j)return df(a.j);Ua("Root logger has no level set.");return Oe},ef=function(a,b){for(;a;)a.l.forEach(function(c){c(b)}),a=a.j},ff=function(){this.entries={};var a=new cf("");a.g=Te;this.entries[""]=a},gf,hf=function(a,b,c){var d=a.entries[b];if(d)return void 0!==c&&(d.g=c),d;d=hf(a,b.substr(0,b.lastIndexOf(".")));var e=new cf(b,d);a.entries[b]=
e;d.s.push(e);void 0!==c&&(e.g=c);return e},jf=function(){gf||(gf=new ff);return gf},C=function(a){return hf(jf(),a,void 0).h},kf=function(a,b){a&&(hf(jf(),a.G()).g=b)},lf=function(a,b,c,d){var e;if(e=a)if(e=a&&b){e=b.value;var f=a?df(hf(jf(),a.G())):Oe;e=e>=f.value}e&&(b=b||Oe,e=hf(jf(),a.G()),"function"===typeof c&&(c=c()),af||(af=new $e),a=new bf(b,c,a.G()),a.g=d,ef(e,a))},mf=function(a,b){a&&lf(a,Qe,b,void 0)},D=function(a,b,c){a&&lf(a,Re,b,c)},E=function(a,b){a&&lf(a,Se,b,void 0)},nf=function(a,
b){a&&lf(a,Ue,b,void 0)};var of=function(){this.g=C("studio.sdk")};h=of.prototype;h.log=function(a,b){lf(this.g,a,b)};h.hf=function(a){lf(this.g,Pe,a)};h.$a=function(a){mf(this.g,a)};h.T=function(a){D(this.g,a)};h.info=function(a){E(this.g,a)};h.Rd=function(a){lf(this.g,Te,a)};h.Pa=function(a){nf(this.g,a)};h.Zd=function(a){lf(this.g,Ve,a)};h.$d=function(a){lf(this.g,We,a)};var F=new of;q("studio.sdk.logger",F,void 0);q("studio.sdk.logger.setLevel",function(a,b){kf(a,b)},void 0);q("studio.sdk.logger.Level.OFF",Oe,void 0);
q("studio.sdk.logger.Level.SHOUT",Pe,void 0);q("studio.sdk.logger.Level.SEVERE",Qe,void 0);q("studio.sdk.logger.Level.WARNING",Re,void 0);q("studio.sdk.logger.Level.INFO",Se,void 0);q("studio.sdk.logger.Level.CONFIG",Te,void 0);q("studio.sdk.logger.Level.FINE",Ue,void 0);q("studio.sdk.logger.Level.FINER",Ve,void 0);q("studio.sdk.logger.Level.FINEST",We,void 0);q("studio.sdk.logger.Level.ALL",Xe,void 0);q("studio.sdk.logger.log",F.log,void 0);q("studio.sdk.logger.shout",F.hf,void 0);
q("studio.sdk.logger.severe",F.$a,void 0);q("studio.sdk.logger.warning",F.T,void 0);q("studio.sdk.logger.info",F.info,void 0);q("studio.sdk.logger.config",F.Rd,void 0);q("studio.sdk.logger.fine",F.Pa,void 0);q("studio.sdk.logger.finer",F.Zd,void 0);q("studio.sdk.logger.finest",F.$d,void 0);var pf={ENABLER:"enabler",DCM_ENABLER:"dcmenabler",SSR_ENABLER:"ssrenabler",VIDEO:"video",CONFIGURABLE:"configurable",CONFIGURABLE_FILLER:"configurablefiller",LAYOUTS:"layouts",FILLER:"layoutsfiller",RAD_VIDEO:"rad_ui_video",GDN:"gdn"};q("studio.module.ModuleId",pf,void 0);var rf=function(a){qf();return Xb(a)},qf=Ca;var sf=function(a,b){this.j=a;this.l=b;this.h=0;this.g=null};sf.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.j();return a};var tf=function(a,b){a.l(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};var uf,vf=function(){var a=n.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!z("Presto")&&(a=function(){var e=Fd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),l="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=u(function(m){if(("*"==l||m.origin==l)&&m.data==g)this.port1.onmessage()},
this);f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,l)}}});if("undefined"!==typeof a&&!z("Trident")&&!z("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.pc;c.pc=null;e()}};return function(e){d.next={pc:e};d=d.next;b.port2.postMessage(0)}}return function(e){n.setTimeout(e,0)}};function wf(a){n.setTimeout(function(){throw a;},0)};var xf=function(){this.h=this.g=null};xf.prototype.add=function(a,b){var c=yf.get();c.set(a,b);this.h?this.h.next=c:(x(!this.g),this.g=c);this.h=c};var Af=function(){var a=zf,b=null;a.g&&(b=a.g,a.g=a.g.next,a.g||(a.h=null),b.next=null);return b},yf=new sf(function(){return new Bf},function(a){return a.reset()}),Bf=function(){this.next=this.scope=this.g=null};Bf.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};Bf.prototype.reset=function(){this.next=this.scope=this.g=null};var Ff=function(a,b){Cf||Df();Ef||(Cf(),Ef=!0);zf.add(a,b)},Cf,Df=function(){if(n.Promise&&n.Promise.resolve){var a=n.Promise.resolve(void 0);Cf=function(){a.then(Gf)}}else Cf=function(){var b=Gf;"function"!==typeof n.setImmediate||n.Window&&n.Window.prototype&&!z("Edge")&&n.Window.prototype.setImmediate==n.setImmediate?(uf||(uf=vf()),uf(b)):n.setImmediate(b)}},Ef=!1,zf=new xf,Gf=function(){for(var a;a=Af();){try{a.g.call(a.scope)}catch(b){wf(b)}tf(yf,a)}Ef=!1};var If=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var Lf=function(a){this.g=0;this.v=void 0;this.l=this.h=this.j=null;this.s=this.o=!1;if(a!=Ca)try{var b=this;a.call(void 0,function(c){Jf(b,2,c)},function(c){if(!(c instanceof Kf))try{if(c instanceof Error)throw c;throw Error("Promise rejected.");}catch(d){}Jf(b,3,c)})}catch(c){Jf(this,3,c)}},Mf=function(){this.next=this.context=this.j=this.h=this.g=null;this.l=!1};Mf.prototype.reset=function(){this.context=this.j=this.h=this.g=null;this.l=!1};
var Nf=new sf(function(){return new Mf},function(a){a.reset()}),Of=function(a,b,c){var d=Nf.get();d.h=a;d.j=b;d.context=c;return d},Pf=function(a){if(a instanceof Lf)return a;var b=new Lf(Ca);Jf(b,2,a);return b},Qf=function(a){return new Lf(function(b,c){c(a)})},Sf=function(a,b,c){Rf(a,b,c,null)||Ff(Na(b,a))},Tf=function(a){return new Lf(function(b,c){var d=a.length,e=[];if(d)for(var f=function(p,r){d--;e[p]=r;0==d&&b(e)},g=function(p){c(p)},l=0,m;l<a.length;l++)m=a[l],Sf(m,Na(f,l),g);else b(e)})};
Lf.prototype.then=function(a,b,c){null!=a&&Xa(a,"opt_onFulfilled should be a function.");null!=b&&Xa(b,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");return Uf(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};Lf.prototype.$goog_Thenable=!0;var Vf=function(a,b){return Uf(a,null,b,void 0)};Lf.prototype.cancel=function(a){if(0==this.g){var b=new Kf(a);Ff(function(){Wf(this,b)},this)}};
var Wf=function(a,b){if(0==a.g)if(a.j){var c=a.j;if(c.h){for(var d=0,e=null,f=null,g=c.h;g&&(g.l||(d++,g.g==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.g&&1==d?Wf(c,b):(f?(d=f,x(c.h),x(null!=d),d.next==c.l&&(c.l=d),d.next=d.next.next):Xf(c),Yf(c,e,3,b)))}a.j=null}else Jf(a,3,b)},$f=function(a,b){a.h||2!=a.g&&3!=a.g||Zf(a);x(null!=b.h);a.l?a.l.next=b:a.h=b;a.l=b},Uf=function(a,b,c,d){var e=Of(null,null,null);e.g=new Lf(function(f,g){e.h=b?function(l){try{var m=b.call(d,l);f(m)}catch(p){g(p)}}:
f;e.j=c?function(l){try{var m=c.call(d,l);void 0===m&&l instanceof Kf?g(l):f(m)}catch(p){g(p)}}:g});e.g.j=a;$f(a,e);return e.g};Lf.prototype.B=function(a){x(1==this.g);this.g=0;Jf(this,2,a)};Lf.prototype.F=function(a){x(1==this.g);this.g=0;Jf(this,3,a)};
var Jf=function(a,b,c){0==a.g&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,Rf(c,a.B,a.F,a)||(a.v=c,a.g=b,a.j=null,Zf(a),3!=b||c instanceof Kf||ag(a,c)))},Rf=function(a,b,c,d){if(a instanceof Lf)return null!=b&&Xa(b,"opt_onFulfilled should be a function."),null!=c&&Xa(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?"),$f(a,Of(b||Ca,c||null,d)),!0;if(If(a))return a.then(b,c,d),!0;if(t(a))try{var e=a.then;if("function"===
typeof e)return bg(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},bg=function(a,b,c,d,e){var f=!1,g=function(m){f||(f=!0,c.call(e,m))},l=function(m){f||(f=!0,d.call(e,m))};try{b.call(a,g,l)}catch(m){l(m)}},Zf=function(a){a.o||(a.o=!0,Ff(a.A,a))},Xf=function(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.l=null);null!=b&&x(null!=b.h);return b};Lf.prototype.A=function(){for(var a;a=Xf(this);)Yf(this,a,this.g,this.v);this.o=!1};
var Yf=function(a,b,c,d){if(3==c&&b.j&&!b.l)for(;a&&a.s;a=a.j)a.s=!1;if(b.g)b.g.j=null,cg(b,c,d);else try{b.l?b.h.call(b.context):cg(b,c,d)}catch(e){dg.call(null,e)}tf(Nf,b)},cg=function(a,b,c){2==b?a.h.call(a.context,c):a.j&&a.j.call(a.context,c)},ag=function(a,b){a.s=!0;Ff(function(){a.s&&dg.call(null,b)})},dg=wf,Kf=function(a){w.call(this,a)};v(Kf,w);Kf.prototype.name="cancel";/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var G=function(a,b){this.v=[];this.K=a;this.D=b||null;this.l=this.g=!1;this.j=void 0;this.F=this.$=this.B=!1;this.A=0;this.h=null;this.s=0};G.prototype.cancel=function(a){if(this.g)this.j instanceof G&&this.j.cancel();else{if(this.h){var b=this.h;delete this.h;a?b.cancel(a):(b.s--,0>=b.s&&b.cancel())}this.K?this.K.call(this.D,this):this.F=!0;this.g||this.o(new eg(this))}};G.prototype.ha=function(a,b){this.B=!1;fg(this,a,b)};
var fg=function(a,b,c){a.g=!0;a.j=c;a.l=!b;gg(a)},ig=function(a){if(a.g){if(!a.F)throw new hg(a);a.F=!1}};G.prototype.L=function(a){ig(this);jg(a);fg(this,!0,a)};G.prototype.o=function(a){ig(this);jg(a);fg(this,!1,a)};var jg=function(a){x(!(a instanceof G),"An execution sequence may not be initiated with a blocking Deferred.")},lg=function(a,b,c){kg(a,b,null,c)},kg=function(a,b,c,d){x(!a.$,"Blocking Deferreds can not be re-used");a.v.push([b,c,d]);a.g&&gg(a)};
G.prototype.then=function(a,b,c){var d,e,f=new Lf(function(g,l){e=g;d=l});kg(this,e,function(g){g instanceof eg?f.cancel():d(g)});return f.then(a,b,c)};G.prototype.$goog_Thenable=!0;var mg=function(a,b){b instanceof G?lg(a,u(b.H,b)):lg(a,function(){return b})};G.prototype.H=function(a){var b=new G;kg(this,b.L,b.o,b);a&&(b.h=this,this.s++);return b};
var ng=function(a){return pb(a.v,function(b){return"function"===typeof b[1]})},gg=function(a){if(a.A&&a.g&&ng(a)){var b=a.A,c=og[b];c&&(n.clearTimeout(c.g),delete og[b]);a.A=0}a.h&&(a.h.s--,delete a.h);b=a.j;for(var d=c=!1;a.v.length&&!a.B;){var e=a.v.shift(),f=e[0],g=e[1];e=e[2];if(f=a.l?g:f)try{var l=f.call(e||a.D,b);void 0!==l&&(a.l=a.l&&(l==b||l instanceof Error),a.j=b=l);if(If(b)||"function"===typeof n.Promise&&b instanceof n.Promise)d=!0,a.B=!0}catch(m){b=m,a.l=!0,ng(a)||(c=!0)}}a.j=b;d&&(l=
u(a.ha,a,!0),d=u(a.ha,a,!1),b instanceof G?(kg(b,l,d),b.$=!0):b.then(l,d));c&&(b=new pg(b),og[b.g]=b,a.A=b.g)},hg=function(){w.call(this)};v(hg,w);hg.prototype.message="Deferred has already fired";hg.prototype.name="AlreadyCalledError";var eg=function(){w.call(this)};v(eg,w);eg.prototype.message="Deferred was canceled";eg.prototype.name="CanceledError";var pg=function(a){this.g=n.setTimeout(u(this.j,this),0);this.h=a};
pg.prototype.j=function(){x(og[this.g],"Cannot throw an error that is not scheduled.");delete og[this.g];throw this.h;};var og={};var ug=function(a){var b={},c=b.document||document,d=Tb(a).toString(),e=Nd(new xd(c),"SCRIPT"),f={cd:e,jd:void 0},g=new G(qg,f),l=null,m=null!=b.timeout?b.timeout:5E3;0<m&&(l=window.setTimeout(function(){rg(e,!0);g.o(new sg(1,"Timeout reached for loading script "+d))},m),f.jd=l);e.onload=e.onreadystatechange=function(){e.readyState&&"loaded"!=e.readyState&&"complete"!=e.readyState||(rg(e,b.jg||!1,l),g.L(null))};e.onerror=function(){rg(e,!0,l);g.o(new sg(0,"Error while loading script "+d))};f=b.attributes||
{};Ob(f,{type:"text/javascript",charset:"UTF-8"});Bd(e,f);md(e,a);tg(c).appendChild(e);return g},tg=function(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&0!==b.length?b[0]:a.documentElement},qg=function(){if(this&&this.cd){var a=this.cd;a&&"SCRIPT"==a.tagName&&rg(a,!0,this.jd)}},rg=function(a,b,c){null!=c&&n.clearTimeout(c);a.onload=Ca;a.onerror=Ca;a.onreadystatechange=Ca;b&&window.setTimeout(function(){Kd(a)},0)},sg=function(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+
b);w.call(this,c);this.code=a};v(sg,w);var vg=function(a,b){this.g=a[n.Symbol.iterator]();this.h=b;this.j=0};vg.prototype[Symbol.iterator]=function(){return this};vg.prototype.next=function(){var a=this.g.next();return{value:a.done?void 0:this.h.call(void 0,a.value,this.j++),done:a.done}};var wg=function(a,b){return new vg(a,b)};var xg="StopIteration"in n?n.StopIteration:{message:"StopIteration",stack:""},yg=function(){};yg.prototype.sb=function(){throw xg;};yg.prototype.ya=function(){return this};var Dg=function(a){if(a instanceof zg||a instanceof Ag||a instanceof Bg)return a;if("function"==typeof a.sb)return new zg(function(){return Cg(a)});if("function"==typeof a[Symbol.iterator])return new zg(function(){return a[Symbol.iterator]()});if("function"==typeof a.ya)return new zg(function(){return Cg(a.ya())});throw Error("Not an iterator or iterable.");},Cg=function(a){if(!(a instanceof yg))return a;var b=!1;return{next:function(){for(var c;!b;)try{c=a.sb();break}catch(d){if(d!==xg)throw d;b=
!0}return{value:c,done:b}}}},zg=function(a){this.g=a};zg.prototype.ya=function(){return new Ag(this.g())};zg.prototype[Symbol.iterator]=function(){return new Bg(this.g())};zg.prototype.h=function(){return new Bg(this.g())};var Ag=function(a){this.g=a};k(Ag,yg);Ag.prototype.sb=function(){var a=this.g.next();if(a.done)throw xg;return a.value};Ag.prototype[Symbol.iterator]=function(){return new Bg(this.g)};Ag.prototype.h=function(){return new Bg(this.g)};
var Bg=function(a){zg.call(this,function(){return a});this.j=a};k(Bg,zg);Bg.prototype.next=function(){return this.j.next()};var I=function(a,b){this.h={};this.g=[];this.j=this.size=0;var c=arguments.length;if(1<c){if(c%2)throw Error("Uneven number of arguments");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else if(a)if(a instanceof I)for(c=a.Fa(),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(d in a)this.set(d,a[d])};h=I.prototype;h.la=function(){return this.size};h.Z=function(){Eg(this);for(var a=[],b=0;b<this.g.length;b++)a.push(this.h[this.g[b]]);return a};h.Fa=function(){Eg(this);return this.g.concat()};
h.ka=function(a){return this.has(a)};h.has=function(a){return Fg(this.h,a)};h.Va=function(a){for(var b=0;b<this.g.length;b++){var c=this.g[b];if(Fg(this.h,c)&&this.h[c]==a)return!0}return!1};var Gg=function(a){a.h={};a.g.length=0;a.size=0;a.j=0},Eg=function(a){if(a.size!=a.g.length){for(var b=0,c=0;b<a.g.length;){var d=a.g[b];Fg(a.h,d)&&(a.g[c++]=d);b++}a.g.length=c}if(a.size!=a.g.length){var e={};for(c=b=0;b<a.g.length;)d=a.g[b],Fg(e,d)||(a.g[c++]=d,e[d]=1),b++;a.g.length=c}};h=I.prototype;
h.get=function(a,b){return Fg(this.h,a)?this.h[a]:b};h.set=function(a,b){Fg(this.h,a)||(this.size+=1,this.g.push(a),this.j++);this.h[a]=b};h.forEach=function(a,b){for(var c=this.Fa(),d=0;d<c.length;d++){var e=c[d],f=this.get(e);a.call(b,f,e,this)}};h.keys=function(){return Dg(this.ya(!0)).h()};h.values=function(){return Dg(this.ya(!1)).h()};h.entries=function(){var a=this;return wg(this.keys(),function(b){return[b,a.get(b)]})};
h.ya=function(a){Eg(this);var b=0,c=this.j,d=this,e=new yg;e.sb=function(){if(c!=d.j)throw Error("The map has changed since the iterator was created");if(b>=d.g.length)throw xg;var f=d.g[b++];return a?f:d.h[f]};return e};var Fg=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};function J(a){a&&"function"==typeof a.dispose&&a.dispose()};function Hg(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Ga(d)?Hg.apply(null,d):J(d)}};var K=function(){this.ha=this.ha;this.$=this.$};K.prototype.ha=!1;K.prototype.isDisposed=function(){return this.ha};K.prototype.dispose=function(){this.ha||(this.ha=!0,this.C())};var Ig=function(a,b){b=Na(J,b);a.ha?b():(a.$||(a.$=[]),a.$.push(b))};K.prototype.C=function(){if(this.$)for(;this.$.length;)this.$.shift()()};var Jg=function(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=this.h=!1};Jg.prototype.stopPropagation=function(){this.h=!0};Jg.prototype.j=function(){this.defaultPrevented=!0};var Kg=function(a){a.j()};var Lg=function(){if(!n.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{n.addEventListener("test",Ca,b),n.removeEventListener("test",Ca,b)}catch(c){}return a}();var Ng=function(a,b){Jg.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.da=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(bd){a:{try{Vc(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Mg[a.pointerType]||"";this.state=a.state;this.da=a;a.defaultPrevented&&Ng.J.j.call(this)}};v(Ng,Jg);var Mg=Me({2:"touch",3:"pen",4:"mouse"});Ng.prototype.stopPropagation=function(){Ng.J.stopPropagation.call(this);this.da.stopPropagation?this.da.stopPropagation():this.da.cancelBubble=!0};
Ng.prototype.j=function(){Ng.J.j.call(this);var a=this.da;a.preventDefault?a.preventDefault():a.returnValue=!1};Ng.prototype.de=function(){return this.da};var Og="closure_listenable_"+(1E6*Math.random()|0),Pg=function(a){return!(!a||!a[Og])};var Qg=0;var Rg=function(a,b,c,d,e){this.listener=a;this.g=null;this.src=b;this.type=c;this.capture=!!d;this.Qa=e;this.key=++Qg;this.Za=this.kb=!1},Sg=function(a){a.Za=!0;a.listener=null;a.g=null;a.src=null;a.Qa=null};var Tg=function(a){this.src=a;this.g={};this.h=0};Tg.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=Ug(a,b,d,e);-1<g?(b=a[g],c||(b.kb=!1)):(b=new Rg(b,this.src,f,!!d,e),b.kb=c,a.push(b));return b};
var Vg=function(a,b){var c=b.type;if(!(c in a.g))return!1;var d=sb(a.g[c],b);d&&(Sg(b),0==a.g[c].length&&(delete a.g[c],a.h--));return d},Wg=function(a,b){b=b&&b.toString();var c=0,d;for(d in a.g)if(!b||d==b){for(var e=a.g[d],f=0;f<e.length;f++)++c,Sg(e[f]);delete a.g[d];a.h--}},Xg=function(a,b,c,d,e){a=a.g[b.toString()];b=-1;a&&(b=Ug(a,c,d,e));return-1<b?a[b]:null},Yg=function(a,b){var c=void 0!==b,d=c?b.toString():"";return Eb(a.g,function(e){for(var f=0;f<e.length;++f)if(!c||e[f].type==d)return!0;
return!1})},Ug=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.Za&&f.listener==b&&f.capture==!!c&&f.Qa==d)return e}return-1};var Zg="closure_lm_"+(1E6*Math.random()|0),$g={},ah=0,ch=function(a,b,c,d,e){if(d&&d.once)return bh(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)ch(a,b[f],c,d,e);return null}c=dh(c);return Pg(a)?a.Ka(b,c,t(d)?!!d.capture:!!d,e):eh(a,b,c,!1,d,e)},eh=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=t(e)?!!e.capture:!!e,l=fh(a);l||(a[Zg]=l=new Tg(a));c=l.add(b,c,d,g,f);if(c.g)return c;d=gh();c.g=d;d.src=a;d.listener=c;if(a.addEventListener)Lg||(e=g),void 0===e&&(e=!1),
a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(hh(b.toString()),d);else if(a.addListener&&a.removeListener)x("change"===b,"MediaQueryList only has a change event"),a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");ah++;return c},gh=function(){var a=ih,b=function(c){return a.call(b.src,b.listener,c)};return b},bh=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)bh(a,b[f],c,d,e);return null}c=dh(c);return Pg(a)?a.s.add(String(b),
c,!0,t(d)?!!d.capture:!!d,e):eh(a,b,c,!0,d,e)},jh=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)jh(a,b[f],c,d,e);else d=t(d)?!!d.capture:!!d,c=dh(c),Pg(a)?a.Sa(b,c,d,e):a&&(a=fh(a))&&(b=Xg(a,b,c,d,e))&&kh(b)},kh=function(a){if("number"===typeof a||!a||a.Za)return!1;var b=a.src;if(Pg(b))return Vg(b.s,a);var c=a.type,d=a.g;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(hh(c),d):b.addListener&&b.removeListener&&b.removeListener(d);ah--;(c=
fh(b))?(Vg(c,a),0==c.h&&(c.src=null,b[Zg]=null)):Sg(a);return!0},lh=function(a,b,c,d,e){c=dh(c);d=!!d;return Pg(a)?Xg(a.s,String(b),c,d,e):a?(a=fh(a))?Xg(a,b,c,d,e):null:null},mh=function(a,b){if(Pg(a))return Yg(a.s,void 0!==b?String(b):void 0);a=fh(a);return!!a&&Yg(a,b)},hh=function(a){return a in $g?$g[a]:$g[a]="on"+a},ih=function(a,b){if(a.Za)a=!0;else{b=new Ng(b,this);var c=a.listener,d=a.Qa||a.src;a.kb&&kh(a);a=c.call(d,b)}return a},fh=function(a){a=a[Zg];return a instanceof Tg?a:null},nh="__closure_events_fn_"+
(1E9*Math.random()>>>0),dh=function(a){x(a,"Listener can not be null.");if("function"===typeof a)return a;x(a.handleEvent,"An object listener must have handleEvent method.");a[nh]||(a[nh]=function(b){return a.handleEvent(b)});return a[nh]};var L=function(){K.call(this);this.s=new Tg(this);this.Oe=this;this.hb=null};v(L,K);L.prototype[Og]=!0;h=L.prototype;h.Ub=function(a){this.hb=a};h.addEventListener=function(a,b,c,d){ch(this,a,b,c,d)};h.removeEventListener=function(a,b,c,d){jh(this,a,b,c,d)};
h.dispatchEvent=function(a){oh(this);var b=this.hb;if(b){var c=[];for(var d=1;b;b=b.hb)c.push(b),x(1E3>++d,"infinite loop")}b=this.Oe;d=a.type||a;if("string"===typeof a)a=new Jg(a,b);else if(a instanceof Jg)a.target=a.target||b;else{var e=a;a=new Jg(d,b);Ob(a,e)}e=!0;if(c)for(var f=c.length-1;!a.h&&0<=f;f--){var g=a.g=c[f];e=ph(g,d,!0,a)&&e}a.h||(g=a.g=b,e=ph(g,d,!0,a)&&e,a.h||(e=ph(g,d,!1,a)&&e));if(c)for(f=0;!a.h&&f<c.length;f++)g=a.g=c[f],e=ph(g,d,!1,a)&&e;return e};
h.C=function(){L.J.C.call(this);this.s&&Wg(this.s,void 0);this.hb=null};h.Ka=function(a,b,c,d){oh(this);return this.s.add(String(a),b,!1,c,d)};h.Sa=function(a,b,c,d){var e=this.s;a=String(a).toString();if(a in e.g){var f=e.g[a];b=Ug(f,b,c,d);-1<b?(Sg(f[b]),tb(f,b),0==f.length&&(delete e.g[a],e.h--),e=!0):e=!1}else e=!1;return e};
var ph=function(a,b,c,d){b=a.s.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.Za&&g.capture==c){var l=g.listener,m=g.Qa||g.src;g.kb&&Vg(a.s,g);e=!1!==l.call(m,d)&&e}}return e&&!d.defaultPrevented},oh=function(a){x(a.s,"Event target is not initialized. Did you call the superclass (goog.events.EventTarget) constructor?")};var qh=function(a,b){L.call(this);this.l=a||1;this.j=b||n;this.o=u(this.A,this);this.v=Oa()};v(qh,L);qh.prototype.h=!1;qh.prototype.g=null;qh.prototype.A=function(){if(this.h){var a=Oa()-this.v;0<a&&a<.8*this.l?this.g=this.j.setTimeout(this.o,this.l-a):(this.g&&(this.j.clearTimeout(this.g),this.g=null),this.dispatchEvent("tick"),this.h&&(rh(this),this.start()))}};qh.prototype.start=function(){this.h=!0;this.g||(this.g=this.j.setTimeout(this.o,this.l),this.v=Oa())};
var rh=function(a){a.h=!1;a.g&&(a.j.clearTimeout(a.g),a.g=null)};qh.prototype.C=function(){qh.J.C.call(this);rh(this);delete this.j};var sh=function(a,b,c){if("function"===typeof a)c&&(a=u(a,c));else if(a&&"function"==typeof a.handleEvent)a=u(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:n.setTimeout(a,b||0)};var th={},uh={},vh=new Ke(.001,"");vh.g="load";var wh=new Ke(.1,"");wh.g="fail";var xh=new I;xh.set("enabler","enabler");xh.set("dcmenabler","dcm");xh.set("video","vid");xh.set("configurable","cfg");xh.set("configurablefiller","cfg_fill");xh.set("layouts","lay");xh.set("layoutsfiller","lay_fill");xh.set("gdn","gdn");xh.set("rad_ui_video","rad");
var yh=function(){for(var a=document.getElementsByTagName("script"),b=0;b<a.length;b++){var c=a[b];if(c.src||c.getAttribute("src"))if(c=c.src||c.getAttribute("src"),/Enabler/.test(c))return c.substring(0,c.lastIndexOf("/")+1)}return""},zh=function(a){a=th[a];return void 0!==a&&1<=a},Ah=function(a){return[yh(),"dev_studio_01_247_",[a,"module"].join(""),".js"].join("")};q("goog.exportSymbol",function(a,b,c){q(a,b,c)},this);
var Bh=function(a){a+="goog.exportSymbol('studioLoader.context.evalInContext', "+Bh.toString()+");";eval(a)},Ch=function(a,b){b=xh.get(b)||"unknown";a.l=b;a=a.j();!$b(a)&&B(1)&&(Fd(document,"IMG").src=a)},Dh=function(a,b){th[a]=2;Ch(vh,a);b="number"===typeof b?b:2;for(var c=Ah(a),d=ug(rf(c)),e=0;e<b;++e)d=d.then(void 0,function(){return ug(rf(c))});return d.then(function(){th[a]=3},function(){Ch(wh,a);return Qf()})},Eh=function(a){if(zh(a))return uh[a];th[a]=1;for(var b=[],c=DEPS_GRAPH?DEPS_GRAPH[[a,
"module"].join("")]:[],d=c.length-1;0<=d;d--){var e=c[d].replace(/module$/,"");if(e==a)break;zh(e)?b.push(uh[e]):b.push(Eh(e))}b=Tf(b).then(Na(Dh,a,2));return uh[a]=b},Fh=function(a,b){a=Eh(a);"function"===typeof b&&(a=a.then(b));Vf(a,Ca)};q("studioLoader.context.evalInContext",Bh,void 0);var Ub=new bb($a,"https://tpc.googlesyndication.com/sodar/%{basename}.js");function Gh(a){var b=window,c=!0;c=void 0===c?!1:c;new Promise(function(d,e){function f(){var l;g.onload=null;g.onerror=null;null===(l=g.parentElement)||void 0===l?void 0:l.removeChild(g)}var g=b.document.createElement("script");g.onload=function(){f();d()};g.onerror=function(){f();e(void 0)};g.type="text/javascript";g.src=Pc(a);Qc(g);c&&"complete"!==b.document.readyState?Sc(b,function(){b.document.body.appendChild(g)}):b.document.body.appendChild(g)})};function Hh(a){return Qa(this,function c(){var d,e,f,g,l,m;return wa(c,function(p){switch(p.g){case 1:d="https://pagead2.googlesyndication.com/getconfig/sodar?sv=200&tid="+a.g+("&tv="+a.h+"&st=")+a.Ha;e=void 0;p.s=2;var r=Ih(d);p.g=4;return{value:r};case 4:e=p.o;p.g=3;p.s=0;break;case 2:p.s=0,p.j=null;case 3:if(!e)return p.return(void 0);f=a.rb||e.sodar_query_id;g=void 0===e.rc_enable?"n":e.rc_enable;l=void 0===e.bg_snapshot_delay_ms?"0":e.bg_snapshot_delay_ms;m=void 0===e.is_gen_204?"1":e.is_gen_204;
return f&&e.bg_hash_basename&&e.bg_binary?p.return({context:a.j,Od:e.bg_hash_basename,Nd:e.bg_binary,Ee:a.g+"_"+a.h,rb:f,Ha:a.Ha,pb:g,xb:l,ob:m}):p.return(void 0)}})})}var Ih=function(a){return new Promise(function(b,c){var d=new XMLHttpRequest;d.onreadystatechange=function(){d.readyState===d.DONE&&(200<=d.status&&300>d.status?b(JSON.parse(d.responseText)):c())};d.open("GET",a,!0);d.send()})};
function Jh(){var a=Kh();Qa(this,function c(){var d;return wa(c,function(e){if(1==e.g){var f=Hh(a);e.g=2;return{value:f}}if(d=e.o){f="sodar2";f=void 0===f?"sodar2":f;var g=window,l=g.GoogleGcLKhOms;l&&"function"===typeof l.push||(l=g.GoogleGcLKhOms=[]);var m={};l.push((m._ctx_=d.context,m._bgv_=d.Od,m._bgp_=d.Nd,m._li_=d.Ee,m._jk_=d.rb,m._st_=d.Ha,m._rc_=d.pb,m._dl_=d.xb,m._g2_=d.ob,m));if(l=g.GoogleDX5YKUSk)g.GoogleDX5YKUSk=void 0,l[1]();f=Yb({basename:f});Gh(f)}return e.return(d)})})};x(!0);var Lh=Lc(),Mh=Tc()||z("iPod"),Nh=z("iPad"),Oh=z("Android")&&!(Mc()||Lc()||z("Opera")||z("Silk")),Ph=Mc(),Qh=z("Safari")&&!(Mc()||z("Coast")||z("Opera")||z("Edge")||z("Edg/")||z("OPR")||Lc()||z("Silk")||z("Android"))&&!Uc();var Rh={},Sh=null,Th=function(a,b){x(Ga(a),"encodeByteArray takes an array as a parameter");void 0===b&&(b=0);if(!Sh){Sh={};for(var c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;5>e;e++){var f=c.concat(d[e].split(""));Rh[e]=f;for(var g=0;g<f.length;g++){var l=f[g],m=Sh[l];void 0===m?Sh[l]=g:x(m===g)}}}b=Rh[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=f=0;f<a.length-2;f+=3){m=a[f];var p=a[f+1];l=a[f+2];g=b[m>>2];m=b[(m&3)<<
4|p>>4];p=b[(p&15)<<2|l>>6];l=b[l&63];c[e++]=""+g+m+p+l}g=0;l=d;switch(a.length-f){case 2:g=a[f+1],l=b[(g&15)<<2]||d;case 1:a=a[f],c[e]=""+b[a>>2]+b[(a&3)<<4|g>>4]+l+d}return c.join("")};var Uh="function"===typeof Uint8Array;function Vh(a,b,c){if(null!=a)return"object"===typeof a?Uh&&a instanceof Uint8Array?c(a):Wh(a,b,c):b(a)}function Wh(a,b,c){if(Array.isArray(a)){for(var d=Array(a.length),e=0;e<a.length;e++)d[e]=Vh(a[e],b,c);Array.isArray(a)&&a.Be&&Xh(d);return d}d={};for(e in a)d[e]=Vh(a[e],b,c);return d}
var Yh=function(a){return"number"===typeof a?isFinite(a)?a:String(a):a},Zh={Be:{value:!0,configurable:!0}},Xh=function(a){Array.isArray(a)&&!Object.isFrozen(a)&&Object.defineProperties(a,Zh);return a},$h=Symbol("exempted jspb subclass"),ai=Symbol("generated by jspb");var bi;var ci=function(a,b,c){Za(this,ci,"The message constructor should only be used by subclasses");x(this.constructor!==ci,"Message is an abstract class and cannot be directly constructed");if(!0!==this[$h]){x(!0===this[ai],"Message can only be subclassed by proto gencode.");var d=Object.getPrototypeOf(x(Object.getPrototypeOf(this)));x(d.hasOwnProperty(ai),"Generated jspb classes should not be extended")}d=bi;bi=null;a||(a=d);d=this.constructor.kg;a||(a=d?[d]:[]);this.l=d?0:-1;this.h=null;this.g=a;a:{d=
this.g.length;a=d-1;if(d&&(d=this.g[a],null!==d&&"object"===typeof d&&d.constructor===Object)){this.s=a-this.l;this.j=d;break a}void 0!==b&&-1<b?(this.s=Math.max(b,a+1-this.l),this.j=null):this.s=Number.MAX_VALUE}if(c)for(b=0;b<c.length;b++)a=c[b],a<this.s?(a+=this.l,(d=this.g[a])?Xh(d):this.g[a]=di):(ei(this),(d=this.j[a])?Xh(d):this.j[a]=di)},di=Object.freeze(Xh([])),ei=function(a){var b=a.s+a.l;a.g[b]||(a.j=a.g[b]={})},fi=function(a,b,c){return-1===b?null:(void 0===c?0:c)||b>=a.s?a.j?a.j[b]:void 0:
a.g[b+a.l]},gi=function(a,b){a.h||(a.h={});if(!a.h[1]){var c=fi(a,1,!1);c&&(a.h[1]=new b(c))}return a.h[1]};ci.prototype.toJSON=function(){var a=hi(this,!1);return Wh(a,Yh,Th)};var hi=function(a,b){if(a.h)for(var c in a.h){var d=a.h[c];if(Array.isArray(d))for(var e=0;e<d.length;e++)d[e]&&hi(d[e],b);else d&&hi(d,b)}return a.g};ci.prototype.toString=function(){return hi(this,!1).toString()};var ii=function(){ci.apply(this,arguments)};k(ii,ci);ii.prototype[ai]=!0;var ji=function(a){this.g=a.g;this.h=a.h;this.j=a.j;this.rb=a.rb;this.Ha=a.Ha;this.pb=a.pb;this.xb=a.xb;this.ob=a.ob},ki=function(){this.g="xfad";this.h="01_247";this.j="cr";this.Ha="env";this.pb="n";this.xb="0";this.ob="1"},Kh=function(){var a=new ki;a.Ha="int";return new ji(a)};var li=function(a){ii.call(this,a)};k(li,ii);var mi=function(a){ii.call(this,a)};k(mi,ii);var ni=function(){this.g=new I;this.size=0},oi=function(a){var b=typeof a;return"object"==b&&a||"function"==b?"o"+Ka(a):b.substr(0,1)+a};ni.prototype.la=function(){return this.g.size};ni.prototype.add=function(a){this.g.set(oi(a),a);this.size=this.g.size};var qi=function(a){var b=pi;a=Vd(a);for(var c=a.length,d=0;d<c;d++)b.add(a[d]);b.size=b.g.size};h=ni.prototype;h.has=function(a){return this.g.ka(oi(a))};h.contains=function(a){return this.g.ka(oi(a))};h.Z=function(){return this.g.Z()};
h.values=function(){return this.g.values()};h.ya=function(){return this.g.ya(!1)};ni.prototype[Symbol.iterator]=function(){return this.values()};q("studio.common.Feature.Type",{$f:1,SDK_EVENT_FORWARDER:2,RL_EVENT_FORWARDER:3},void 0);var pi=new ni;q("studio.common.Feature.hasFeature",function(a){return pi.has(a)},void 0);q("studio.common.Feature.hasFeatures",function(a){var b=pi;a:{var c=b.contains;if("function"==typeof a.every)a=a.every(c,b);else if(Ga(a)||"string"===typeof a)a=Array.prototype.every.call(a,c,b);else{for(var d=Wd(a),e=Vd(a),f=e.length,g=0;g<f;g++)if(!c.call(b,e[g],d&&d[g],a)){a=!1;break a}a=!0}}return a},void 0);var ri=function(a,b){this.h=a;this.g=null!=b?b:0},si=function(){var a=window;return a.innerWidth>a.innerHeight?"landscape":"portrait"},ti=function(){return"onorientationchange"in window};ri.prototype.le=function(){return this.h};ri.prototype.ke=function(){return this.g};ri.prototype.toString=function(){return this.h};q("studio.common.Orientation",ri,void 0);ri.prototype.getDegrees=ri.prototype.ke;ri.prototype.getMode=ri.prototype.le;ri.Mode={PORTRAIT:"portrait",LANDSCAPE:"landscape"};function ui(a,b){return b?a.replace("[rm_exit_id]",b):a}function vi(a){$b(sd(a))||"market"!=(a.match(Pd)[1]||null)||(a=a.match(Pd),a="https://play.google.com/store/apps/details"+Od(null,null,null,null,a[5],a[6],a[7]));return a};var wi={CREATIVETOOLSET_CONFIG:"creativeToolsetConfig",CREATIVETOOLSET_INTERNALS:"creativeToolsetInternals",CREATIVETOOLSET_INTERNALS_GEN204:"creativeToolsetInternalsGen204",CREATIVE_REPORTER:"creativeReporter",CREATIVE_INNOVATION:"gcreativeinnovation",GOOGLE_AFMA_SUPPORT:"googleAfmaSupport"};q("studio.common.WhitelistedExternalObject",wi,void 0);var xi={};var M=function(a){K.call(this);this.v=a;this.j={}};v(M,K);
var yi=[],N=function(a,b,c,d,e,f){Array.isArray(c)||(c&&(yi[0]=c.toString()),c=yi);for(var g=0;g<c.length;g++){var l=ch(b,c[g],d||a.handleEvent,e||!1,f||a.v||a);if(!l)break;a.j[l.key]=l}},zi=function(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)zi(a,b,c[g],d,e,f);else(b=bh(b,c,d||a.handleEvent,e,f||a.v||a))&&(a.j[b.key]=b)},Ai=function(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)Ai(a,b,c[g],d,e,f);else if(b=lh(b,c,d||a.handleEvent,t(e)?!!e.capture:!!e,f||a.v||a))kh(b),
delete a.j[b.key]},Bi=function(a){Cb(a.j,function(b,c){this.j.hasOwnProperty(c)&&kh(b)},a);a.j={}};M.prototype.C=function(){M.J.C.call(this);Bi(this)};M.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};var Ci={1:"NativeMessagingTransport",2:"DirectTransport"},Di=["pu","lru","pru","lpu","ppu"],Fi=function(){for(var a=10,b=Ei,c=b.length,d="";0<a--;)d+=b.charAt(Math.floor(Math.random()*c));return d},Ei="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",O=C("goog.net.xpc");var Gi=function(a){K.call(this);this.S=a||zd()};v(Gi,K);Gi.prototype.bb=0;var Hi=function(a){return Dd(a.S.g)};Gi.prototype.G=function(){return Ci[String(this.bb)]||""};var Ii=function(a,b){Gi.call(this,b);this.g=a;this.B=new M(this);Ig(this,this.B);this.o=new qh(100,Hi(this));Ig(this,this.o);this.l=new G;this.s=new G;this.h=new G;this.A=Fi();this.v=null;this.j={};this.D=this.g.name;this.g.Ia(this.g.name+"_"+this.g.W());this.F=!1;mg(this.h,this.l);mg(this.h,this.s);lg(this.h,this.nd,this);this.h.L(!0);N(this.B,this.o,"tick",this.Yb,void 0);E(O,"DirectTransport created. role="+this.g.W())};k(Ii,Gi);
var Li=function(a){var b=new Ji(a.channelName,a.service,a.payload);a=b.g;var c=b.j;b=b.h;nf(O,"messageReceived: channel="+a+", service="+c+", payload="+b);var d=xi[a];if(d)return d.ma(c,b),!0;d=Ki(b)[0];for(var e in xi){var f=xi[e];if(1==f.W()&&!f.isConnected()&&"tp"==c&&"SETUP"==d)return f.Ia(a),f.ma(c,b),!0}E(O,"channel name mismatch; message ignored.");return!1};h=Ii.prototype;
h.ab=function(a){a=Ki(a);var b=a[1];switch(a[0]){case "SETUP_ACK":this.l.g||this.l.L(!0);break;case "SETUP":this.U("tp","SETUP_ACK"),this.s.g||this.s.L(!0),null!=this.v&&this.v!=b&&(E(O,"Sending SETUP and changing peer ID to: "+b),this.U("tp","SETUP,"+this.A)),this.v=b}};h.connect=function(){var a=Hi(this);if(a){var b=Ka(a);0==(Mi[b]||0)&&null==Aa("crosswindowmessaging.channel",a)&&q("crosswindowmessaging.channel",Li,a);Mi[b]++;this.F=!0;this.Yb()}else nf(O,"connect(): no window to initialize.")};
h.Yb=function(){this.g.isConnected()?rh(this.o):(this.o.start(),this.U("tp","SETUP,"+this.A))};h.U=function(a,b){this.g.aa?(a=new Ji(this.D+"_"+(0==this.g.W()?1:0),a,b),this.g.g.directSyncMode?this.Xb(a):this.j[Ka(a)]=sh(u(this.Xb,this,a),0)):nf(O,"send(): window not ready")};
h.Xb=function(a){var b=Ka(a);this.j[b]&&delete this.j[b];try{var c=Aa("crosswindowmessaging.channel",this.g.aa)}catch(d){D(O,"Can't access other window, ignoring.",d);return}if(null===c)D(O,"Peer window had no global function.");else try{c({channelName:a.g,service:a.j,payload:a.h}),E(O,"send(): channelName="+a.g+" service="+a.j+" payload="+a.h)}catch(d){D(O,"Error performing call, ignoring.",d)}};h.nd=function(){Ni(this.g,0)};
h.C=function(){if(this.F){var a=Hi(this),b=Ka(a);1==--Mi[b]&&q("crosswindowmessaging.channel",null,a)}this.j&&(Cb(this.j,function(c){n.clearTimeout(c)}),this.j=null);this.l&&(this.l.cancel(),delete this.l);this.s&&(this.s.cancel(),delete this.s);this.h&&(this.h.cancel(),delete this.h);Gi.prototype.C.call(this)};var Ki=function(a){a=a.split(",");a[1]=a[1]||null;return a},Mi={};Ii.prototype.bb=2;var Ji=function(a,b,c){this.g=a;this.j=b;this.h=c};var Oi=function(a,b,c,d,e){Gi.call(this,c);this.j=a;this.h=e||2;x(1<=this.h);x(2>=this.h);this.K=b||"*";this.D=new M(this);this.v=new qh(100,Hi(this));this.B=!!d;this.s=new G;this.o=new G;this.l=new G;this.X=Fi();this.F=null;this.B?1==this.j.W()?mg(this.l,this.s):mg(this.l,this.o):(mg(this.l,this.s),2==this.h&&mg(this.l,this.o));lg(this.l,this.oa,this);this.l.L(!0);this.pa=Zc&&!jd();N(this.D,this.v,"tick",this.M,void 0);E(O,"NativeMessagingTransport created.  protocolVersion="+this.h+", oneSidedHandshake="+
this.B+", role="+this.j.W())};k(Oi,Gi);
var Qi=function(a){var b=a.da.data;if("string"!==typeof b)return!1;var c=b.indexOf("|"),d=b.indexOf(":");if(-1==c||-1==d)return!1;var e=b.substring(0,c);c=b.substring(c+1,d);b=b.substring(d+1);nf(O,"messageReceived: channel="+e+", service="+c+", payload="+b);if(d=xi[e])return d.ma(c,b,a.da.origin),!0;d=Pi(b)[0];for(var f in xi){var g=xi[f];if(1==g.W()&&!g.isConnected()&&"tp"==c&&("SETUP"==d||"SETUP_NTPV2"==d)&&g.Xa(a.da.origin))return g.Ia(e),g.ma(c,b),!0}E(O,'channel name mismatch; message ignored"');
return!1};Oi.prototype.ab=function(a){var b=Pi(a);a=b[1];switch(b[0]){case "SETUP_ACK":Ri(this,1);this.s.g||this.s.L(!0);break;case "SETUP_ACK_NTPV2":2==this.h&&(Ri(this,2),this.s.g||this.s.L(!0));break;case "SETUP":Ri(this,1);Si(this,1);break;case "SETUP_NTPV2":2==this.h&&(b=this.g,Ri(this,2),Si(this,2),1!=b&&null==this.F||this.F==a||(E(O,"Sending SETUP and changing peer ID to: "+a),Ti(this)),this.F=a)}};
var Ti=function(a){x(!(1==a.h&&2==a.g));2!=a.h||null!=a.g&&2!=a.g||a.U("tp","SETUP_NTPV2,"+a.X);null!=a.g&&1!=a.g||a.U("tp","SETUP")},Si=function(a,b){x(1!=a.h||2!=b,"Shouldn't try to send a v2 setup ack in v1 mode.");if(2!=a.h||null!=a.g&&2!=a.g||2!=b){if(null!=a.g&&1!=a.g||1!=b)return;a.U("tp","SETUP_ACK")}else a.U("tp","SETUP_ACK_NTPV2");a.o.g||a.o.L(!0)},Ri=function(a,b){b>a.g&&(a.g=b);1==a.g&&(a.o.g||a.B||a.o.L(!0),a.F=null)};
Oi.prototype.connect=function(){var a=Hi(this),b=Ka(a),c=Ui[b];"number"!==typeof c&&(c=0);0==c&&ch(a.postMessage?a:a.document,"message",Qi,!1,Oi);Ui[b]=c+1;this.H=!0;this.M()};Oi.prototype.M=function(){var a=0==this.j.W();this.B&&a||this.j.isConnected()||this.isDisposed()?rh(this.v):(this.v.start(),Ti(this))};
var Vi=function(a,b,c){var d=a.j.aa,e=a.j.name;a.A=0;try{var f=d.postMessage?d:d.document;f.postMessage?(f.postMessage(e+"|"+b+":"+c,a.K),nf(O,"send(): service="+b+" payload="+c+" to hostname="+a.K)):D(O,"Peer window had no postMessage function.")}catch(g){D(O,"Error performing postMessage, ignoring.",g)}};Oi.prototype.U=function(a,b){var c=this;this.j.aa?this.pa?this.A=sh(function(){return void Vi(c,a,b)},0):Vi(this,a,b):nf(O,"send(): window not ready")};
Oi.prototype.oa=function(){Ni(this.j,1==this.h||1==this.g?200:void 0)};Oi.prototype.C=function(){if(this.H){var a=Hi(this),b=Ka(a),c=Ui[b];Ui[b]=c-1;1==c&&jh(a.postMessage?a:a.document,"message",Qi,!1,Oi)}this.A&&(n.clearTimeout(this.A),this.A=0);J(this.D);delete this.D;J(this.v);delete this.v;this.s.cancel();delete this.s;this.o.cancel();delete this.o;this.l.cancel();delete this.l;delete this.U;Gi.prototype.C.call(this)};var Pi=function(a){a=a.split(",");a[1]=a[1]||null;return a};
Oi.prototype.g=null;Oi.prototype.H=!1;Oi.prototype.bb=1;var Ui={};Oi.prototype.A=0;var Wi=function(a,b,c){K.call(this);this.h=a;this.l=b||0;this.j=c;this.s=u(this.o,this)};v(Wi,K);Wi.prototype.g=0;Wi.prototype.C=function(){Wi.J.C.call(this);0!=this.g&&n.clearTimeout(this.g);this.g=0;delete this.h;delete this.j};Wi.prototype.start=function(a){0!=this.g&&n.clearTimeout(this.g);this.g=0;this.g=sh(this.s,void 0!==a?a:this.l)};Wi.prototype.o=function(){this.g=0;this.h&&this.h.call(this.j)};var Zi=function(a){var b=[];Xi(new Yi,a,b);return b.join("")},Yi=function(){},Xi=function(a,b,c){if(null==b)c.push("null");else{if("object"==typeof b){if(Array.isArray(b)){var d=b;b=d.length;c.push("[");for(var e="",f=0;f<b;f++)c.push(e),Xi(a,d[f],c),e=",";c.push("]");return}if(b instanceof String||b instanceof Number||b instanceof Boolean)b=b.valueOf();else{c.push("{");e="";for(d in b)Object.prototype.hasOwnProperty.call(b,d)&&(f=b[d],"function"!=typeof f&&(c.push(e),$i(d,c),c.push(":"),Xi(a,f,c),
e=","));c.push("}");return}}switch(typeof b){case "string":$i(b,c);break;case "number":c.push(isFinite(b)&&!isNaN(b)?String(b):"null");break;case "boolean":c.push(String(b));break;case "function":c.push("null");break;default:throw Error("Unknown type: "+typeof b);}}},aj={'"':'\\"',"\\":"\\\\","/":"\\/","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\u000b"},bj=/\uffff/.test("\uffff")?/[\\"\x00-\x1f\x7f-\uffff]/g:/[\\"\x00-\x1f\x7f-\xff]/g,$i=function(a,b){b.push('"',a.replace(bj,
function(c){var d=aj[c];d||(d="\\u"+(c.charCodeAt(0)|65536).toString(16).substr(1),aj[c]=d);return d}),'"')};var cj=function(){K.call(this);this.v={}};v(cj,K);h=cj.prototype;h.Zb=C("goog.messaging.AbstractChannel");h.connect=function(a){a&&a()};h.isConnected=function(){return!0};h.eb=function(a,b,c){this.v[a]={L:b,Rc:!!c}};h.Zc=function(a){this.o=a};
var dj=function(a,b,c){var d=a.v[b];d||(a.o?d={L:Na(a.o,b),Rc:t(c)}:(D(a.Zb,'Unknown service name "'+b+'"'),d=null));if(d){a:{var e=d.Rc;if(e&&"string"===typeof c)try{var f=JSON.parse(c);break a}catch(g){D(a.Zb,"Expected JSON payload for "+b+', was "'+c+'"');f=null;break a}else if(!e&&"string"!==typeof c){f=Zi(c);break a}f=c}a=f;null!=a&&d.L(a)}};cj.prototype.C=function(){cj.J.C.call(this);delete this.v;delete this.o};var fj=function(a,b){cj.call(this);for(var c=0,d;d=Di[c];c++)if(d in a&&!/^https?:\/\//.test(a[d]))throw Error("URI "+a[d]+" is invalid for field "+d);this.g=a;this.name=this.g.cn||Fi();this.j=b||zd();this.l=[];this.s=new M(this);a.lpu=a.lpu||Rd(Dd(this.j.g).location.href)+"/robots.txt";a.ppu=a.ppu||Rd(a.pu||"")+"/robots.txt";xi[this.name]=this;lh(window,"unload",ej)||bh(window,"unload",ej);E(O,"CrossPageChannel created: "+this.name)};k(fj,cj);fj.prototype.isConnected=function(){return 2==this.La};
fj.prototype.connect=function(a){this.h=a||Ca;3==this.La&&(this.La=1);this.Ba?lg(this.Ba,this.A):this.A()};
fj.prototype.A=function(){E(O,"continueConnection_()");this.Ba=null;this.g.ifrid&&(this.cb=this.j.getElement(this.g.ifrid));if(this.cb){var a=this.cb.contentWindow;a||(a=window.frames[this.g.ifrid]);this.aa=a}if(!this.aa){if(window==window.top)throw Error("CrossPageChannel: Can't connect, peer window-object not set.");this.aa=window.parent}if(!this.ia){this.g.tp||(this.g.tp="function"===typeof document.postMessage||"function"===typeof window.postMessage||Zc&&window.postMessage?1:0);if("function"===
typeof this.g.tp)this.ia=new this.g.tp(this,this.j);else switch(this.g.tp){case 1:this.ia=new Oi(this,this.g.ph,this.j,!!this.g.osh,this.g.nativeProtocolVersion||2);break;case 2:if(a=this.aa)try{a=window.document.domain==this.aa.document.domain}catch(b){a=!1}a?this.ia=new Ii(this,this.j):E(O,"DirectTransport not supported for this window, peer window in different security context or not set yet.")}if(this.ia)E(O,"Transport created: "+this.ia.G());else throw Error("CrossPageChannel: No suitable transport found! You may try injecting a Transport constructor directly via the channel config object.");
}for(this.ia.connect();0<this.l.length;)this.l.shift()()};fj.prototype.close=function(){this.Ba&&(this.Ba.cancel(),this.Ba=null);this.l.length=0;Bi(this.s);this.La=3;J(this.ia);this.h=this.ia=null;J(this.va);this.va=null;E(O,'Channel "'+this.name+'" closed')};var Ni=function(a,b){a.isConnected()||a.va&&0!=a.va.g||(a.La=2,E(O,'Channel "'+a.name+'" connected'),J(a.va),void 0!==b?(a.va=new Wi(a.h,b),a.va.start()):(a.va=null,a.h()))};h=fj.prototype;
h.Ta=function(a,b){if(this.isConnected()){try{var c=!!this.aa&&!this.aa.closed}catch(e){c=!1}if(c){t(b)&&(b=Zi(b));c=this.ia;var d=c.U;gj.test(a)&&(a="%"+a);a=a.replace(/[%:|]/g,encodeURIComponent);d.call(c,a,b)}else mf(O,"Peer has disappeared."),this.close()}else mf(O,"Can't send. Channel not connected.")};
h.ma=function(a,b,c){this.Ba?this.l.push(u(this.ma,this,a,b,c)):this.Xa(c)?this.isDisposed()||3==this.La?D(O,"CrossPageChannel::xpcDeliver(): Channel closed."):a&&"tp"!=a?this.isConnected()?(a=a.replace(/%[0-9a-f]{2}/gi,decodeURIComponent),a=hj.test(a)?a.substring(1):a,dj(this,a,b)):E(O,"CrossPageChannel::xpcDeliver(): Not connected."):this.ia.ab(b):D(O,'Message received from unapproved origin "'+c+'" - rejected.')};
h.W=function(){var a=this.g.role;return"number"===typeof a?a:window.parent==this.aa?1:0};h.Ia=function(a){nf(O,"changing channel name to "+a);delete xi[this.name];this.name=a;xi[a]=this};h.Xa=function(a){var b=this.g.ph;return $b(sd(a))||$b(sd(b))||a==this.g.ph};h.C=function(){this.close();this.cb=this.aa=null;delete xi[this.name];J(this.s);delete this.s;cj.prototype.C.call(this)};var ej=function(){for(var a in xi)J(xi[a])},gj=RegExp("^%*tp$"),hj=RegExp("^%+tp$");h=fj.prototype;h.va=null;h.Ba=null;
h.ia=null;h.La=1;h.aa=null;h.cb=null;var ij={Gf:"devicemotion",Hf:"deviceorientation",Wf:"hostpageScroll",Lf:"enterViewport",Mf:"exitViewport",Af:"adLocation"},jj={},kj;for(kj in ij)jj[ij[kj]]=!0;var Q=function(a){Jg.call(this,a)};k(Q,Jg);Q.prototype.fa=function(a,b){this[a]=b;return this};q("studio.events.StudioEvent",Q,void 0);Q.prototype.addProperty=Q.prototype.fa;Q.INIT="init";Q.VISIBLE="visible";Q.HIDDEN="hidden";Q.VISIBILITY_CHANGE="visibilityChange";Q.VISIBILITY_CHANGE_WITH_INFO="visibilityChangeWithInfo";Q.EXIT="exit";Q.INTERACTION="interaction";Q.PAGE_LOADED="pageLoaded";Q.ORIENTATION="orientation";Q.ABOUT_TO_EXPAND="aboutToExpand";Q.EXPAND_START="expandStart";Q.EXPAND_FAILED="expandFailed";
Q.EXPAND_FINISH="expandFinish";Q.COLLAPSE_START="collapseStart";Q.COLLAPSE_FINISH="collapseFinish";Q.COLLAPSE="collapse";Q.FULLSCREEN_SUPPORT="fullscreenSupport";Q.HOSTPAGE_FEATURES_LOADED="hostpageFeaturesLoaded";Q.FULLSCREEN_DIMENSIONS="fullscreenDimensions";Q.FULLSCREEN_EXPAND_START="fullscreenExpandStart";Q.FULLSCREEN_EXPAND_FINISH="fullscreenExpandFinish";Q.FULLSCREEN_COLLAPSE_START="fullscreenCollapseStart";Q.FULLSCREEN_COLLAPSE_FINISH="fullscreenCollapseFinish";Q.HOSTPAGE_SCROLL="hostpageScroll";
Q.OPTIONAL_HOSTPAGE_SCROLL="optHostpageScroll";Q.SCROLL_INTERACTION="scrollInteraction";Q.ENTER_VIEWPORT="enterViewport";Q.OPTIONAL_ENTER_VIEWPORT="optEnterViewport";Q.EXIT_VIEWPORT="exitViewport";Q.OPTIONAL_EXIT_VIEWPORT="optExitViewport";Q.VIDEO_START="videoStart";var lj={},mj=(lj.optHostpageScroll="hostpageScroll",lj.optEnterViewport="enterViewport",lj.optExitViewport="exitViewport",lj);var nj=function(){M.call(this);this.g=new Map;this.l=this.o=!1;this.h=this.s=null;this.g.set("nx",null);this.g.set("ny",null);this.g.set("dim",null)};k(nj,M);nj.prototype.A=function(a){for(var b=[],c=0;c<arguments.length;++c)b[c-0]=arguments[c];return 2040>b.reduce(function(d,e){return d+e.length},0)};
nj.prototype.B=function(a){var b=a.clientX,c=a.clientY;a.changedTouches&&a.changedTouches[0]&&(b=a.changedTouches[0].clientX,c=a.changedTouches[0].clientY);this.g.set("nx",Math.round(b));this.g.set("ny",Math.round(c));this.l&&(this.h||(this.h=window.GoogleA13IjpGc),this.s=this.h&&"function"===typeof this.h.snapshotSync?this.h.snapshotSync():null)};nj.prototype.C=function(){this.o=!1;M.prototype.C.call(this)};var oj=function(a){this.g=a};oj.prototype.toString=function(){return(this.g&2?"b":"t")+(this.g&1?"r":"l")};q("studio.common.mde.Direction",oj,void 0);oj.Corner={gg:0,hg:1,Bf:2,Cf:3};var pj={TL:new oj(0),TR:new oj(1),BL:new oj(2),BR:new oj(3)},qj=Fb(pj);var rj={eg:"startExpandInternal",dg:"startCollapseInternal",Of:"finishCollapseInternal",zf:"aboutToExpandInternal",ag:"setAdVisibleInternal",bg:"setAdParameters",If:"dispatchEvent",cg:"setParameter",Vf:"getParameter",Uf:"fullscreenSupportInternal",Rf:"fullscreenDimensionsInternal",Tf:"fullscreenExpandStartInternal",Sf:"fullscreenExpandFinishInternal",Qf:"fullscreenCollapseStartInternal",Pf:"fullscreenCollapseFinishInternal",Xf:"invokeOnAllVideos",Yf:"livePreviewChannel",Jf:"dispatchPageLoaded"},sj=
{},tj;for(tj in rj)sj[rj[tj]]=!0;var uj=[/s0(qa)?\.2mdn\.net/,/^.*\.(prod|corp)\.google\.com/,/localhost/,/tpc\.googlesyndication\.com/,/secureframe\.doubleclick\.net/,/imasdk\.googleapis\.com/,/^.*dot-expandable-ad-tool\.appspot\.com/];function vj(){var a=location.hostname;return B(2)&&!B(16)?!1:pb(uj,function(b){return b.test(a)})};var wj=function(a){K.call(this);this.h=a;this.g={};this.h.Zc(u(this.l,this))};v(wj,K);wj.prototype.j=C("goog.messaging.MultiChannel");var yj=function(a,b){if(-1!=b.indexOf(":"))throw Error('Virtual channel name "'+b+'" should not contain colons');if(b in a.g)throw Error('Virtual channel "'+b+'" was already created for this multichannel.');var c=new xj(a,b);return a.g[b]=c};
wj.prototype.l=function(a,b){var c=a.match(/^([^:]*):(.*)/);if(c){var d=c[1];a=c[2];d in this.g?(c=this.g[d])?c.j?c.j(a,b):D(this.j,'Service "'+a+'" is not registered on virtual channel "'+d+'"'):D(this.j,'Virtual channel "'+d+' has been disposed, but a message was received for it: "'+a+'"'):D(this.j,'Virtual channel "'+d+' does not exist, but a message was received for it: "'+a+'"')}else D(this.j,'Invalid service name "'+a+'": no virtual channel specified')};
wj.prototype.C=function(){Cb(this.g,function(a){J(a)});J(this.h);delete this.g;delete this.h};var xj=function(a,b){K.call(this);this.h=a;this.g=b};v(xj,K);h=xj.prototype;h.od=C("goog.messaging.MultiChannel.VirtualChannel");h.connect=function(a){a&&a()};h.isConnected=function(){return!0};h.eb=function(a,b,c){this.h.h.eb(this.g+":"+a,u(this.uc,this,b),c)};h.Zc=function(a){this.j=u(this.uc,this,a)};
h.Ta=function(a,b){if(this.isDisposed())throw Error("#send called for disposed VirtualChannel.");this.h.h.Ta(this.g+":"+a,b)};h.uc=function(a,b){this.isDisposed()?D(this.od,'Virtual channel "'+this.g+'" received  a message after being disposed.'):a.apply({},Array.prototype.slice.call(arguments,1))};h.C=function(){this.h=this.h.g[this.g]=null};var zj=function(a){K.call(this);this.h=new wj(a);this.l={};this.j=yj(this.h,"private");this.v=yj(this.h,"public");this.j.eb("mics",u(this.H,this),!0)};v(zj,K);zj.prototype.M=0;zj.prototype.F=C("goog.messaging.RespondingChannel");zj.prototype.C=function(){J(this.h);delete this.h;delete this.v;delete this.j};var Aj=function(a,b,c,d){var e=a.M++;a.l[e]=d;d={signature:e};d.data=c;a.v.Ta(b,d)};
zj.prototype.H=function(a){var b=a.signature;a=a.data;b in this.l?((0,this.l[b])(a),delete this.l[b]):D(this.F,"Received signature is invalid")};var Bj=function(a,b,c){a.v.eb(b,u(a.K,a,c),!0)};zj.prototype.K=function(a,b){a=a(b.data);var c=b.signature;Pf(a).then(u(function(d){var e={};e.data=d;e.signature=c;this.j&&this.j.Ta("mics",e)},this))};var Cj=function(a,b){var c=this;a=void 0===a?"":a;b=void 0===b?null:b;this.g=this.o=this.A=null;this.B=!1;var d={},e=1,f=window.parent;if(null!=b)switch(b){case 3:f=window;case 2:e=2;d.directSyncMode=!0;break;case 4:f=window.parent.frames["goog-messaging-iframe"]}d.tp=e;d.role=1;d.nativeProtocolVersion=2;a&&(d.cn=a);kf(C("goog.net.xpc"),Oe);this.s=new fj(d);this.s.aa=f;zj.call(this,this.s);Bj(this,"general",function(g){a:{var l=g.methodName;g=g.args;if(l in sj){if(c.g){F.Pa("Invoking method: "+l+
" with args: "+g.join(", "));var m=c.g[l];"function"!==typeof m&&(m=c.g.defaultMessageHandler,g=[l,g]);if("function"===typeof m){l=m.apply(c.g,g);break a}}l=null}else l=void 0}return l})};v(Cj,zj);Cj.prototype.connect=function(a){this.B||vj()?this.o?(this.A=Oa(),this.s.connect(u(this.D,this,a))):F.$a("You must call setAssetUrl before connecting."):F.Pa("This class should only listen to messages when served by the rendering libraries.")};
Cj.prototype.D=function(a){R(this,"conduitInitialized",[this.o,Array.from(pi.values())]);var b={version:"01_247"};b.x=window.STUDIO_SDK_START||null;b.c=this.A;b.t=Oa();R(this,"recordTimings",[b]);a&&a()};var R=function(a,b,c,d){var e={};e.methodName=b;e.args=c&&Array.isArray(c)?c:[];Aj(a,"general",e,d||Ca)};q("studio.sdk.ContainerState",{COLLAPSING:"collapsing",COLLAPSED:"collapsed",EXPANDING:"expanding",EXPANDED:"expanded",FS_COLLAPSING:"fs_collapsing",FS_EXPANDING:"fs_expanding",FS_EXPANDED:"fs_expanded"},void 0);var Dj={NONE:0,LOG_ONLY:1};q("studio.sdk.ExitFlag",Dj,void 0);Dj.NONE=0;Dj.LOG_ONLY=1;var Ej={GET_CURRENT_POSITION:"getCurrentPosition",GET_DEFAULT_POSITION:"getDefaultPosition",GET_SCREEN_SIZE:"getScreenSize",CREATE_CALENDAR_EVENT:"createCalendarEvent",GET_MAX_SIZE:"getMaxSize",PLAY_VIDEO:"playVideo",STORE_PICTURE:"storePicture",SUPPORTS:"supports",USE_CUSTOM_CLOSE:"useCustomClose"};q("studio.sdk.MraidMethod",Ej,void 0);var Fj=function(){};q("studio.sdk.IEnabler",Fj,void 0);h=Fj.prototype;h.dd=function(){};h.reportManualClose=function(){};h.$c=function(){};h.fd=function(){};h.ed=function(){};h.isVisible=function(){};h.ra=function(){};h.isPageLoaded=function(){};h.isInitialized=function(){};h.oc=function(){};h.getParameter=function(){};h.exit=function(){};h.Lb=function(){};h.zc=function(){};h.counter=function(){};h.startTimer=function(){};h.stopTimer=function(){};h.Bc=function(){};h.Ec=function(){};h.vb=function(){};
h.Ob=function(){};h.Ra=function(){};h.Nb=function(){};h.close=function(){};h.Wa=function(){};h.Ya=function(){};h.addEventListener=function(){};h.removeEventListener=function(){};h.Yc=function(){};h.Xc=function(){};h.ad=function(){};h.yc=function(){};h.Sb=function(){};h.xc=function(){};h.Gc=function(){};h.Kc=function(){};function Gj(a,b){return"The "+a+" method has been deprecated. As an alternative please use: "+b+"."}function Hj(a,b){return'Custom event "'+a+'" of type "'+b+'" invoked.'};var Ij=function(a){this.g=a;this.h=""},Lj=function(a,b,c){for(var d=c.split("&"),e=0;e<d.length;e++){var f=d[e].split("=");if(1<f.length&&f[0].length&&f[1].length){var g=decodeURIComponent(f[0]);f=decodeURIComponent(f[1]);a.g.set(g,f)}}if(null!=a.g&&a.g.ka("exitEvents")){d={};e=a.g.get("exitEvents").toString();g=e.split("{DELIM}");for(f=0;f<g.length;f++){var l=g[f];ic(e,"%2C")&&(l=unescape(l));var m={};l=l.split(",");for(var p=0;p<l.length;p++)if(Jj.test(l[p])){l[p].replace(Kj,"%25$1!");var r=l[p].split(":"),
H=r.shift();m[H]=unescape(r.join(":"))}d[m.name]=m}b.exitEvents=d}a.h=c};Ij.prototype.get=function(a,b){return this.g.get(a,b)};Ij.prototype.set=function(a,b){return this.g.set(a,b)};Ij.prototype.ka=function(a){return this.g.ka(a)};var Jj=/:/,Kj=/%(.+)!/;var Mj=function(a){this.g={};this.h=new Ij(a)},Nj=function(a,b,c,d){var e=c;"Number"==d?e=parseInt(c,10):"Boolean"==d&&(e="true"==c.toLowerCase()||"1"==c);a.g[b]=e},Oj=function(a,b){try{var c=JSON.parse(b);null!=c&&Ob(a.g,c);var d={};Cb(a.g,function(e,f){e&&!t(e)&&(f=decodeURIComponent(f),e=decodeURIComponent(e));f&&e&&(d[f]=e)},a);a.g=d}catch(e){Lj(a.h,a.g,b)}};Mj.prototype.getParameter=function(a,b){return Ib(this.g,a)?Kb(this.g,a):this.h.get(a,b)};
Mj.prototype.V=function(a){a=parseInt(this.getParameter(a),10);return isNaN(a)?null:a};Mj.prototype.ga=function(a){a=this.getParameter(a);return $b(sd(a))?null:a.toString()};var Pj=function(){this.g=!1;this.l=[]},Qj=function(a,b,c){a.h?R(a.h,b,c):a.l.push({type:b,Md:c})},Rj=function(a,b,c,d,e,f){Qj(a,f?"logEventFlushCounters":"logEvent",[b,c,a.j,!!d,!!e])},Tj=function(a){a.g||(Rj(a,"Count","INTERACTIVE_IMPRESSION"),a.g=!0,Sj(a))},Sj=function(a){Qj(a,"flushCounters",[a.j])};Pj.prototype.X=function(a,b,c){Qj(this,"logVideoEvent",[a,escape(b),c])};var Uj=function(a){M.call(this);this.A=a;this.g=this.l=null;this.D=!1;this.h=null;this.s=!1;this.o=-1;this.B=0};k(Uj,M);Uj.prototype.Pb=function(){return this.D};Uj.prototype.H=function(){this.B=1;this.g&&(rh(this.g),this.g.start())};Uj.prototype.K=function(){this.B=0;this.g&&rh(this.g)};
Uj.prototype.F=function(){"1"==this.A.getParameter("isMouseOver")||1==this.B?this.s||(this.D=!0,1>this.o?this.o=Oa():1E3<Oa()-this.o&&(this.s=!0,this.A.dispatchEvent(new Q("interaction")),Vj(this.A,"setTimerAdjustment",["INTERACTION_TIMER",-1E3,0]),this.l&&(Rj(this.l,"Start","INTERACTION_TIMER"),Tj(this.l)))):(this.s&&Wj(this),this.o=-1)};var Wj=function(a){a.s=!1;a.l&&Rj(a.l,"Stop","INTERACTION_TIMER")};Uj.prototype.C=function(){this.s&&Wj(this);Hg(this.h,this.g);M.prototype.C.call(this)};var Xj=function(){return new Yd((window.STUDIO_ORIGINAL_ASSET_URL?window.STUDIO_ORIGINAL_ASSET_URL:window.location.href).replace(/%(?![A-Fa-f0-9][A-Fa-f0-9])/g,"%25"))},Yj=function(a){a&&Ie(parseInt(a,10)||0)};var Zj=function(a){this.g=a};Zj.prototype.j=function(a){return(null===this.g||this.g.canPlayType)&&Zb(a,"video/")};Zj.prototype.h=function(a){return this.g?"probably"==this.g.canPlayType(a.toLowerCase()):!1};function ak(a){this.g=a}ak.prototype.j=function(a){return"image/webp"==a.toLowerCase()};ak.prototype.h=function(a){if(!this.g)return!1;a=a.toLowerCase();return Zb(this.g.toDataURL(a),"data:"+a)};function bk(a,b){var c=lb(a);if(/#/.test(c))throw Error("Found a hash in url ("+c+"), appending not supported");var d=/\?/.test(c)?"&":"?";b.forEach(function(e,f){e=e instanceof Array?e:[e];for(var g=0;g<e.length;g++){var l=e[g];null!==l&&void 0!==l&&(c+=d+encodeURIComponent(f)+"="+encodeURIComponent(String(l)),d="&")}});return jb(c)};var ck=function(a){ii.call(this,a)};k(ck,ii);var dk=function(){this.g=Oa()},ek=null;dk.prototype.set=function(a){this.g=a};dk.prototype.reset=function(){this.set(Oa())};dk.prototype.get=function(){return this.g};var fk=function(a){this.l=a||"";ek||(ek=new dk);this.s=ek};fk.prototype.g=!0;fk.prototype.h=!0;fk.prototype.j=!1;var gk=function(a){return 10>a?"0"+a:String(a)},hk=function(a){fk.call(this,a)};v(hk,fk);
var ik=function(a,b){var c=[];c.push(a.l," ");if(a.h){var d=new Date(b.j);c.push("[",gk(d.getFullYear()-2E3)+gk(d.getMonth()+1)+gk(d.getDate())+" "+gk(d.getHours())+":"+gk(d.getMinutes())+":"+gk(d.getSeconds())+"."+gk(Math.floor(d.getMilliseconds()/10)),"] ")}d=c.push;var e=a.s.get();e=(b.j-e)/1E3;var f=e.toFixed(3),g=0;if(1>e)g=2;else for(;100>e;)g++,e*=10;for(;0<g--;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.h,"] ");c.push(b.s);a.j&&(b=b.g,void 0!==b&&c.push("\n",b instanceof Error?b.message:String(b)));
a.g&&c.push("\n");return c.join("")};var jk=function(){this.s=u(this.j,this);this.g=new hk;this.g.h=!1;this.g.j=!1;this.h=this.g.g=!1;this.l={}},lk=function(){var a=kk;if(1!=a.h){var b=hf(jf(),"").h,c=a.s;b&&hf(jf(),b.G()).l.push(c);a.h=!0}};jk.prototype.j=function(a){function b(f){if(f){if(f.value>=Qe.value)return"error";if(f.value>=Re.value)return"warn";if(f.value>=Te.value)return"log"}return"debug"}if(!this.l[a.h]){var c=ik(this.g,a),d=mk;if(d){var e=b(a.l);nk(d,e,c,a.g)}}};
var kk=null,mk=n.console,ok=function(){kk||(kk=new jk);n.location&&-1!=n.location.href.indexOf("Debug=true")&&lk()},nk=function(a,b,c,d){if(a[b])a[b](c,void 0===d?"":d);else a.log(c,void 0===d?"":d)};var qk=function(a,b,c){if("string"===typeof b)(b=pk(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=pk(c,d);f&&(c.style[f]=e)}},rk={},pk=function(a,b){var c=rk[b];if(!c){var d=vd(b);c=d;void 0===a.style[d]&&(d=(cd?"Webkit":bd?"Moz":Zc?"ms":null)+wd(d),void 0!==a.style[d]&&(c=d));rk[b]=c}return c},sk=function(a){"number"==typeof a&&(a=Math.round(a)+"px");return a},tk=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=cd&&!b&&!c;if((void 0===b||d)&&a.getBoundingClientRect){try{var e=a.getBoundingClientRect()}catch(f){e=
{left:0,top:0,right:0,bottom:0}}return new pd(e.right-e.left,e.bottom-e.top)}return new pd(b,c)},vk=function(a){var b=zd(void 0),c=b.g;if(Zc&&c.createStyleSheet)return b=c.createStyleSheet(),uk(b,a),b;c=Md(b,"HEAD")[0];if(!c){var d=Md(b,"BODY")[0];c=b.h("HEAD");d.parentNode.insertBefore(c,d)}d=b.h("STYLE");var e;(e=ld('style[nonce],link[rel="stylesheet"][nonce]',void 0))&&d.setAttribute("nonce",e);uk(d,a);b.j(c,d);return d},wk=function(a){Kd(a.ownerNode||a.owningElement||a)},uk=function(a,b){b instanceof
Gc&&b.constructor===Gc?b=b.g:(Ua("expected object of type SafeStyleSheet, got '"+b+"' of type "+Fa(b)),b="type_error:SafeStyleSheet");if(Zc&&void 0!==a.cssText)a.cssText=b;else if(n.trustedTypes)if(x(null!=a,"goog.dom.setTextContent expects a non-null value for node"),"textContent"in a)a.textContent=b;else if(3==a.nodeType)a.data=String(b);else if(a.firstChild&&3==a.firstChild.nodeType){for(;a.lastChild!=a.firstChild;)a.removeChild(x(a.lastChild));a.firstChild.data=String(b)}else{Jd(a);var c=yd(a);
a.appendChild(c.createTextNode(String(b)))}else a.innerHTML=b},xk=function(a){a=a.style;a.position="relative";a.display="inline-block"};var S=function(a){window.AdobeEdge=window.AdobeEdge||{};window.AdobeEdge.bootstrapLoading=!0;n.console&&(ok(),lk());F.info("");if(a!=yk)return F.$a("You must access the enabler instance using studio.Enabler.getInstance(); or Enabler and not create a duplicate instance."),!1;L.call(this);this.B={};this.o={};this.K=new ri(si(),ti()?window.orientation:0);this.H=new M(this);this.ib=!1;this.F=null;this.j="collapsed";this.Eb=!1;this.pa=null;this.kf=0;this.gb={};this.oa=null;this.Db=!1;this.M=new G;this.l=
null;this.Cb=[];this.Na={};this.Gb=[];this.h=new Pj;this.S=new Uj(this);a=A("CANVAS");a.getContext&&a.getContext("2d")||(a=null);this.kd=new ak(a);(a=A("VIDEO"))||(a=null);this.Qc=new Zj(a);this.g=new Mj(zk(this));a=this.xa=new nj;a.o=!0;N(a,document.body||window,"mousedown",a.B,{capture:!0,passive:!0});N(a,document.body||window,"touchstart",a.B,{capture:!0,passive:!0});this.Wb={}};v(S,L);q("studio.Enabler",S,void 0);var Ak=["c"],yk=Math.random(),Bk=!1,Ck=null,T=function(){Ck||(Ck=new S(yk));return Ck};
S.getInstance=T;h=S.prototype;h.Wc=-1;h.Ib=null;h.$b=null;h.Vb=null;h.gd=!0;h.yb=!1;h.Oa=!1;h.Tc=!1;h.ac=!1;h.Da=null;h.zb=null;h.za=null;h.I=null;
var Dk=function(a){a.Vb||(a.Vb=Xj());return a.Vb},zk=function(a){var b=Dk(a).h;(a=Qd(Dk(a).toString()))&&Sd(a,function(c,d){-1<Ak.indexOf(c)&&b.set(c,d)});return b},Vj=function(a,b,c){R(a.I,b,c,void 0)},Hk=function(a){a.yb=!0;a.zb=a.Dc();a.za=a.Cc();if(a.I){var b=a.h,c=a.zb;b.h=a.I;b.j=c;for(c=vb(b.l);c.length;){var d=c.shift();Qj(b,d.type,d.Md)}}null==a.g.getParameter("clickN")&&Nj(a.g,"clickN",1);a.gd="true"!=a.g.getParameter("ise");b=a.g.getParameter("e",null);null!=b&&Yj(b);b=a.V("leftOffset")||
0;c=a.V("topOffset")||0;0==b&&0==c||Ek(a,b,c);a.o=a.g.getParameter("exitEvents",{});b=a.g;if(Ib(b.g,"assets")||b.h.ka("assets"))b=a.g.getParameter("assets").toString(),Fk(a,b);qi(a.g.getParameter("features",[]));a.S.l=a.h;b=a.S;B(2)&&(N(b,document.body||window,"mouseover",b.H,void 0),N(b,document.body||window,"mouseout",b.K,void 0));void 0!==window.ontouchstart&&(b.g=new qh(1E3),N(b,b.g,"tick",b.K,void 0),N(b,document,["touchstart","touchmove"],b.H,void 0));b.h&&(Ai(b,b.h,"tick",b.F),b.h.dispose());
b.h=new qh(80);N(b,b.h,"tick",b.F,void 0);b.h.start();Gk(a);if(b=a.getParameter("layoutsConfig"))a.$b=JSON.parse(String(b));if(b=a.getParameter("experiments"))a.Wb=JSON.parse(String(b));if(b=a.getParameter("rum_config"))try{var e=JSON.parse(String(b));b=window;if(!b.google_rum_config&&e){var f=new ck(e),g,l=fi(f,2);if(g=null==l?"":l){var m=gi(f,mi);if(m){var p=gi(m,li);if(p){b.google_timing_url=g;Za(p,ci);var r;(void 0===r?0:r)||2>=p.s?(ei(p),p.j[2]=3):p.g[2+p.l]=3;b.google_rum_config=m.toJSON();
var H=fi(f,3);var P=null==H?H:!!H;b.google_measure_js_timing=null==P?!1:P;var Ba=b.document,Hb=rf(g),Ja=Ba.createElement("script");Ja.src=Pc(Hb);Qc(Ja);var Hf=Ba.getElementsByTagName("script")[0];if(Hf&&Hf.parentNode){Hf.parentNode.insertBefore(Ja,Hf);var Em=Ja}else Em=null;Em||(b.google_timing_url=void 0,b.google_rum_config=void 0,b.google_measure_js_timing=void 0)}}}}}catch(Yq){}a.dispatchEvent(new Q("init"));a.M.g||a.M.L();e=a.xa;e.l=!!a.Wb.add_sodar_interaction_signals;if(e.l)try{Jh()}catch(Yq){}};
S.prototype.df=function(a){"number"===typeof a?(this.Wc=a,F.info("enabler.setProfileId set to: "+a)):F.info("enabler.setProfileId invalid profile id value: "+a)};S.prototype.setProfileId=S.prototype.df;S.prototype.ne=function(){return this.Wc};S.prototype.getProfileId=S.prototype.ne;S.prototype.af=function(a){t(a)?(this.Ib=a,this.isInitialized()&&Gk(this)):F.info("enabler.setDevDynamicContent invalid dcData value: "+a)};S.prototype.setDevDynamicContent=S.prototype.af;
var Gk=function(a){if(null!=a.ga("dcData")||a.Ib)window.dynamicContent=null!=a.ga("dcData")?a.nb():a.Ib};S.prototype.nb=function(){var a=this.getParameter("dcData");return a?JSON.parse(String(a)):null};S.prototype.getDynamicDataPayload=S.prototype.nb;S.prototype.sa=function(){return this.$b};S.prototype.getLayoutsConfig=S.prototype.sa;S.prototype.rf=function(){return this.g.h.h||Zi(this.g)};S.prototype.getAdParameters=S.prototype.rf;
S.prototype.yf=function(a){Oj(this.g,a);this.Da&&kh(this.Da);Hk(this);F.info("Asset properties have been set by host.")};S.prototype.setAdParameters=S.prototype.yf;S.prototype.Re=function(){this.Da&&kh(this.Da);F.info("Using default ad parameters in test environment. Simulating local events.");Hk(this)};S.setRushSimulatedLocalEvents=function(a){Bk=!!a;if(a&&Ck){a=Ck;for(var b=0;b<a.Cb.length;++b)a.dispatchEvent(a.Cb[b])}};
var Ik=function(a,b,c){var d=U;c=null!=c?c:0;d.Cb.push(b);sh(function(){this.dispatchEvent(b)},c,d);return bh(d,b,a,!1,d)};S.prototype.Hc=function(){var a=si(),b=this.K.g;if(this.K.h!=a||ti()&&this.K.g!=window.orientation){ti()&&(b=window.orientation);var c=new Q("orientation");c.fa("mode",a);c.fa("degrees",b);this.dispatchEvent(c)}};S.prototype.v=function(a,b,c){if(!this.ra()){var d=u.apply(this,[a,this].concat(Array.prototype.slice.call(arguments,2)));sh(d,b)}};
var Fk=function(a,b){0<b.length&&-1==b.indexOf("=")&&(b=decodeURIComponent(b));b=b.split("&");if(!(2>=b.length&&""==b[0]))for(var c=0;c<b.length;c++){var d=b[c].split("=");a.B[d[0].toLowerCase()]=unescape(d[1])}};S.prototype.A=function(){Sj(this.h)};S.prototype.reportActivitiesImmediately=S.prototype.A;
var Jk=function(a){a.h&&Tj(a.h)},Kk=function(a,b){var c=a.g.getParameter("click",""),d=parseInt(a.g.getParameter("clickN"),10);a=a.g.getParameter("thirdPartyClickRedirect","");a:{var e=c;c=b;if(!$b(sd(e))){e=ui(e,null);if(!Zb(b,e))break a;c=rd(c,e.length)}a&&(c=decodeURIComponent(c),Zb(c,a)&&(c=rd(c,a.length)));if(!$b(sd(e))&&-1<e.indexOf("?"))for(b="number"===typeof d?d:1,d=0;d<b;d++)c=unescape(c);b=c}return b},Lk=function(a,b,c,d){var e={};e.target=c;Oh&&(e.fullscreen=!0);(c=e)||(c={});e=window;
var f=b instanceof mc?b:rc("undefined"!=typeof b.href?b.href:String(b))||tc;b=c.target||b.target;var g=[];for(l in c)switch(l){case "width":case "height":case "top":case "left":g.push(l+"="+c[l]);break;case "target":case "noopener":case "noreferrer":break;default:g.push(l+"="+(c[l]?1:0))}var l=g.join(",");Uc()&&e.navigator&&e.navigator.standalone&&b&&"_self"!=b?(l=Fd(document,"A"),kd(l,"HTMLAnchorElement"),f=f instanceof mc?f:sc(f),l.href=nc(f),l.setAttribute("target",b),c.noreferrer&&l.setAttribute("rel",
"noreferrer"),c=document.createEvent("MouseEvent"),c.initMouseEvent("click",!0,!0,e,1),l.dispatchEvent(c)):c.noreferrer?(e=nd("",e,b,l),f=nc(f),e&&(ad&&ic(f,";")&&(f="'"+f.replace(/'/g,"%27")+"'"),e.opener=null,c=new bb($a,"b/12014412, meta tag with sanitized URL"),hc.test(f)&&(-1!=f.indexOf("&")&&(f=f.replace(bc,"&amp;")),-1!=f.indexOf("<")&&(f=f.replace(cc,"&lt;")),-1!=f.indexOf(">")&&(f=f.replace(dc,"&gt;")),-1!=f.indexOf('"')&&(f=f.replace(ec,"&quot;")),-1!=f.indexOf("'")&&(f=f.replace(fc,"&#39;")),
-1!=f.indexOf("\x00")&&(f=f.replace(gc,"&#0;"))),f='<meta name="referrer" content="no-referrer"><meta http-equiv="refresh" content="0; url='+f+'">',Wa(cb(c),"must provide justification"),x(!$b(cb(c)),"must provide non-empty justification"),c=(c=Qb())?c.createHTML(f):f,c=new Oc(c,null,Nc),(e=e.document)&&e.write&&(f=e.write,c instanceof Oc&&c.constructor===Oc?c=c.g:(Ua("expected object of type SafeHtml, got '"+c+"' of type "+Fa(c)),c="type_error:SafeHtml"),f.call(e,c),e.close()))):(e=nd(f,e,b,l))&&
c.noopener&&(e.opener=null);d&&a.A()},Mk=function(a,b,c,d,e){R(a.I,e?"logExitFlushEventsOpenPopup":"launchExit",["Count",b,a.zb,!1,c,null,d])},Ok=function(a,b,c,d){if($b(sd(b)))F.info("There was a problem with the exit call.");else if(a.isInitialized()){var e=void 0!==d?d:0;d=a.o[b]&&a.o[b].target||"_blank";var f;if(f=!B(8))f=!(ic(a.g.getParameter("click",""),"[rm_exit_id]")&&null!=a.o[b]&&null!=a.o[b].reportingId&&!$b(a.o[b].reportingId));e=!(e&1);var g=a.g.getParameter("click",""),l=a.xa;if(l.o){var m=
g;var p=Cd();l.g.set("dim",p.width+"x"+p.height);p="";for(var r=ha(l.g),H=r.next();!H.done;H=r.next()){var P=ha(H.value);H=P.next().value;P=P.next().value;p=null==P?p+"&"+H+"=":p+"&"+H+"="+P}l.l&&(r="bg=","string"===typeof l.s&&l.A(m,p,r,l.s)&&(r=""+r+l.s),p=p+"&"+r);m=p;p=g.toLowerCase().indexOf("&adurl=");-1<p&&l.A(g,m)&&(g=g.substr(0,p)+m+g.substr(p))}e&&(a.o[b]?B(8)?Mk(a,b,c,g,!0):(e=vi(c),e=Nk(a,e,g,b),Lk(a,e,d,!f)):c&&(a.ra()&&B(8)?Mk(a,b,c,g,!1):(e=vi(c),e=Nk(a,e,g,null),Lk(a,e,d,!0),f=!1)));
f&&Rj(a.h,"Count",b,!0,!0,!0);R(a.I,"AD_CLICKED");d=new Q("exit");d.fa("id",b);d.fa("url",c);a.dispatchEvent(d);F.info('Exit "'+b+'" invoked.')}},Pk=function(a,b,c,d,e){a=a.o[b];c=!d&&a&&a.url?a.url:c;null==e||$b(sd(e))?e=c:(e+="")?(d=c.indexOf("#"),0>d&&(d=c.length),a=c.indexOf("?"),0>a||a>d?(a=d,b=""):b=c.substring(a+1,d),c=[c.substr(0,a),b,c.substr(d)],d=c[1],c[1]=e?d?d+"&"+e:e:d,e=c[0]+(c[1]?"?"+c[1]:"")+c[2]):e=c;return e},Nk=function(a,b,c,d){var e=null;d&&(e=a.o[d],e=null!=e.reportingId?e.reportingId:
null);b=b||"";d=a.g.getParameter("thirdPartyClickRedirect","");a=parseInt(a.g.getParameter("clickN"),10);if(!$b(sd(c))&&-1<c.indexOf("?")){a="number"===typeof a?a:1;for(var f=0;f<a;f++)b=encodeURIComponent(b)}e&&(c=ui(c,e),b=d?encodeURIComponent(d+b):b);return c+b};S.prototype.reportManualClose=function(){F.info("Ad was closed by user action.");Rj(this.h,"Count","EVENT_MANUAL_CLOSE",void 0,void 0,void 0)};S.prototype.reportManualClose=S.prototype.reportManualClose;
S.prototype.$c=function(){Rj(this.h,"Count","ENGAGEMENT",!1,!1,!1);this.A()};S.prototype.reportEngagement=S.prototype.$c;var Qk=function(a,b,c){var d=b;100<d.length&&(d=d.substr(0,100));R(a.I,"reportCustomVariable",[escape(d),c]);F.info('Custom string "'+b+'" recorded.')},Ek=function(a,b,c){a.ra()&&!B(8)&&(a.oa&&wk(a.oa),a.oa=vk(Hc("body",{position:"relative","margin-left":-b+"px !important","margin-top":-c+"px !important"})))};S.prototype.Ie=function(a,b,c){Nj(this.g,a,b,c)};
S.prototype.setParameter=S.prototype.Ie;S.prototype.jc=function(){this.dispatchEvent(new Q("pageLoaded"))};S.prototype.dispatchPageLoaded=S.prototype.jc;S.prototype.dispatchEvent=function(a){this.Na[a.type]=(this.Na[a.type]||0)+1;switch(a.type){case "pageLoaded":null!=window.AdobeEdge&&"function"===typeof window.AdobeEdge.loadResources&&window.AdobeEdge.loadResources();this.Tc=!0;break;case "orientation":this.K.h=a.mode,this.K.g=a.degrees}return S.J.dispatchEvent.call(this,a)};
S.prototype.dispatchEvent=S.prototype.dispatchEvent;S.prototype.Fb=function(a){(this.ac=a)&&!this.Na.visible?(null!=window.AdobeEdge&&"function"===typeof window.AdobeEdge.playWhenReady&&window.AdobeEdge.playWhenReady(),this.gd&&Rj(this.h,"Start","DISPLAY_TIMER",void 0,void 0,void 0),this.dispatchEvent(new Q("visible"))):this.Na.hidden||this.dispatchEvent(new Q("hidden"));this.dispatchEvent(new Q("visibilityChange"))};S.prototype.setAdVisibleInternal=S.prototype.Fb;S.prototype.nf=function(){this.Fb(!0)};
S.prototype.dispatchAdVisible=S.prototype.nf;S.prototype.dd=function(a,b,c,d,e,f){if(c||d)this.F||(this.F={}),this.F.width=c,this.F.height=d;null!=e&&(this.ib=!!e);null!=f&&Nj(this.g,"isMultiDirectional",f?"true":"false")};S.prototype.setExpandingPixelOffsets=S.prototype.dd;S.prototype.fd=function(a){this.ib=!!a};S.prototype.setStartExpanded=S.prototype.fd;S.prototype.ed=function(a){Nj(this.g,"isMultiDirectional",a?"true":"false")};S.prototype.setIsMultiDirectional=S.prototype.ed;
S.prototype.gf=function(a){R(this.I,"invokeMraidMethod",["useCustomClose",[a]]);this.Db=0==a};S.prototype.setUseCustomClose=S.prototype.gf;S.prototype.bf=function(){};S.prototype.setFloatingPixelDimensions=S.prototype.bf;S.prototype.isVisible=function(){return this.ac};S.prototype.isVisible=S.prototype.isVisible;S.prototype.ra=function(){return B(1)};S.prototype.isServingInLiveEnvironment=S.prototype.ra;S.prototype.isPageLoaded=function(){return this.Tc};S.prototype.isPageLoaded=S.prototype.isPageLoaded;
S.prototype.isInitialized=function(){return this.yb};S.prototype.isInitialized=S.prototype.isInitialized;S.prototype.oc=function(a){"function"===typeof a&&lg(this.M,a)};S.prototype.callAfterInitialized=S.prototype.oc;S.prototype.getParameter=function(a){return this.g.getParameter(a,null)};S.prototype.getParameter=S.prototype.getParameter;S.prototype.getParameter=S.prototype.getParameter;S.prototype.V=function(a){return this.g.V(a)};S.prototype.getParameterAsInteger=S.prototype.V;
S.prototype.Fc=function(a){a=this.g.ga(a);return void 0!=a&&("true"==a.toLowerCase()||"1"==a)};S.prototype.getParameterAsBoolean=S.prototype.Fc;S.prototype.ga=function(a){return this.g.ga(a)};S.prototype.getParameterAsNullableString=S.prototype.ga;S.prototype.exit=function(a,b,c){void 0!==b&&(b=Kk(this,b));Jk(this);Ok(this,a,Pk(this,a,b,!1),c)};S.prototype.exit=S.prototype.exit;S.prototype.Lb=function(a,b,c){b=Kk(this,b);Jk(this);Ok(this,a,Pk(this,a,b,!0),c)};S.prototype.exitOverride=S.prototype.Lb;
S.prototype.Xd=function(a,b,c,d,e){e&&!e.pIndex&&(e.pIndex=c);b=Kk(this,b);Jk(this);Ok(this,a,Pk(this,a,b,!0),d)};S.prototype.dynamicExit=S.prototype.Xd;S.prototype.Mb=function(a,b){Ok(this,a,Pk(this,a,void 0,void 0,b||""))};S.prototype.exitQueryString=S.prototype.Mb;S.prototype.zc=function(a){return Nk(this,a,this.g.getParameter("click",""),null)};S.prototype.formExitUrlFromOverride=S.prototype.zc;
S.prototype.counter=function(a,b){F.info('Counter "'+a+'" invoked.');Rj(this.h,"Count",a,b,!0,void 0);F.Pa(Hj(a,"Count"))};S.prototype.counter=S.prototype.counter;S.prototype.startTimer=function(a){F.info('Timer "'+a+'" started.');Rj(this.h,"Start",a,void 0,!0,void 0);F.Pa(Hj(a,"Start"))};S.prototype.startTimer=S.prototype.startTimer;S.prototype.stopTimer=function(a){F.info('Timer "'+a+'" stopped.');Rj(this.h,"Stop",a,void 0,!0,void 0);F.Pa(Hj(a,"Stop"))};S.prototype.stopTimer=S.prototype.stopTimer;
S.prototype.We=function(a){F.T(Gj("Enabler.reportCustomImpressionVariable(postString)","Enabler.reportCustomVariableCount1(customString)"));Qk(this,a,1)};S.prototype.reportCustomImpressionVariable=S.prototype.We;S.prototype.Xe=function(a){Qk(this,a,1)};S.prototype.reportCustomVariableCount1=S.prototype.Xe;S.prototype.Ve=function(a){F.T(Gj("Enabler.reportCustomClickVariable(postString)","Enabler.reportCustomVariableCount1(customString)"));Qk(this,a,2)};S.prototype.reportCustomClickVariable=S.prototype.Ve;
S.prototype.Ye=function(a){Qk(this,a,2)};S.prototype.reportCustomVariableCount2=S.prototype.Ye;S.prototype.Bc=function(){return this.j};S.prototype.getContainerState=S.prototype.Bc;S.prototype.Ec=function(){return this.pa};S.prototype.getExpandDirection=S.prototype.Ec;S.prototype.ef=function(a){this.Eb||Vj(this,"setResponsiveBehavior",[a?2:0,2])};S.prototype.setResponsiveExpanding=S.prototype.ef;S.prototype.ff=function(a,b){Vj(this,"responsiveResize",[a,b])};S.prototype.setResponsiveSize=S.prototype.ff;
S.prototype.vb=function(){if("collapsed"!=this.j)F.T("Enabler.requestExpand() should not be invoked unless the creative is in the collapsed state.");else{mh(this,"expandStart")||F.T("Please implement the expansion via event handlers:\nEnabler.addEventListener(\n    studio.events.StudioEvent.EXPAND_START,\n    function() {/* expand action */});");this.Eb=!0;var a=[this.za];this.F&&a.push(this.F);R(this.I,"expandRequested",a);Rk(this,this.Ra);this.v(this.Mc,0)}};S.prototype.requestExpand=S.prototype.vb;
var Rk=function(a,b){if(!a.ra()&&a.Db){var c=document.getElementsByTagName("body")[0],d=A("IMG",{width:"15",height:"15",border:"0",src:"http://s0.2mdn.net/ads/studio/close.png"});a.l={L:b,element:A("DIV",{style:"position: absolute;right: 5px;top: 5px;width: 15px;height: 15px;cursor: pointer;"},d)};N(a.H,a.l.element,"click",b,void 0);Id(c,a.l.element)}},Sk=function(a){a.l&&(Kd(a.l.element),Ai(a.H,a.l.element,"click",a.l.L),a.l.element=null,a.l.L=null,a.l=null)};S.prototype.Je=function(){this.dispatchEvent(new Q("aboutToExpand"))};
S.prototype.aboutToExpandInternal=S.prototype.Je;
S.prototype.Mc=function(a){a&&(a=pj[a.toString().toUpperCase()]);var b=0,c=0;"true"==this.getParameter("isMultiDirectional")&&a&&(a.g&2&&(c=this.V("topOffset")),a.g&1&&(b=this.V("leftOffset")));Ek(this,null===b?0:b,null===c?0:c);a?this.pa=a:this.pa=this.ra()||"true"!=this.getParameter("isMultiDirectional")?null:qj[this.kf++%qj.length];this.ib||(Rj(this.h,"Start","EXPAND_TIMER",void 0,void 0,void 0),Jk(this),this.Oa||(this.A(),this.Oa=!0));this.ib=!1;this.j="expanding";a=new Q("expandStart");a.fa("direction",
this.pa);this.dispatchEvent(a)};S.prototype.startExpandInternal=S.prototype.Mc;S.prototype.Ob=function(){"expanding"!=this.j?F.T("You must first call Enabler.requestExpand() to initiate the expansion and then call Enabler.finishExpand() when the expand animation has  finished. Cancelling expansion..."):(R(this.I,"expandFinished",[this.za]),this.j="expanded",F.info("The creative has expanded."),this.dispatchEvent(new Q("expandFinish")))};S.prototype.finishExpand=S.prototype.Ob;
S.prototype.expand=function(a,b){F.T("The Enabler.expand() method has been deprecated. As an alternative please use: Enabler.requestExpand().");Ek(this,0,0);var c=[this.za];b&&c.push(b);this.Db=!!b&&0==b.useCustomClose;R(this.I,"expandAsset",c);a||(Rj(this.h,"Start","EXPAND_TIMER",void 0,void 0,void 0),Jk(this));this.Oa||(this.A(),this.Oa=!0);F.info("The creative has expanded.")};S.prototype.expand=S.prototype.expand;
S.prototype.Ra=function(){"expanded"!=this.j&&F.T("Enabler.requestCollapse() should not be invoked unless the creative is in the expanded state.");Sk(this);mh(this,"collapseStart")||F.T("Please implement collapse via event handlers:\nEnabler.addEventListener(\n    studio.events.StudioEvent.COLLAPSE_START,\n    function() {/* Begin collapse animation */});");R(this.I,"collapseRequested",[this.za]);this.v(this.Lc,0)};S.prototype.requestCollapse=S.prototype.Ra;
S.prototype.Lc=function(){this.j="collapsing";this.dispatchEvent(new Q("collapseStart"))};S.prototype.startCollapseInternal=S.prototype.Lc;S.prototype.Nb=function(){"collapsing"!=this.j?F.T("You must first call Enabler.requestCollapse() to initiate the collapse and then call Enabler.finishCollapse() when the collapse animation has  finished. Cancelling collapse..."):(R(this.I,"collapseFinished",[this.za]),this.v(this.kc,0))};S.prototype.finishCollapse=S.prototype.Nb;
S.prototype.kc=function(){var a=this.V("leftOffset")||0,b=this.V("topOffset")||0;Ek(this,a,b);Rj(this.h,"Stop","EXPAND_TIMER",void 0,void 0,void 0);this.j="collapsed";F.info("The creative has collapsed.");this.dispatchEvent(new Q("collapseFinish"))};S.prototype.finishCollapseInternal=S.prototype.kc;
S.prototype.collapse=function(){F.T("The Enabler.collapse() method has been deprecated. As an alternative please use: Enabler.requestCollapse().");mh(this,"collapse")||F.T("Please implement collapse via event handlers:\nEnabler.addEventListener(\n    studio.events.StudioEvent.COLLAPSE_START,\n    function() {/* Begin collapse animation */});");var a=this.V("leftOffset")||0,b=this.V("topOffset")||0;Ek(this,a,b);R(this.I,"collapseAsset",[this.za]);this.dispatchEvent(new Q("collapse"));Rj(this.h,"Stop",
"EXPAND_TIMER",void 0,void 0,void 0)};S.prototype.collapse=S.prototype.collapse;S.prototype.close=function(){this.S.dispose();R(this.I,"tellAssetHide",[this.za]);F.info("Closing ad. If this was invoked by a user action, call Enabler.reportManualClose() as well.")};S.prototype.close=S.prototype.close;S.prototype.Pd=function(){};S.prototype.closeCompanion=S.prototype.Pd;S.prototype.Wd=function(){};S.prototype.displayCompanion=S.prototype.Wd;S.prototype.je=function(){return this.ga("sn")};
S.prototype.getDartSiteName=S.prototype.je;S.prototype.ie=function(){return this.V("sid")};S.prototype.getDartSiteId=S.prototype.ie;S.prototype.fe=function(){return this.V("aid")};S.prototype.getDartAdId=S.prototype.fe;S.prototype.he=function(){return this.V("pid")};S.prototype.getDartPageId=S.prototype.he;S.prototype.Dc=function(){return this.ga("rid")};S.prototype.getDartRenderingId=S.prototype.Dc;S.prototype.ge=function(){return this.V("cid")};S.prototype.getDartCreativeId=S.prototype.ge;
S.prototype.Cc=function(){return this.ga("varName")};S.prototype.getDartAssetId=S.prototype.Cc;S.prototype.qe=function(){return this.ga("ct")};S.prototype.getUserCountry=S.prototype.qe;S.prototype.se=function(){return this.ga("st")};S.prototype.getUserState=S.prototype.se;S.prototype.te=function(){return this.ga("zp")};S.prototype.getUserZipCode=S.prototype.te;S.prototype.pe=function(){var a=this.V("bw");return null!=a?a:0};S.prototype.getUserBandwidth=S.prototype.pe;S.prototype.oe=function(){return this.ga("ac")};
S.prototype.getUserAreaCode=S.prototype.oe;S.prototype.re=function(){return this.V("dma")};S.prototype.getUserDMACode=S.prototype.re;S.prototype.getFilename=function(a){F.T("The method: Enabler.getFilename(filename) has been deprecated. As an alternative please use: Enabler.getUrl(filename).");return this.Wa(a)};S.prototype.getFilename=S.prototype.getFilename;
S.prototype.Wa=function(a){var b=a.toLowerCase(),c=b.slice(b.lastIndexOf("/")+1),d=encodeURIComponent(c),e=this.B[c];e=(e=(e=(e=(e=e||this.B["pro_"+c])||this.B[b])||this.B["pro_"+b])||this.B[d])||this.B["pro_"+d];return null!=e?e:a};S.prototype.getUrl=S.prototype.Wa;S.prototype.me=function(){return this.K};S.prototype.getOrientation=S.prototype.me;S.prototype.X=function(a,b,c){this.h.X(a,b,c);F.info('Video "'+b+'" dispatching "'+c+'" event.')};
S.prototype.vf=function(a,b){if(a)for(var c=0;c<this.Gb.length;++c){var d=this.Gb[c];if(null!=d)switch(a){case "changevolume":null!=b&&(0<b&&(d.muted=!1),d.volume=b);break;case "pause":d.pause();break;case "resume":d.play()}}};S.prototype.invokeOnAllVideos=S.prototype.vf;S.prototype.Ic=function(a){null!=a&&this.Gb.push(a)};S.prototype.registerVideoElements=S.prototype.Ic;S.prototype.De=function(a,b){a=ug(rf(a));null!=b&&lg(a,b)};S.prototype.loadScript=S.prototype.De;
S.prototype.Ya=function(a,b){Xd(pf,a)?lg(this.M,Na(Fh,a,b)):F.$a("There is no module called "+a+".")};S.prototype.loadModule=S.prototype.Ya;S.prototype.Ce=function(a,b){for(var c=a.length,d=0;d<a.length;++d)this.Ya(a[d],function(){0==--c&&b()})};S.prototype.loadModules=S.prototype.Ce;S.prototype.Jc=function(a){F.info('Dispatching function invocation "'+a+'" on parent.');R(this.I,"invokeExternalJSFunction",[escape(a)])};S.prototype.invokeExternalJsFunction=S.prototype.Jc;
S.prototype.Kc=function(a,b,c){a in Ej||F.T('The mraid method "'+a+"\" isn't allowed to be invoked, please use one of the corresponding Enabler methods.");var d='Method "'+a+'" invoked';b&&(d+='with arguments "'+b.join(",")+'"');F.info(d+".");R(this.I,"invokeMraidMethod",[a,b],c)};S.prototype.invokeMraidMethod=S.prototype.Kc;S.prototype.uf=function(){F.T("The method: Enabler.invokeAdMobMethod has been deprecated.")};S.prototype.invokeAdMobMethod=S.prototype.uf;
S.prototype.D=function(a,b,c,d){Xd(wi,a)?R(this.I,"invokeExternalJSFunctionWithReturn",[a,b,c],d):F.$a('The whitelist global object "'+a+"\" isn't whitelisted, please only call methods on one of the existing whitelisted objects.")};S.prototype.invokeExternalJsFunctionWithReturn=S.prototype.D;S.prototype.xf=function(a,b){F.info("Dispatching function invocation openUrl on parent.");R(this.I,"invokeUrlOpen",[a],b)};S.prototype.invokeUrlOpen=S.prototype.xf;
S.prototype.C=function(){this.oa&&wk(this.oa);this.Da&&kh(this.Da);Hg(this.S,this.h,this.H,this.M,this.xa);delete this.gb;S.J.C.call(this)};S.prototype.addEventListener=function(a,b,c,d,e){a=mj[a.toString()]||a;if(a.toString()in jj){if(!this.ra()){e=b;"function"===typeof b&&(e=function(f){f.de?b(f.da):b(f)});N(this.H,window,a,e,c,d);return}R(this.I,"registerEventTypeListenerForType",[a,e])}"hostpageFeaturesLoaded"==a&&R(this.I,"getHostpageFeatures",[a]);ch(this,a,b,c,d)};
S.prototype.addEventListener=S.prototype.addEventListener;S.prototype.removeEventListener=function(a,b,c,d){jh(this,a,b,c,d)};S.prototype.removeEventListener=S.prototype.removeEventListener;S.prototype.Hb=function(a,b){this.gb[a]=b};S.prototype.addMessageHandler=S.prototype.Hb;S.prototype.Te=function(a){delete this.gb[a]};S.prototype.removeMessageHandler=S.prototype.Te;S.prototype.$e=function(a,b){a=this.gb[a];"function"===typeof a&&a.apply(null,b)};S.prototype.defaultMessageHandler=S.prototype.$e;
S.prototype.Yc=function(){mh(this,"fullscreenSupport")||F.T("Please implement an event handler in order to receive support status:\nEnabler.addEventListener(\n    studio.events.StudioEvent.FULLSCREEN_SUPPORT,\n    function() {/* query event for fullscreen status */});");R(this.I,"isFullscreenSupported");this.v(this.Ac,0,!0)};S.prototype.queryFullscreenSupport=S.prototype.Yc;
S.prototype.Xc=function(){mh(this,"fullscreenDimensions")||F.T("Please implement an event handler in order to receive dimensions:\nEnabler.addEventListener(\n    studio.events.StudioEvent.FULLSCREEN_DIMENSIONS,\n    function() {/* query event for fullscreen dimensions */});");R(this.I,"queryFullscreenDimensions");if(!this.ra()){var a=Cd();this.v(this.qc,0,a.width,a.height)}};S.prototype.queryFullscreenDimensions=S.prototype.Xc;
S.prototype.ad=function(a,b){if("collapsed"!=this.j)F.T("Enabler.requestFullscreenExpand() should not be invoked unless the  creative is in the collapsed state.");else{mh(this,"fullscreenExpandStart")||F.T("Please implement the fullscreen expansion via event handlers:\nEnabler.addEventListener(\n    studio.events.StudioEvent.FULLSCREEN_EXPAND_START,\n    function() {/* expand action */});");this.Eb=!0;var c=[];a&&b&&(c=[a,b]);R(this.I,"fullscreenExpandRequested",c);Rk(this,this.Sb);this.v(this.wc,
0,a,b)}};S.prototype.requestFullscreenExpand=S.prototype.ad;S.prototype.yc=function(){"fs_expanding"!=this.j?F.T("You must first call Enabler.requestFullscreenExpand() to initiate the expansion and then call Enabler.finishFullscreenExpand() when the expand animation has finished. Cancelling expansion..."):(R(this.I,"fullscreenExpandFinished"),F.info("The creative has expanded."),this.v(this.vc,0))};S.prototype.finishFullscreenExpand=S.prototype.yc;
S.prototype.Sb=function(){"fs_expanded"!=this.j?F.T("Enabler.requestFullscreenCollapse() should not be invoked unless the  creative is in the fullscreen state."):(mh(this,"fullscreenCollapseStart")||F.T("Please implement fullscreen collapse via event handlers:\nEnabler.addEventListener(\n    studio.events.StudioEvent.FULLSCREEN_COLLAPSE_START,\n    function() {/* Begin collapse animation */});"),Sk(this),R(this.I,"fullscreenCollapseRequested"),this.v(this.nc,0))};
S.prototype.requestFullscreenCollapse=S.prototype.Sb;S.prototype.xc=function(){"fs_collapsing"!=this.j?F.T("You must first call Enabler.requestFullscreenCollapse() to initiate the collapse and then call Enabler.finishFullscreenCollapse() when the collapse animation has finished. Cancelling collapse..."):(R(this.I,"fullscreenCollapseFinished"),this.v(this.lc,0))};S.prototype.finishFullscreenCollapse=S.prototype.xc;S.prototype.Se=function(a){R(this.I,"registerChargeableEventName",[a])};
S.prototype.registerChargeableEventName=S.prototype.Se;S.prototype.Pb=function(){return this.S.Pb()};S.prototype.hasUserInteracted=S.prototype.Pb;S.prototype.Ac=function(a){var b=new Q("fullscreenSupport");b.fa("supported",a);this.dispatchEvent(b)};S.prototype.fullscreenSupportInternal=S.prototype.Ac;S.prototype.qc=function(a,b){var c=new Q("fullscreenDimensions");void 0!=a&&void 0!=b&&(c.fa("width",a),c.fa("height",b));this.dispatchEvent(c)};S.prototype.fullscreenDimensionsInternal=S.prototype.qc;
S.prototype.wc=function(a,b,c,d){Rj(this.h,"Start","EXPAND_TIMER",void 0,void 0,void 0);Jk(this);this.Oa||(this.A(),this.Oa=!0);this.j="fs_expanding";var e=new Q("fullscreenExpandStart");e.fa("width",a);e.fa("height",b);e.fa("left",c);e.fa("top",d);this.dispatchEvent(e)};S.prototype.fullscreenExpandStartInternal=S.prototype.wc;S.prototype.vc=function(){this.j="fs_expanded";this.dispatchEvent(new Q("fullscreenExpandFinish"))};S.prototype.fullscreenExpandFinishInternal=S.prototype.vc;
S.prototype.nc=function(){this.j="fs_collapsing";this.dispatchEvent(new Q("fullscreenCollapseStart"))};S.prototype.fullscreenCollapseStartInternal=S.prototype.nc;S.prototype.lc=function(){Rj(this.h,"Stop","EXPAND_TIMER",void 0,void 0,void 0);this.j="collapsed";this.dispatchEvent(new Q("fullscreenCollapseFinish"))};S.prototype.fullscreenCollapseFinishInternal=S.prototype.lc;S.prototype.Qe=function(){return this.kd.h("image/webp")};S.prototype.canRenderWebpImages=S.prototype.Qe;
S.prototype.Nc=function(a){return this.Qc.j(a)?this.Qc.h(a):!1};S.prototype.supportsVideoFormat=S.prototype.Nc;S.prototype.cf=function(){return this};S.prototype.setHint=S.prototype.cf;S.prototype.Gc=function(){a:{for(a in Le)if("studio"==Le[a]){var a="studio";break a}a=null}return a||"studio"};S.prototype.getSdk=S.prototype.Gc;var Tk=T();q("Enabler",Tk,void 0);var U=T();
if(!U.yb){var Uk=U.g.getParameter("e",null);Yj(Uk);var Vk=U.V("leftOffset")||0,Wk=U.V("topOffset")||0;0==Vk&&0==Wk||Ek(U,Vk,Wk);var Xk=U.Fc("ssr"),Yk;Yk=Dk(U).l;var Zk;Zk=Dk(U).j;var $k;$k=Dk(U).g;var al=[Yk,"://",Zk,$k].join(""),bl=pi;Gg(bl.g);bl.size=0;qi([1,2]);var cl;cl=U.g.getParameter("c",void 0);var dl;dl=U.g.V("t");U.I=new Cj(cl,dl);Ig(U,U.I);U.I.g=U;U.I.o=al.split("?")[0];U.I.B=Xk;if(!U.ra()){var el=1E3;Bk&&(el=0);U.Da=Ik(U.Re,"a",el);var fl=2E3,gl=2500;Bk&&(gl=fl=0);Ik(U.jc,"b",fl);Ik(Na(U.Fb,
!0),"c",gl);N(U.H,window,["resize","orientationchange"],U.Hc,void 0);U.Hc()}U.I.connect()}th.enabler=3;var hl=[],il=function(a){x(!Object.isSealed(a),"Cannot use getInstance() with a sealed constructor.");var b="Ga";if(a.Ga&&a.hasOwnProperty(b))return a.Ga;hl.push(a);var c=new a;a.Ga=c;x(a.hasOwnProperty(b),"Could not instantiate singleton.");return c};function V(){L.call(this);this.g="loading";this.h=n.Enabler;this.h.isInitialized()?this.j():ch(this.h,"init",this.j,!1,this)}v(V,L);V.prototype.l=function(){return this.g};V.prototype.getState=V.prototype.l;V.prototype.A=function(){return"1.0"};V.prototype.getVersion=V.prototype.A;V.prototype.o=function(){this.h.exit("MRAID default exit")};V.prototype.open=V.prototype.o;
V.prototype.close=function(){"expanded"==this.g?(this.h.Ra(),this.g="default",this.dispatchEvent("stateChange")):"default"==this.g&&(this.g="hidden",this.h.close(),this.dispatchEvent("stateChange"))};V.prototype.close=V.prototype.close;V.prototype.B=function(){return this.h.isVisible()};V.prototype.isViewable=V.prototype.B;V.prototype.expand=function(){"default"==this.g&&(this.h.vb(),this.g="expanded",this.dispatchEvent("stateChange"))};V.prototype.expand=V.prototype.expand;
V.prototype.j=function(){this.g="default";ch(this.h,"collapseStart",this.v,!1,this);this.dispatchEvent("ready")};V.prototype.v=function(){"expanded"==this.g&&(this.g="default",this.dispatchEvent("stateChange"))};if(!window.mraid){var jl=il(V);q("mraid",jl,void 0)};q("studio.sdk.hint.ExpansionMode",{NORMAL:"normal",LIGHTBOX:"lightbox"},void 0);q("studio.sdk.hint.ExpansionTrigger",{ON_CLICK:"onClick",ON_HOVER:"onHover"},void 0);q("studio.sdk.hint.Hint",{EXPANSION_MODE:"expansionMode",EXPANSION_TRIGGER:"expansionTrigger"},void 0);var kl=function(a){return"Config type "+a+" does not exist"},ll=function(a){return"Unable to parse a type for value with JavaScript type "+Fa(a)+': "'+a+'"'},ml=function(a,b){return"Cannot handle description for property "+b+" on type "+a+"."},nl=function(a,b){return"Array property "+b+" on type "+a+" must have at least one element."},ol=function(a,b){return"Invalid type for value of property "+b+" on type "+a+"."},pl=function(a,b){return"No value specified for non-optional property "+b+" on type "+
a+"."},ql=function(a,b){return"Property "+b+" does not exist on type "+a+"."},rl=function(a,b){return"Property "+b+" is not an array on type "+a+"."},sl=function(a,b,c,d){return"Property "+b+" on type "+a+" has length "+c+", but invalid index "+d+" was requested."},tl=function(a){return"The optional property "+a+" must be a reference"};var ul=function(){},vl=function(a){var b=T();b.isInitialized()?a(b):b.s.add("init",Na(a,b),!0,void 0,void 0)},wl=function(){return new Lf(function(a){var b=T();b.isInitialized()?a(b):b.s.add("init",Na(a,b),!0,void 0,void 0)})};q("studio.utils.EnablerAccessor",ul,void 0);ul.loadModuleWhenReady=function(a,b){vl(function(c){c.Ya(a,b)})};ul.getInitializedEnabler=wl;ul.getInitializedEnablerByCallback=vl;var xl=function(){};Ea(xl);xl.prototype.g=0;var yl=function(a){L.call(this);this.oa=a||zd();this.K=null;this.qa=!1;this.h=null;this.F=void 0;this.A=this.j=this.v=null};v(yl,L);yl.prototype.Na=xl.Ea();var zl=function(a){return a.K||(a.K=":"+(a.Na.g++).toString(36))};yl.prototype.getElement=function(){return this.h};
var Al=function(a){a.F||(a.F=new M(a));return x(a.F)},Bl=function(a,b){if(a==b)throw Error("Unable to set parent component");var c;if(c=b&&a.v&&a.K){c=a.v;var d=a.K;c=c.A&&d?Kb(c.A,d)||null:null}if(c&&a.v!=b)throw Error("Unable to set parent component");a.v=b;yl.J.Ub.call(a,b)};yl.prototype.Ub=function(a){if(this.v&&this.v!=a)throw Error("Method not supported");yl.J.Ub.call(this,a)};yl.prototype.ba=function(){this.h=Nd(this.oa,"DIV")};
var Cl=function(a,b,c){if(a.qa)throw Error("Component already rendered");a.h||a.ba();b?b.insertBefore(a.h,c||null):a.oa.g.body.appendChild(a.h);a.v&&!a.v.qa||a.ca()};yl.prototype.ca=function(){this.qa=!0;Dl(this,function(a){!a.qa&&a.getElement()&&a.ca()})};var El=function(a){Dl(a,function(b){b.qa&&El(b)});a.F&&Bi(a.F);a.qa=!1};yl.prototype.C=function(){this.qa&&El(this);this.F&&(this.F.dispose(),delete this.F);Dl(this,function(a){a.dispose()});this.h&&Kd(this.h);this.v=this.h=this.A=this.j=null;yl.J.C.call(this)};
var Fl=function(a,b){var c=a.j?a.j.length:0;x(!!b,"Provided element must not be null.");if(b.qa)throw Error("Component already rendered");if(0>c||c>(a.j?a.j.length:0))throw Error("Child component index out of bounds");a.A&&a.j||(a.A={},a.j=[]);if(b.v==a){var d=zl(b);a.A[d]=b;sb(a.j,b)}else{d=a.A;var e=zl(b);if(null!==d&&e in d)throw Error('The object already contains the key "'+e+'"');d[e]=b}Bl(b,a);xb(a.j,c,0,b);b.qa&&a.qa&&b.v==a?(a=a.na(),(a.childNodes[c]||null)!=b.getElement()&&(b.getElement().parentElement==
a&&a.removeChild(b.getElement()),c=a.childNodes[c]||null,a.insertBefore(b.getElement(),c))):(a.h||a.ba(),c=a.j?a.j[c+1]||null:null,Cl(b,a.na(),c?c.h:null))};yl.prototype.na=function(){return this.h};
var Dl=function(a,b){a.j&&a.j.forEach(b,void 0)},Gl=function(a){for(var b=[];a.j&&0!=a.j.length;){var c=b,d=c.push,e=a,f=a.j?a.j[0]||null:null;if(f){var g="string"===typeof f?f:zl(f);f=e.A&&g?Kb(e.A,g)||null:null;if(g&&f){var l=e.A;g in l&&delete l[g];sb(e.j,f);El(f);f.h&&Kd(f.h);Bl(f,null)}}if(!f)throw Error("Child is not in parent component");d.call(c,f)}};})();
