/**
 * Ed-admin API Configuration
 * 
 * Update the API_BASE_URL to point to your deployed backend
 */

// Configuration object
const EdAdminConfig = {
    // PRODUCTION: Backend deployed to Render
    API_BASE_URL: 'http://localhost:8000',

    // For local development, use:
    // API_BASE_URL: 'http://localhost:3001',
    
    // Alternative: You can deploy to Render, Railway, or Heroku
    // Examples:
    // API_BASE_URL: 'https://your-app-name.onrender.com',
    // API_BASE_URL: 'https://your-app-name.up.railway.app',
    // API_BASE_URL: 'https://your-app-name.herokuapp.com',
    
    // API Endpoints
    get JOBS_API_URL() {
        return `${this.API_BASE_URL}/api/jobs`;
    },
    
    get APPLICATION_API_URL() {
        return `${this.API_BASE_URL}/api/applications`;
    },
    
    get ADMIN_API_URL() {
        return `${this.API_BASE_URL}/api/admin`;
    },
    
    get HEALTH_API_URL() {
        return `${this.API_BASE_URL}/api/health`;
    }
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EdAdminConfig;
}
