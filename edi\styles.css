/* start of header styles */

.header-edi {
    height: 90vh;
    width: 100%;
    background-image: url('../assets/img/Edi/HeroImageNew.png');
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
}


.header-heading-1{
    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
    color: #FFFFFF;
    font-size: 66px;
    text-align: center;
}

@media (max-width: 450px) {
    .header-heading-1{
        font-family: "Montserrat", serif;
        font-optical-sizing: auto;
        font-weight: 500;
        font-style: normal;
        color: #FFFFFF;
        font-size: 45px;
        text-align: center;
    }
}


.header-subheading-1{
    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    color: #BFBFBF;
    font-size: 25px;
    text-align: center;
    
}

.header-button-1{
    width: 166px;
    height: 39px;
    background-color: #E49616;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 35px;
}




/* End of Header styles */



/* Start of about us section styles */

.our-mission-section{
    height: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 20px;
    padding-bottom: 20px;
    background-color: #165E92;
  
}

@media (min-width: 1561px) {
    .our-mission-section{
        height: auto;
        width: 1561px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding-top: 20px;
        padding-bottom: 20px;

      
    }
}



.our-mission-subsection{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.our-mission-text-container{
    width: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (max-width: 992px) {
    .our-mission-text-container{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: center;
       
    }
}



.our-mission-heading-1{
    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
    color: #EEEEEE;
}

.our-mission-description-1{
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-style: normal;
     color: #EEEEEE;
}


/* End of about us section */


/* Start of our products section */


.our-product-section{
    height: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #ffffff;
    padding-top: 50px;
    padding-bottom: 50px;
}

@media (min-width: 1561px) {
    .our-product-section{
        height: auto;
        width: 1561px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
 
        padding-top: 50px;
        padding-bottom: 50px;
    }
}




.our-product-text-container{
    width: 78%;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (max-width: 992px) {
    .our-product-text-container{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: center;
        padding-left: 20px;
        padding-right: 20px;
    }
}

.our-product-subsection{
    display: flex;
    margin-top: 3%;
    margin-bottom: 3%;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.our-product-heading-1{

    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
}

.our-product-description{
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-style: normal;
    
    width: 100%;
 
}


/* start of administrative section   */


.administrative-section{
    height: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 20px;
    padding-bottom: 20px;
}

@media (min-width: 1561px) {

    .administrative-section{
        height: auto;
        width: 1561px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding-top: 20px;
        padding-bottom: 20px;
    }
    
}

.administrative-subsection{
    display: flex;

    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.administrative-text-container{
    width: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (max-width: 992px) {
    .administrative-text-container{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: center;
        padding-left: 20px;
        padding-right: 20px;
     }
}

.administrative-heading-1{
    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
}

.administrative-description-1{
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-style: normal;
}


/* End of administrative section   */


.grow-your-academy-section{
   height: auto;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 20px;
    padding-bottom: 20px;
    background-color:  #0D7451;
  
}

@media (min-width: 1561px) {

    .grow-your-academy-section{
        height: auto;
         width: 1561px;
         display: flex;
         justify-content: center;
         align-items: center;
         flex-direction: column;
         padding-top: 20px;
         padding-bottom: 20px;
         background-color: #0D7451;
       
     }
    
}

.grow-your-academy-subsection{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}



.grow-your-academy-text-container{
     width: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (max-width: 992px) {
    .grow-your-academy-text-container{
       
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            padding-left: 20px;
            padding-right: 20px;
    }
}


.grow-your-academy-heading-1{
    font-family: "Montserrat", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
    color: white;

}

.grow-your-academy-description-1{
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-style: normal;
    color: white
    

    ;
}
