<!doctype html>
<html lang="zxx">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Learn about Ed-admin’s most recent updates">
        <meta name="keywords" content="Health screening for schools">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="/assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="/assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="/assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="/assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="/assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="/assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="/assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="/assets/css/responsive.css">

        <title>Ed-admin - Health Screening</title>

        <link rel="icon" type="image/png" href="/assets/img/favicon-Edadmin.ico">
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="Resources news" class="d-none"></span>
<!-- Start PopUps Area -->
<div data-include="/popups/demonow"></div> 
<div data-include="/popups/bookdemo"></div>
<div data-include="/popups/downloadnow"></div>
<div data-include="/popups/freedemo"></div>
<!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="/common/header2"></div>
        <!-- End Header Area -->

        <!-- Start Blog Details Area -->
        <section class="blog-details-area ptb-100">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 col-md-12">
                        <div class="blog-details-desc">
                            <div class="article-image">
                                <img src="/assets/img/resources-ed-admin-news/72ppi-1.jpg" alt="image">
                            </div>

                            <div class="article-content">
                                <div class="entry-meta">
                                    <ul>
                                        <li><i class='bx bx-time'></i> <a href="#"> Oct 20, 2020</a></li>
                                        <!-- <li><i class='bx bx-user'></i> <a href="#">Steven Smith</a></li> -->
                                    </ul>
                                </div>

                                <h1>Health Screening</h1>

                                <p>The COVID-19 pandemic has completely changed the way we live our lives, including how we learn and teach. Even now that an increasing number of institutions are starting the difficult process of resuming in-classroom learning, it’s not exactly “back to school” as we know it.</p>

                                <p>Many government guidelines require rigorous health screening and incident tracking for schools to offer in-person teaching. Unfortunately, with the relentless pace with which the pandemic took over the world, there are few institutions completely prepared for this new normal.</p>
                                <p>That’s why we at Ed-admin have accelerated the development of a brand new feature called “Health Screening” in the “Edana Staff Portal App”. This module will allow institutions to record vital data about student health and safety quickly and easily. The application has a symptom checker, including information on symptom severity, so that users can make informed decisions to keep their student safe.</p>
                                <p>Below is a quick introduction of how this module will work inside the “Edana Staff App”:
                                    Health Screening:  Quick access to reports on previous screenings for all students and staff.
                                </p>
                                <p>New Screening: Initiate a new screening by pressing the “+” button and populate the basic details field with a quick search for an “ID” or “name”. One touch can calculate a severity score, assisting the screener in making an informed decision on with recommended actions.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-12">
                        <aside class="widget-area" id="secondary">
                            <!-- Start Popular Post Area -->
                            <div data-include="/common/popular_post"></div>
                            <!-- End Popular Post Area Area -->
                        </aside>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Blog Details Area -->

        <!-- Start Free Trial Area -->
        <section class="free-trial-area ptb-100 bg-f4f5fe">
            <div class="container">
                <div class="free-trial-content">
                    <h2>Like to see Ed-admin in action? Ask for a demo!</h2>
                    <!-- <p>Qualify your leads & recognize the value of word your customer will love you</p> -->

                    <a href="#" class="default-btn" style="padding-left: 25px;"  onclick="Calendly.initPopupWidget({url: 'https://calendly.com/ed-admin/free-demo'});return false;">
                        <!-- <i class="bx bxs-hot"></i>  -->
                        Free Demo <span></span></a>
                </div>
            </div>

            <div class="shape10"><img src="/assets/img/shape/10.png" alt="image"></div>
            <div class="shape11"><img src="/assets/img/shape/7.png" alt="image"></div>
            <div class="shape12"><img src="/assets/img/shape/11.png" alt="image"></div>
            <div class="shape13"><img src="/assets/img/shape/12.png" alt="image"></div>
        </section>
        <!-- End Free Trial Area -->

        <!-- Start Header Area -->
        <div data-include="/common/footer"></div>
        <!-- End Header Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>
        
        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="/assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="/assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="/assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="/assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="/assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="/assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="/assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="/assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="/assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="/assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="/assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="/assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="/assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="/assets/js/particles.min.js"></script>
        <script src="/assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="/assets/js/main.js"></script>
        <script>
            // This event listener fires when the page is fully loaded.
            window.addEventListener("load", function() {
              // Redirects the browser to the home page.
              window.location.replace("../../error-404.html");
            });
          </script>
    </body>
</html>