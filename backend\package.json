{"name": "edadmin-backend", "version": "1.0.0", "description": "Ed-admin website backend API with PostgreSQL", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": ["edtech", "backend", "api", "postgresql", "jobs"], "author": "Ed-admin", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^7.0.3", "pg": "^8.16.0"}, "devDependencies": {"nodemon": "^3.0.1"}}