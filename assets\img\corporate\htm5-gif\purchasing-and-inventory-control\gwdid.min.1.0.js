(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var d,f=this||self;function h(a){this.i="";this.g=[];this.h="";a&&("string"==typeof a?this.o(a):this.m(a))}d=h.prototype;d.s=function(a){for(var b=this.i==a.i&&this.h==a.h&&this.g.length==a.g.length,c=0;b&&c<this.g.length;c++)b=this.g[c]==a.g[c];return b};d.D=function(){return""==this.l()};d.j=function(){this.i="";this.g=[];this.h=""};
d.m=function(a){this.j();if(!a)return!1;var b=a.getAttribute("data-gwd-group-def")||"",c=b?[]:[a];for(a=a.parentElement;a&&!b;)a.hasAttribute("data-gwd-group")?c.push(a):b=a.getAttribute("data-gwd-group-def"),a=a.parentElement;c=c.reverse();a=!!b;var g=c[0];if(!a&&!g.id)return!1;for(var e=[],m=a?0:1;m<c.length;m++){var n=c[m].getAttribute("data-gwd-grp-id");if(!n)return!1;e.push(n)}this.i=a?"":g.id;this.g=e;this.h=b||"";return!0};
d.o=function(a){this.j();a=a.split(" ");for(var b=""==a[0],c="",g=[],e=b?1:0;e<a.length;e++)if(a[e])c?g.push(a[e]):c=a[e];else return!1;if(!c)return!1;b?this.h=c:this.i=c;this.g=g;return!0};d.u=function(a){if(this.h)var b=a.querySelector('[data-gwd-group-def="'+this.h+'"]');else if(this.i){if(b=a.getElementById(this.i),!b){var c=a.querySelector("gwd-pagedeck");c?"function"==typeof c.getElementById&&(b=c.getElementById(this.i)):"document.body"==this.i&&(b=a.body)}}else return null;return k(this,b)};
d.v=function(a,b){if(!this.h)return null;for(a=b;a;){var c=a.getAttribute("data-gwd-group");if(c==this.h)break;a=c&&a!=b?null:a.parentElement}return a?k(this,a):null};d.l=function(){var a="";this.h?a=" "+this.h:this.i&&(a=this.i);for(var b=0;b<this.g.length;b++)a+=" "+this.g[b];return a};d.B=function(){return this.i};d.C=function(){return this.g};d.A=function(){return this.h};
function k(a,b){for(var c=0;b&&c<a.g.length;c++){for(var g=b.querySelector('[data-gwd-grp-id="'+a.g[c]+'"]'),e=g?g.parentElement:null;g&&e&&e!=b;)e.hasAttribute("data-gwd-group")?g=null:e=e.parentElement;b=g}return b};var l=["gwd","GwdId"],p=f;l[0]in p||"undefined"==typeof p.execScript||p.execScript("var "+l[0]);for(var q;l.length&&(q=l.shift());)l.length||void 0===h?p[q]&&p[q]!==Object.prototype[q]?p=p[q]:p=p[q]={}:p[q]=h;h.GROUP_REFERENCE_ATTR="data-gwd-group";h.prototype.equals=h.prototype.s;h.prototype.clear=h.prototype.j;h.prototype.isEmpty=h.prototype.D;h.prototype.setFromElement=h.prototype.m;h.prototype.setFromString=h.prototype.o;h.prototype.getElement=h.prototype.u;h.prototype.getElementInInstance=h.prototype.v;
h.prototype.getString=h.prototype.l;h.prototype.getLeadingId=h.prototype.B;h.prototype.getPseudoIds=h.prototype.C;h.prototype.getGroupName=h.prototype.A;}).call(this);
