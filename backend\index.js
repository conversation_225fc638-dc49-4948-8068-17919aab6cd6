const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const nodemailer = require('nodemailer');
const session = require('express-session');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');
const pgSession = require('connect-pg-simple')(session);
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = process.env.PORT || 3001;

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'ed-admin-jwt-secret-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'; // 7 days

// Middleware
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://127.0.0.1:5500',
        'http://localhost:3001',
        'https://edadminwebsite.ed-space.net',
        'https://ed-space.net',
        /\.ed-space\.net$/,
        /localhost:\d+$/
    ], // Allow your deployed domain and localhost
    credentials: true // Allow cookies to be sent
}));
app.use(express.json());
app.use(express.static('public'));
// Serve admin pages from the parent directory
app.use('/admin', express.static(path.join(__dirname, '..', 'admin')));
app.use(cookieParser());

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only PDF, DOC, and DOCX files are allowed.'), false);
        }
    }
});

// Email configuration
console.log('Email config:', {
    service: process.env.EMAIL_SERVICE,
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS ? '***hidden***' : 'undefined'
});

const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // Use TLS
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    },
    tls: {
        rejectUnauthorized: false
    }
});

// Database setup
console.log('Setting up database connection...');
if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
}

// Log database URL format (without credentials) for debugging
const dbUrl = new URL(process.env.DATABASE_URL);
console.log(`Connecting to database: ${dbUrl.protocol}//${dbUrl.hostname}:${dbUrl.port}${dbUrl.pathname}`);
console.log(`SSL Mode: ${process.env.NODE_ENV === 'production' ? 'enabled' : 'disabled'}`);

// Validate CockroachDB connection string format
if (!process.env.DATABASE_URL.includes('cockroachlabs.cloud') && !process.env.DATABASE_URL.includes('localhost')) {
    console.warn('Warning: DATABASE_URL does not appear to be a CockroachDB connection string');
}

// Enhanced database configuration for PostgreSQL/CockroachDB
const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: false,
        require: true
    },
    connectionTimeoutMillis: 30000,
    idleTimeoutMillis: 30000,
    max: 10, // Maximum number of clients in the pool
    min: 2,  // Minimum number of clients in the pool
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
});
console.log('Database pool created.');

// Session configuration with PostgreSQL store (after pool is created)
app.use(session({
    store: new pgSession({
        pool: pool, // Use existing database pool
        tableName: 'session' // Use 'session' table for sessions
    }),
    secret: process.env.SESSION_SECRET || 'ed-admin-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true in production with HTTPS
        httpOnly: true,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        sameSite: 'lax'
    },
    name: 'edadmin.sid' // Custom session name
}));

// Database connection test with retry logic
async function testDatabaseConnection(retries = 5) {
    for (let i = 0; i < retries; i++) {
        try {
            console.log(`Testing database connection (attempt ${i + 1}/${retries})...`);
            const client = await pool.connect();
            await client.query('SELECT 1');
            client.release();
            console.log('Database connection successful!');
            return true;
        } catch (error) {
            console.error(`Database connection attempt ${i + 1} failed:`, error.message);
            if (i === retries - 1) {
                throw error;
            }
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
    }
}

// Create tables if they don't exist
async function initializeDatabase() {
    try {
        // Test connection first
        await testDatabaseConnection();

        // Create jobs table
        await pool.query(`CREATE TABLE IF NOT EXISTS jobs (
            id SERIAL PRIMARY KEY,
            title TEXT NOT NULL,
            department TEXT NOT NULL,
            location TEXT NOT NULL,
            type TEXT NOT NULL,
            description TEXT NOT NULL,
            benefits TEXT NOT NULL,
            what_youll_do TEXT NOT NULL,
            what_youll_need TEXT NOT NULL,
            requirements TEXT NOT NULL,
            image_url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create admin_users table
        await pool.query(`CREATE TABLE IF NOT EXISTS admin_users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email VARCHAR(100),
            is_default_user BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )`);

        // Add is_default_user column if it doesn't exist (for existing databases)
        try {
            await pool.query(`ALTER TABLE admin_users ADD COLUMN IF NOT EXISTS is_default_user BOOLEAN DEFAULT FALSE`);
        } catch (error) {
            // Column might already exist, ignore error
            console.log('is_default_user column already exists or could not be added');
        }

        // Create session table for persistent sessions (CockroachDB compatible)
        await pool.query(`CREATE TABLE IF NOT EXISTS "session" (
            "sid" varchar NOT NULL,
            "sess" json NOT NULL,
            "expire" timestamp(6) NOT NULL
        );`);

        await pool.query(`ALTER TABLE "session" ADD CONSTRAINT "session_pkey" PRIMARY KEY ("sid");`).catch(() => {
            // Constraint might already exist
        });

        await pool.query(`CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "session" ("expire");`);

        console.log('Session table created/verified');

        console.log('Database tables initialized successfully!');

        // Create default admin user
        const defaultUserResult = await pool.query("SELECT COUNT(*) as count FROM admin_users WHERE username = $1", ['<EMAIL>']);
        const defaultUserExists = parseInt(defaultUserResult.rows[0].count) > 0;

        if (!defaultUserExists) {
            console.log('Creating default admin user...');

            // Hash the default password
            const hashedPassword = await bcrypt.hash('4XXG?rgs', 10);

            await pool.query(
                `INSERT INTO admin_users (username, password_hash, email, is_default_user)
                 VALUES ($1, $2, $3, $4)`,
                ['<EMAIL>', hashedPassword, '<EMAIL>', true]
            );

            console.log('Default admin user created successfully!');
            console.log('Default credentials: username=<EMAIL>, password=4XXG?rgs');
        } else {
            console.log('Default admin user already exists.');
        }

        // Create demo admin user if it doesn't exist (for backward compatibility)
        const demoUserResult = await pool.query("SELECT COUNT(*) as count FROM admin_users WHERE username = $1", ['admin']);
        const demoUserExists = parseInt(demoUserResult.rows[0].count) > 0;

        if (!demoUserExists) {
            console.log('Creating demo admin user...');

            // Hash the demo password
            const hashedPassword = await bcrypt.hash('admin123', 10);

            await pool.query(
                `INSERT INTO admin_users (username, password_hash, email, is_default_user)
                 VALUES ($1, $2, $3, $4)`,
                ['admin', hashedPassword, '<EMAIL>', false]
            );

            console.log('Demo admin user created successfully!');
            console.log('Demo credentials: username=admin, password=admin123');
        }

    } catch (error) {
        console.error('Error initializing database:', error);
    }
}

// Initialize database on startup
initializeDatabase();

// Authentication middleware
function requireAuth(req, res, next) {
    // Check for auth token in cookies first, then Authorization header
    let token = req.cookies.edAdminToken;

    // If no cookie, check Authorization header
    if (!token && req.headers.authorization) {
        const authHeader = req.headers.authorization;
        if (authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7);
        }
    }

    if (!token) {
        console.log('No auth token found in cookies or headers');
        return res.status(401).json({ error: 'Authentication required - please login first' });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;

        console.log('Valid token found for user:', decoded.adminUsername);
        return next();
    } catch (error) {
        console.error('Token verification failed:', error.message);
        // Clear invalid token if it was from cookies
        if (req.cookies.edAdminToken) {
            res.clearCookie('edAdminToken');
        }
        return res.status(401).json({ error: 'Invalid token - please login again' });
    }
}

// Authentication Routes
app.post('/api/admin/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Find admin user
        const result = await pool.query(
            'SELECT id, username, password_hash FROM admin_users WHERE username = $1',
            [username]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid username or password' });
        }

        const admin = result.rows[0];

        // Verify password
        const isValidPassword = await bcrypt.compare(password, admin.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid username or password' });
        }

        // Create simple auth token
        const tokenPayload = {
            adminId: admin.id,
            adminUsername: admin.username,
            loginTime: Date.now()
        };

        const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

        // Set auth token in cookie (not HTTP-only so frontend can read it)
        console.log('Setting cookie for origin:', req.get('origin') || 'no origin header');
        console.log('Request host:', req.get('host'));

        res.cookie('edAdminToken', token, {
            httpOnly: false, // Allow frontend to read the cookie
            secure: false, // Set to true in production with HTTPS
            sameSite: 'lax',
            path: '/', // Ensure cookie is available for all paths
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
            // Don't set domain to allow localhost and file:// origins
        });

        // Also maintain session for backend use
        req.session.adminId = admin.id;
        req.session.adminUsername = admin.username;

        console.log('Auth token created and set in cookie:', token.substring(0, 20) + '...');

        // Update last login
        await pool.query(
            'UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
            [admin.id]
        );

        console.log(`Admin login successful: ${username}`);

        res.json({
            success: true,
            message: 'Login successful',
            token: token, // Include token in response for cross-origin scenarios
            admin: {
                id: admin.id,
                username: admin.username
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/admin/logout', (req, res) => {
    console.log('Logout request received');

    // Clear auth token cookie
    res.clearCookie('edAdminToken', { path: '/' });

    // Also destroy session
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
            return res.status(500).json({ error: 'Failed to logout' });
        }

        res.clearCookie('edadmin.sid'); // Clear session cookie with custom name
        console.log('Logout successful - token cleared');
        res.json({ success: true, message: 'Logout successful' });
    });
});

// New token verification endpoint (supports both cookies and Authorization header)
app.post('/api/admin/verify-token', (req, res) => {
    console.log('=== TOKEN VERIFICATION DEBUG ===');
    console.log('Request origin:', req.get('origin') || 'no origin header');
    console.log('Request host:', req.get('host'));
    console.log('Authorization header:', req.get('authorization') || 'no auth header');
    console.log('Has edAdminToken cookie:', !!req.cookies.edAdminToken);

    // Check for auth token in Authorization header first, then cookies
    let token = null;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        token = req.headers.authorization.substring(7);
        console.log('Token found in Authorization header');
    } else if (req.cookies.edAdminToken) {
        token = req.cookies.edAdminToken;
        console.log('Token found in cookies');
    }

    if (!token) {
        console.log('Token verification - No auth token found');
        console.log('=== TOKEN VERIFICATION END ===');
        return res.status(401).json({
            valid: false,
            message: 'No token provided'
        });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('Token verification - Valid token found for user:', decoded.adminUsername);
        console.log('=== TOKEN VERIFICATION SUCCESS ===');

        return res.json({
            valid: true,
            user: {
                id: decoded.adminId,
                username: decoded.adminUsername
            }
        });
    } catch (error) {
        console.error('Token verification - Token verification failed:', error.message);
        console.log('=== TOKEN VERIFICATION FAILED ===');
        return res.status(401).json({
            valid: false,
            message: 'Invalid token'
        });
    }
});

// Legacy session check endpoint (for backward compatibility)
app.get('/api/admin/check-session', (req, res) => {
    console.log('=== LEGACY SESSION CHECK ===');

    // Check for auth token in cookies
    const token = req.cookies.edAdminToken;

    if (!token) {
        console.log('Legacy session check - No auth token found');
        return res.status(401).json({
            authenticated: false,
            message: 'Please login first'
        });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('Legacy session check - Valid token found for user:', decoded.adminUsername);

        return res.json({
            authenticated: true,
            admin: {
                id: decoded.adminId,
                username: decoded.adminUsername
            }
        });
    } catch (error) {
        console.error('Legacy session check - Token verification failed:', error.message);
        // Clear invalid token
        res.clearCookie('edAdminToken', { path: '/' });
        return res.status(401).json({
            authenticated: false,
            message: 'Invalid token - please login again'
        });
    }
});

// User Management Routes (Protected)
app.get('/api/admin/users', requireAuth, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT id, username, email, is_default_user, created_at, last_login FROM admin_users ORDER BY is_default_user DESC, created_at DESC'
        );

        res.json(result.rows);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/admin/users', requireAuth, async (req, res) => {
    try {
        const { username, password, email } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Check if username already exists
        const existingUser = await pool.query(
            'SELECT id FROM admin_users WHERE username = $1',
            [username]
        );

        if (existingUser.rows.length > 0) {
            return res.status(400).json({ error: 'Username already exists' });
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Insert new user (never as default user)
        const result = await pool.query(
            'INSERT INTO admin_users (username, password_hash, email, is_default_user) VALUES ($1, $2, $3, $4) RETURNING id, username, email, created_at',
            [username, hashedPassword, email || null, false]
        );

        const newUser = result.rows[0];

        console.log(`New admin user created: ${username} by ${req.user.adminUsername}`);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: newUser
        });

    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.delete('/api/admin/users/:id', requireAuth, async (req, res) => {
    try {
        const userId = req.params.id;
        const currentUserId = req.user.adminId;

        // Prevent users from deleting themselves
        if (parseInt(userId) === parseInt(currentUserId)) {
            return res.status(400).json({ error: 'Cannot delete your own account' });
        }

        // Check if user exists and if it's a default user
        const userResult = await pool.query(
            'SELECT username, is_default_user FROM admin_users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const user = userResult.rows[0];

        // Prevent deletion of default user
        if (user.is_default_user) {
            return res.status(400).json({ error: 'Cannot delete the default admin user' });
        }

        // Delete the user
        await pool.query('DELETE FROM admin_users WHERE id = $1', [userId]);

        console.log(`Admin user deleted: ${user.username} by ${req.user.adminUsername}`);

        res.json({
            success: true,
            message: 'User deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Change password endpoint
app.put('/api/admin/change-password', requireAuth, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.adminId;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }

        if (newPassword.length < 6) {
            return res.status(400).json({ error: 'New password must be at least 6 characters long' });
        }

        // Get current user's password hash
        const userResult = await pool.query(
            'SELECT password_hash FROM admin_users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const currentPasswordHash = userResult.rows[0].password_hash;

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentPasswordHash);

        if (!isCurrentPasswordValid) {
            return res.status(400).json({ error: 'Current password is incorrect' });
        }

        // Hash new password
        const newPasswordHash = await bcrypt.hash(newPassword, 10);

        // Update password
        await pool.query(
            'UPDATE admin_users SET password_hash = $1 WHERE id = $2',
            [newPasswordHash, userId]
        );

        console.log(`Password changed for user: ${req.user.adminUsername}`);

        res.json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// API Routes with enhanced error handling
app.get('/api/jobs', async (req, res) => {
    try {
        // Test connection before querying
        const client = await pool.connect();
        const result = await client.query("SELECT * FROM jobs ORDER BY created_at DESC");
        client.release();
        res.json(result.rows);
    } catch (err) {
        console.error('Error fetching jobs:', err);

        // Provide more specific error messages
        if (err.message.includes('Connection terminated') || err.message.includes('connect')) {
            res.status(503).json({
                error: 'Database connection error. Please try again later.',
                details: 'The service is temporarily unavailable due to database connectivity issues.'
            });
        } else {
            res.status(500).json({
                error: 'Internal server error',
                details: 'An unexpected error occurred while fetching jobs.'
            });
        }
    }
});

app.get('/api/jobs/:id', async (req, res) => {
    try {
        const jobId = req.params.id;
        const result = await pool.query("SELECT * FROM jobs WHERE id = $1", [jobId]);

        if (result.rows.length === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json(result.rows[0]);
    } catch (err) {
        console.error('Error fetching job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Admin API Routes for Job Management (Protected)
app.post('/api/admin/jobs', requireAuth, async (req, res) => {
    try {
        const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

        if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
            return res.status(400).json({ error: 'All fields except image_url are required' });
        }

        const finalImageUrl = image_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

        const result = await pool.query(
            `INSERT INTO jobs (title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING id`,
            [title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl]
        );

        const newJobId = result.rows[0].id;

        res.status(201).json({
            id: newJobId,
            message: 'Job created successfully',
            job: { id: newJobId, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    } catch (err) {
        console.error('Error creating job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.put('/api/admin/jobs/:id', requireAuth, async (req, res) => {
    try {
        const jobId = req.params.id;
        const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

        if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
            return res.status(400).json({ error: 'All fields except image_url are required' });
        }

        const finalImageUrl = image_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

        const result = await pool.query(
            `UPDATE jobs SET title = $1, department = $2, location = $3, type = $4,
             description = $5, benefits = $6, what_youll_do = $7, what_youll_need = $8, requirements = $9, image_url = $10
             WHERE id = $11`,
            [title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl, jobId]
        );

        if (result.rowCount === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json({
            message: 'Job updated successfully',
            job: { id: jobId, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    } catch (err) {
        console.error('Error updating job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.delete('/api/admin/jobs/:id', requireAuth, async (req, res) => {
    try {
        const jobId = req.params.id;

        const result = await pool.query('DELETE FROM jobs WHERE id = $1', [jobId]);

        if (result.rowCount === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json({ message: 'Job deleted successfully' });
    } catch (err) {
        console.error('Error deleting job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Job Application endpoint
app.post('/api/applications', upload.single('cv'), async (req, res) => {
    try {
        const { name, email, linkedinLink, jobId, jobTitle, jobDescription } = req.body;
        const cvFile = req.file;

        // Debug logging
        console.log('Application received:', {
            name,
            email,
            linkedinLink: linkedinLink || 'Not provided',
            jobId,
            jobTitle,
            hasCV: !!cvFile
        });

        // Validate required fields
        if (!name || !email || !jobId || !jobTitle || !cvFile) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Prepare email content
        const emailSubject = `Job Application: ${jobTitle} - ${name}`;
        const emailHtml = `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
                            New Job Application Received
                        </h2>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Details</h3>
                            <p><strong>Position:</strong> ${jobTitle}</p>
                            <p><strong>Job ID:</strong> ${jobId}</p>
                        </div>

                        <div style="background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Applicant Information</h3>
                            <p><strong>Name:</strong> ${name}</p>
                            <p><strong>Email:</strong> ${email}</p>
                            ${linkedinLink ? `<p><strong>LinkedIn:</strong> <a href="${linkedinLink}" target="_blank">${linkedinLink}</a></p>` : ''}
                        </div>

                        <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Description</h3>
                            <p style="white-space: pre-wrap;">${jobDescription}</p>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
                            <p>This application was submitted through the Ed-admin careers website.</p>
                            <p>CV/Resume is attached to this email.</p>
                        </div>
                    </div>
                </body>
            </html>
        `;

        // Email options
        const mailOptions = {
            from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
            to: '<EMAIL>', // Send to recruitment email
            subject: emailSubject,
            html: emailHtml,
            attachments: [
                {
                    filename: `${name}_CV_${jobTitle.replace(/\s+/g, '_')}.${cvFile.originalname.split('.').pop()}`,
                    content: cvFile.buffer,
                    contentType: cvFile.mimetype
                }
            ]
        };

        // Send email
        await transporter.sendMail(mailOptions);

        console.log(`Job application received for ${jobTitle} from ${name} (${email})`);

        res.json({
            success: true,
            message: 'Application submitted successfully'
        });

    } catch (error) {
        console.error('Error processing job application:', error);
        res.status(500).json({
            error: 'Failed to submit application. Please try again later.'
        });
    }
});

// Health check endpoint with database connectivity
app.get('/api/health', async (req, res) => {
    const health = {
        status: 'OK',
        message: 'Ed-admin Jobs API is running',
        timestamp: new Date().toISOString(),
        database: 'unknown'
    };

    try {
        // Test database connection
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        health.database = 'connected';
    } catch (error) {
        health.database = 'disconnected';
        health.status = 'DEGRADED';
        health.error = error.message;
    }

    res.json(health);
});

// Start server
app.listen(PORT, () => {
    console.log(`Ed-admin Jobs API server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
    console.log(`Jobs API: http://localhost:${PORT}/api/jobs`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down server...');
    try {
        await pool.end();
        console.log('Database connection pool closed.');
    } catch (err) {
        console.error('Error closing database pool:', err);
    }
    process.exit(0);
});