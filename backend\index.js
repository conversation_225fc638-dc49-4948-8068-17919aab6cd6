const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const nodemailer = require('nodemailer');
const session = require('express-session');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');
const pgSession = require('connect-pg-simple')(session);
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = process.env.PORT || 3001;

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'ed-admin-jwt-secret-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'; // 7 days

// Middleware
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        'https://edadminwebsite.ed-space.net',
        'https://ed-space.net',
        /\.ed-space\.net$/,
        /localhost:\d+$/
    ], // Allow your deployed domain and localhost
    credentials: true // Allow cookies to be sent
}));
app.use(express.json());
app.use(express.static('public'));
// Serve admin pages from the parent directory
app.use('/admin', express.static(path.join(__dirname, '..', 'admin')));
app.use(cookieParser());

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only PDF, DOC, and DOCX files are allowed.'), false);
        }
    }
});

// Email configuration
console.log('Email config:', {
    service: process.env.EMAIL_SERVICE,
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS ? '***hidden***' : 'undefined'
});

const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // Use TLS
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    },
    tls: {
        rejectUnauthorized: false
    }
});

// Database setup
console.log('Setting up database connection...');
if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
}

// Log database URL format (without credentials) for debugging
const dbUrl = new URL(process.env.DATABASE_URL);
console.log(`Connecting to database: ${dbUrl.protocol}//${dbUrl.hostname}:${dbUrl.port}${dbUrl.pathname}`);
console.log(`SSL Mode: ${process.env.NODE_ENV === 'production' ? 'enabled' : 'disabled'}`);

// Validate CockroachDB connection string format
if (!process.env.DATABASE_URL.includes('cockroachlabs.cloud') && !process.env.DATABASE_URL.includes('localhost')) {
    console.warn('Warning: DATABASE_URL does not appear to be a CockroachDB connection string');
}

// Enhanced database configuration for PostgreSQL/CockroachDB
const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: false,
        require: true
    },
    connectionTimeoutMillis: 30000,
    idleTimeoutMillis: 30000,
    max: 10, // Maximum number of clients in the pool
    min: 2,  // Minimum number of clients in the pool
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
});
console.log('Database pool created.');

// Session configuration with PostgreSQL store (after pool is created)
app.use(session({
    store: new pgSession({
        pool: pool, // Use existing database pool
        tableName: 'session' // Use 'session' table for sessions
    }),
    secret: process.env.SESSION_SECRET || 'ed-admin-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true in production with HTTPS
        httpOnly: true,
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        sameSite: 'lax'
    },
    name: 'edadmin.sid' // Custom session name
}));

// Database connection test with retry logic
async function testDatabaseConnection(retries = 5) {
    for (let i = 0; i < retries; i++) {
        try {
            console.log(`Testing database connection (attempt ${i + 1}/${retries})...`);
            const client = await pool.connect();
            await client.query('SELECT 1');
            client.release();
            console.log('Database connection successful!');
            return true;
        } catch (error) {
            console.error(`Database connection attempt ${i + 1} failed:`, error.message);
            if (i === retries - 1) {
                throw error;
            }
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
    }
}

// Create tables if they don't exist
async function initializeDatabase() {
    try {
        // Test connection first
        await testDatabaseConnection();

        // Create jobs table
        await pool.query(`CREATE TABLE IF NOT EXISTS jobs (
            id SERIAL PRIMARY KEY,
            title TEXT NOT NULL,
            department TEXT NOT NULL,
            location TEXT NOT NULL,
            type TEXT NOT NULL,
            description TEXT NOT NULL,
            benefits TEXT NOT NULL,
            what_youll_do TEXT NOT NULL,
            what_youll_need TEXT NOT NULL,
            requirements TEXT NOT NULL,
            image_url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create admin_users table
        await pool.query(`CREATE TABLE IF NOT EXISTS admin_users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email VARCHAR(100),
            is_default_user BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )`);

        // Add is_default_user column if it doesn't exist (for existing databases)
        try {
            await pool.query(`ALTER TABLE admin_users ADD COLUMN IF NOT EXISTS is_default_user BOOLEAN DEFAULT FALSE`);
        } catch (error) {
            // Column might already exist, ignore error
            console.log('is_default_user column already exists or could not be added');
        }

        // Create session table for persistent sessions (CockroachDB compatible)
        await pool.query(`CREATE TABLE IF NOT EXISTS "session" (
            "sid" varchar NOT NULL,
            "sess" json NOT NULL,
            "expire" timestamp(6) NOT NULL
        );`);

        await pool.query(`ALTER TABLE "session" ADD CONSTRAINT "session_pkey" PRIMARY KEY ("sid");`).catch(() => {
            // Constraint might already exist
        });

        await pool.query(`CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "session" ("expire");`);

        console.log('Session table created/verified');

        // Insert fresh demo data with new Unsplash images
        const demoJobs = [
            {
                title: 'Senior Full Stack Developer',
                department: 'Engineering',
                location: 'Remote / San Francisco',
                type: 'Full-time',
                description: 'Join our engineering team to build cutting-edge education management software. You will work on scalable web applications, API development, and help shape the future of educational technology that serves millions of students and educators worldwide.',
                benefits: 'Competitive salary ($120k-$180k) and equity package• Comprehensive health, dental, and vision insurance• Flexible work arrangements and unlimited PTO• $3,000 annual professional development budget• Annual team retreats and conferences• Latest tech equipment and home office setup',
                what_youll_do: 'Design and develop scalable web applications using modern frameworks• Build robust APIs and microservices architecture• Collaborate with product and design teams on feature development• Mentor junior developers and conduct code reviews• Participate in technical discussions and architectural decisions• Contribute to open-source projects and technical blog posts',
                what_youll_need: 'Strong proficiency in JavaScript/TypeScript, Node.js, and React• Experience with PostgreSQL, MongoDB, or similar databases• Knowledge of cloud platforms (AWS, GCP, or Azure)• Understanding of software engineering best practices and design patterns• Excellent problem-solving and communication skills• Experience with containerization (Docker, Kubernetes)',
                requirements: '5+ years of full-stack software development experience• Bachelor\'s degree in Computer Science or related field• Experience with agile development methodologies and CI/CD• Strong understanding of RESTful APIs and GraphQL• Experience with version control systems (Git) and testing frameworks• Previous experience in EdTech or SaaS products preferred',
                image_url: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Senior Product Manager',
                department: 'Product',
                location: 'Remote / New York',
                type: 'Full-time',
                description: 'Lead product strategy and development for our education management platform. Work closely with engineering, design, and customer success teams to deliver exceptional user experiences that transform how educational institutions operate.',
                benefits: 'Competitive salary ($130k-$200k) and equity• Health, dental, and vision insurance• Flexible PTO policy and sabbatical options• $4,000 remote work stipend• Professional development opportunities and conference attendance• Stock options with high growth potential',
                what_youll_do: 'Define product roadmap and strategy for multiple product lines• Gather and analyze user feedback through interviews and data analysis• Work with engineering teams on feature development and prioritization• Conduct market research and competitive analysis• Collaborate with design team on user experience improvements• Present product updates and metrics to executive stakeholders',
                what_youll_need: 'Strong analytical and strategic thinking skills with data-driven approach• Experience with product management tools (Jira, Figma, Mixpanel, etc.)• Deep understanding of education technology landscape and challenges• Excellent communication and leadership abilities• Experience with A/B testing and product analytics• Customer-centric mindset with empathy for end users',
                requirements: '5+ years of product management experience in B2B SaaS• MBA or equivalent experience preferred• Experience in education technology or related vertical• Strong project management and stakeholder management skills• Experience working with cross-functional teams in agile environment• Track record of successful product launches and growth metrics',
                image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Senior UX/UI Designer',
                department: 'Design',
                location: 'Remote / Los Angeles',
                type: 'Full-time',
                description: 'Create intuitive and engaging user experiences for our education management platform. Work on both web and mobile interfaces that serve students, teachers, and administrators, ensuring accessibility and usability across all touchpoints.',
                benefits: 'Competitive salary ($110k-$160k)• Comprehensive benefits package including mental health support• Creative freedom and design autonomy• Latest design tools and software licenses• Flexible work schedule and creative time• Design conference attendance and workshop budget',
                what_youll_do: 'Design user interfaces for web and mobile applications across multiple platforms• Create wireframes, prototypes, and high-fidelity mockups using modern design tools• Conduct user research, interviews, and usability testing sessions• Collaborate with product and engineering teams on implementation• Maintain and evolve design system and component libraries• Present design concepts and rationale to stakeholders and clients',
                what_youll_need: 'Expert proficiency in design tools (Figma, Sketch, Adobe Creative Suite)• Strong understanding of UX principles, methodologies, and accessibility standards• Experience with responsive and mobile-first design approaches• Portfolio demonstrating design process, thinking, and impact• Knowledge of HTML/CSS and basic front-end development• Experience with design systems and atomic design principles',
                requirements: '4+ years of UX/UI design experience in digital products• Bachelor\'s degree in Design, HCI, Psychology, or related field• Experience designing for B2B, SaaS, or education products• Strong portfolio showcasing end-to-end design process• Experience with design systems and component libraries• Knowledge of user research methods and usability testing',
                image_url: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Customer Success Manager',
                department: 'Customer Success',
                location: 'Remote / Chicago',
                type: 'Full-time',
                description: 'Help educational institutions maximize their success with our platform. Build strong relationships with customers and ensure they achieve their goals using our education management software while driving retention and expansion.',
                benefits: 'Base salary ($80k-$120k) plus performance bonuses• Health and wellness benefits including gym membership• $2,000 professional development budget• Flexible work arrangements and mental health days• Team building events and customer conference attendance• Career advancement opportunities with clear growth paths',
                what_youll_do: 'Onboard new customers and ensure successful implementation and adoption• Provide ongoing support, training, and best practice guidance to users• Identify opportunities for account expansion and upselling• Gather customer feedback for product improvement and feature requests• Create and maintain customer success documentation and playbooks• Collaborate with sales, product, and engineering teams on customer initiatives',
                what_youll_need: 'Excellent communication and interpersonal skills with emotional intelligence• Experience in customer-facing roles with proven track record• Understanding of education sector challenges and institutional dynamics• Strong problem-solving abilities and consultative approach• Proficiency with CRM and customer success tools (Salesforce, Gainsight, etc.)• Data analysis skills and comfort with metrics and reporting',
                requirements: '3+ years of customer success or account management experience• Bachelor\'s degree preferred, education background a plus• Experience in SaaS or education technology industry• Strong organizational and time management skills• Ability to work independently and as part of a collaborative team• Experience with customer onboarding and training programs',
                image_url: 'https://images.unsplash.com/photo-**********-97eda2d62296?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'DevOps Engineer',
                department: 'Engineering',
                location: 'Remote / Austin',
                type: 'Full-time',
                description: 'Build and maintain the infrastructure that powers our education management platform. Ensure high availability, security, and scalability for systems serving educational institutions worldwide.',
                benefits: 'Competitive salary ($110k-$170k) and equity package• Comprehensive health benefits and HSA contribution• Flexible work schedule and unlimited PTO• $3,500 home office and equipment budget• Professional certification reimbursement• On-call compensation and rotation flexibility',
                what_youll_do: 'Design and implement CI/CD pipelines and deployment automation• Manage cloud infrastructure on AWS/GCP with Infrastructure as Code• Monitor system performance and implement alerting and logging solutions• Ensure security compliance and implement best practices• Collaborate with development teams on application deployment and scaling• Participate in incident response and post-mortem analysis',
                what_youll_need: 'Strong experience with cloud platforms (AWS, GCP, or Azure)• Proficiency in containerization technologies (Docker, Kubernetes)• Experience with Infrastructure as Code tools (Terraform, CloudFormation)• Knowledge of monitoring and logging tools (Prometheus, Grafana, ELK stack)• Understanding of security best practices and compliance requirements• Scripting skills in Python, Bash, or similar languages',
                requirements: '4+ years of DevOps or Site Reliability Engineering experience• Bachelor\'s degree in Computer Science or related field• Experience with database administration and backup strategies• Knowledge of networking, load balancing, and CDN configuration• Experience with automated testing and quality assurance processes• Previous experience in high-availability, mission-critical systems',
                image_url: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Data Scientist',
                department: 'Analytics',
                location: 'Remote / Boston',
                type: 'Full-time',
                description: 'Leverage data to drive insights and improve educational outcomes. Work with large datasets to build predictive models and analytics that help educational institutions make data-driven decisions.',
                benefits: 'Competitive salary ($120k-$180k) and equity• Health, dental, vision, and mental health benefits• Flexible work arrangements and research time• $4,000 conference and learning budget• Access to cutting-edge tools and computing resources• Collaboration with leading education researchers',
                what_youll_do: 'Analyze large educational datasets to identify trends and insights• Build predictive models for student success and institutional outcomes• Develop dashboards and visualizations for stakeholders• Collaborate with product teams on data-driven feature development• Conduct A/B tests and statistical analysis• Present findings to executive team and customers',
                what_youll_need: 'Strong proficiency in Python, R, and SQL• Experience with machine learning frameworks (scikit-learn, TensorFlow, PyTorch)• Knowledge of statistical analysis and experimental design• Experience with data visualization tools (Tableau, Power BI, or similar)• Understanding of education metrics and learning analytics• Strong communication skills for presenting technical findings',
                requirements: 'Master\'s or PhD in Data Science, Statistics, Computer Science, or related field• 3+ years of data science experience in industry• Experience with big data technologies (Spark, Hadoop) preferred• Knowledge of education domain and learning sciences a plus• Experience with cloud-based analytics platforms• Strong problem-solving and critical thinking skills',
                image_url: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Marketing Manager',
                department: 'Marketing',
                location: 'Remote / Seattle',
                type: 'Full-time',
                description: 'Drive growth and brand awareness for our education management platform. Develop and execute marketing strategies that resonate with educational institutions and decision-makers.',
                benefits: 'Competitive salary ($90k-$140k) plus performance bonuses• Comprehensive benefits package• Creative freedom and campaign ownership• $2,500 marketing tools and conference budget• Flexible work schedule and creative time• Stock options and growth opportunities',
                what_youll_do: 'Develop and execute integrated marketing campaigns across multiple channels• Create compelling content for blogs, whitepapers, and case studies• Manage social media presence and community engagement• Collaborate with sales team on lead generation and nurturing• Analyze campaign performance and optimize for ROI• Coordinate with design team on marketing materials and brand consistency',
                what_youll_need: 'Strong experience in B2B marketing, preferably in SaaS or EdTech• Proficiency with marketing automation tools (HubSpot, Marketo, etc.)• Content creation skills and understanding of SEO/SEM• Experience with social media marketing and community building• Data analysis skills and comfort with marketing metrics• Creative thinking and project management abilities',
                requirements: '4+ years of marketing experience in B2B environment• Bachelor\'s degree in Marketing, Communications, or related field• Experience with education sector marketing preferred• Strong writing and communication skills• Experience with CRM and marketing analytics tools• Knowledge of digital marketing trends and best practices',
                image_url: 'https://images.unsplash.com/photo-1552581234-26160f608093?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            },
            {
                title: 'Sales Development Representative',
                department: 'Sales',
                location: 'Remote / Denver',
                type: 'Full-time',
                description: 'Generate and qualify leads for our education management platform. Be the first point of contact for potential customers and help them understand how our solution can transform their institution.',
                benefits: 'Base salary ($60k-$80k) plus uncapped commission• Health and wellness benefits• Professional sales training and development• $1,500 home office setup budget• Team incentives and recognition programs• Clear path to Account Executive role',
                what_youll_do: 'Prospect and qualify leads through various channels (email, phone, social)• Conduct discovery calls to understand customer needs and challenges• Schedule qualified meetings for Account Executives• Maintain accurate records in CRM system• Collaborate with marketing on lead generation campaigns• Participate in trade shows and industry events',
                what_youll_need: 'Excellent communication and interpersonal skills• Strong phone presence and email writing abilities• Resilience and persistence in a fast-paced environment• Basic understanding of education sector and institutional sales• Proficiency with CRM systems and sales tools• Goal-oriented mindset with competitive drive',
                requirements: '1-3 years of sales or business development experience• Bachelor\'s degree preferred• Experience in B2B sales environment• Strong organizational and time management skills• Ability to work independently and as part of a team• Interest in education technology and making an impact',
                image_url: 'https://images.unsplash.com/photo-*************-90a7e086ee0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
            }
        ];

        // Clear existing demo data and insert fresh data
        console.log('Clearing existing job data...');
        await pool.query("DELETE FROM jobs");

        console.log('Inserting fresh demo job data...');
        for (const job of demoJobs) {
            await pool.query(
                `INSERT INTO jobs (title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url)
                 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
                [job.title, job.department, job.location, job.type, job.description, job.benefits, job.what_youll_do, job.what_youll_need, job.requirements, job.image_url]
            );
        }

        console.log('Fresh demo job data inserted successfully!');

        // Create default admin user
        const defaultUserResult = await pool.query("SELECT COUNT(*) as count FROM admin_users WHERE username = $1", ['<EMAIL>']);
        const defaultUserExists = parseInt(defaultUserResult.rows[0].count) > 0;

        if (!defaultUserExists) {
            console.log('Creating default admin user...');

            // Hash the default password
            const hashedPassword = await bcrypt.hash('4XXG?rgs', 10);

            await pool.query(
                `INSERT INTO admin_users (username, password_hash, email, is_default_user)
                 VALUES ($1, $2, $3, $4)`,
                ['<EMAIL>', hashedPassword, '<EMAIL>', true]
            );

            console.log('Default admin user created successfully!');
            console.log('Default credentials: username=<EMAIL>, password=4XXG?rgs');
        } else {
            console.log('Default admin user already exists.');
        }

        // Create demo admin user if it doesn't exist (for backward compatibility)
        const demoUserResult = await pool.query("SELECT COUNT(*) as count FROM admin_users WHERE username = $1", ['admin']);
        const demoUserExists = parseInt(demoUserResult.rows[0].count) > 0;

        if (!demoUserExists) {
            console.log('Creating demo admin user...');

            // Hash the demo password
            const hashedPassword = await bcrypt.hash('admin123', 10);

            await pool.query(
                `INSERT INTO admin_users (username, password_hash, email, is_default_user)
                 VALUES ($1, $2, $3, $4)`,
                ['admin', hashedPassword, '<EMAIL>', false]
            );

            console.log('Demo admin user created successfully!');
            console.log('Demo credentials: username=admin, password=admin123');
        }

    } catch (error) {
        console.error('Error initializing database:', error);
    }
}

// Initialize database on startup
initializeDatabase();

// Authentication middleware
function requireAuth(req, res, next) {
    // Check for auth token in cookies first, then Authorization header
    let token = req.cookies.edAdminToken;

    // If no cookie, check Authorization header
    if (!token && req.headers.authorization) {
        const authHeader = req.headers.authorization;
        if (authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7);
        }
    }

    if (!token) {
        console.log('No auth token found in cookies or headers');
        return res.status(401).json({ error: 'Authentication required - please login first' });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        req.user = decoded;

        console.log('Valid token found for user:', decoded.adminUsername);
        return next();
    } catch (error) {
        console.error('Token verification failed:', error.message);
        // Clear invalid token if it was from cookies
        if (req.cookies.edAdminToken) {
            res.clearCookie('edAdminToken');
        }
        return res.status(401).json({ error: 'Invalid token - please login again' });
    }
}

// Authentication Routes
app.post('/api/admin/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Find admin user
        const result = await pool.query(
            'SELECT id, username, password_hash FROM admin_users WHERE username = $1',
            [username]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid username or password' });
        }

        const admin = result.rows[0];

        // Verify password
        const isValidPassword = await bcrypt.compare(password, admin.password_hash);

        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid username or password' });
        }

        // Create simple auth token
        const tokenPayload = {
            adminId: admin.id,
            adminUsername: admin.username,
            loginTime: Date.now()
        };

        const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

        // Set auth token in cookie (not HTTP-only so frontend can read it)
        console.log('Setting cookie for origin:', req.get('origin') || 'no origin header');
        console.log('Request host:', req.get('host'));

        res.cookie('edAdminToken', token, {
            httpOnly: false, // Allow frontend to read the cookie
            secure: false, // Set to true in production with HTTPS
            sameSite: 'lax',
            path: '/', // Ensure cookie is available for all paths
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
            // Don't set domain to allow localhost and file:// origins
        });

        // Also maintain session for backend use
        req.session.adminId = admin.id;
        req.session.adminUsername = admin.username;

        console.log('Auth token created and set in cookie:', token.substring(0, 20) + '...');

        // Update last login
        await pool.query(
            'UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
            [admin.id]
        );

        console.log(`Admin login successful: ${username}`);

        res.json({
            success: true,
            message: 'Login successful',
            token: token, // Include token in response for cross-origin scenarios
            admin: {
                id: admin.id,
                username: admin.username
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/admin/logout', (req, res) => {
    console.log('Logout request received');

    // Clear auth token cookie
    res.clearCookie('edAdminToken', { path: '/' });

    // Also destroy session
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
            return res.status(500).json({ error: 'Failed to logout' });
        }

        res.clearCookie('edadmin.sid'); // Clear session cookie with custom name
        console.log('Logout successful - token cleared');
        res.json({ success: true, message: 'Logout successful' });
    });
});

// New token verification endpoint (supports both cookies and Authorization header)
app.post('/api/admin/verify-token', (req, res) => {
    console.log('=== TOKEN VERIFICATION DEBUG ===');
    console.log('Request origin:', req.get('origin') || 'no origin header');
    console.log('Request host:', req.get('host'));
    console.log('Authorization header:', req.get('authorization') || 'no auth header');
    console.log('Has edAdminToken cookie:', !!req.cookies.edAdminToken);

    // Check for auth token in Authorization header first, then cookies
    let token = null;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        token = req.headers.authorization.substring(7);
        console.log('Token found in Authorization header');
    } else if (req.cookies.edAdminToken) {
        token = req.cookies.edAdminToken;
        console.log('Token found in cookies');
    }

    if (!token) {
        console.log('Token verification - No auth token found');
        console.log('=== TOKEN VERIFICATION END ===');
        return res.status(401).json({
            valid: false,
            message: 'No token provided'
        });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('Token verification - Valid token found for user:', decoded.adminUsername);
        console.log('=== TOKEN VERIFICATION SUCCESS ===');

        return res.json({
            valid: true,
            user: {
                id: decoded.adminId,
                username: decoded.adminUsername
            }
        });
    } catch (error) {
        console.error('Token verification - Token verification failed:', error.message);
        console.log('=== TOKEN VERIFICATION FAILED ===');
        return res.status(401).json({
            valid: false,
            message: 'Invalid token'
        });
    }
});

// Legacy session check endpoint (for backward compatibility)
app.get('/api/admin/check-session', (req, res) => {
    console.log('=== LEGACY SESSION CHECK ===');

    // Check for auth token in cookies
    const token = req.cookies.edAdminToken;

    if (!token) {
        console.log('Legacy session check - No auth token found');
        return res.status(401).json({
            authenticated: false,
            message: 'Please login first'
        });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('Legacy session check - Valid token found for user:', decoded.adminUsername);

        return res.json({
            authenticated: true,
            admin: {
                id: decoded.adminId,
                username: decoded.adminUsername
            }
        });
    } catch (error) {
        console.error('Legacy session check - Token verification failed:', error.message);
        // Clear invalid token
        res.clearCookie('edAdminToken', { path: '/' });
        return res.status(401).json({
            authenticated: false,
            message: 'Invalid token - please login again'
        });
    }
});

// User Management Routes (Protected)
app.get('/api/admin/users', requireAuth, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT id, username, email, is_default_user, created_at, last_login FROM admin_users ORDER BY is_default_user DESC, created_at DESC'
        );

        res.json(result.rows);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/admin/users', requireAuth, async (req, res) => {
    try {
        const { username, password, email } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Check if username already exists
        const existingUser = await pool.query(
            'SELECT id FROM admin_users WHERE username = $1',
            [username]
        );

        if (existingUser.rows.length > 0) {
            return res.status(400).json({ error: 'Username already exists' });
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Insert new user (never as default user)
        const result = await pool.query(
            'INSERT INTO admin_users (username, password_hash, email, is_default_user) VALUES ($1, $2, $3, $4) RETURNING id, username, email, created_at',
            [username, hashedPassword, email || null, false]
        );

        const newUser = result.rows[0];

        console.log(`New admin user created: ${username} by ${req.user.adminUsername}`);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: newUser
        });

    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.delete('/api/admin/users/:id', requireAuth, async (req, res) => {
    try {
        const userId = req.params.id;
        const currentUserId = req.user.adminId;

        // Prevent users from deleting themselves
        if (parseInt(userId) === parseInt(currentUserId)) {
            return res.status(400).json({ error: 'Cannot delete your own account' });
        }

        // Check if user exists and if it's a default user
        const userResult = await pool.query(
            'SELECT username, is_default_user FROM admin_users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const user = userResult.rows[0];

        // Prevent deletion of default user
        if (user.is_default_user) {
            return res.status(400).json({ error: 'Cannot delete the default admin user' });
        }

        // Delete the user
        await pool.query('DELETE FROM admin_users WHERE id = $1', [userId]);

        console.log(`Admin user deleted: ${user.username} by ${req.user.adminUsername}`);

        res.json({
            success: true,
            message: 'User deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Change password endpoint
app.put('/api/admin/change-password', requireAuth, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.adminId;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }

        if (newPassword.length < 6) {
            return res.status(400).json({ error: 'New password must be at least 6 characters long' });
        }

        // Get current user's password hash
        const userResult = await pool.query(
            'SELECT password_hash FROM admin_users WHERE id = $1',
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const currentPasswordHash = userResult.rows[0].password_hash;

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentPasswordHash);

        if (!isCurrentPasswordValid) {
            return res.status(400).json({ error: 'Current password is incorrect' });
        }

        // Hash new password
        const newPasswordHash = await bcrypt.hash(newPassword, 10);

        // Update password
        await pool.query(
            'UPDATE admin_users SET password_hash = $1 WHERE id = $2',
            [newPasswordHash, userId]
        );

        console.log(`Password changed for user: ${req.user.adminUsername}`);

        res.json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// API Routes with enhanced error handling
app.get('/api/jobs', async (req, res) => {
    try {
        // Test connection before querying
        const client = await pool.connect();
        const result = await client.query("SELECT * FROM jobs ORDER BY created_at DESC");
        client.release();
        res.json(result.rows);
    } catch (err) {
        console.error('Error fetching jobs:', err);

        // Provide more specific error messages
        if (err.message.includes('Connection terminated') || err.message.includes('connect')) {
            res.status(503).json({
                error: 'Database connection error. Please try again later.',
                details: 'The service is temporarily unavailable due to database connectivity issues.'
            });
        } else {
            res.status(500).json({
                error: 'Internal server error',
                details: 'An unexpected error occurred while fetching jobs.'
            });
        }
    }
});

app.get('/api/jobs/:id', async (req, res) => {
    try {
        const jobId = req.params.id;
        const result = await pool.query("SELECT * FROM jobs WHERE id = $1", [jobId]);

        if (result.rows.length === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json(result.rows[0]);
    } catch (err) {
        console.error('Error fetching job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Admin API Routes for Job Management (Protected)
app.post('/api/admin/jobs', requireAuth, async (req, res) => {
    try {
        const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

        if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
            return res.status(400).json({ error: 'All fields except image_url are required' });
        }

        const finalImageUrl = image_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

        const result = await pool.query(
            `INSERT INTO jobs (title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING id`,
            [title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl]
        );

        const newJobId = result.rows[0].id;

        res.status(201).json({
            id: newJobId,
            message: 'Job created successfully',
            job: { id: newJobId, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    } catch (err) {
        console.error('Error creating job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.put('/api/admin/jobs/:id', requireAuth, async (req, res) => {
    try {
        const jobId = req.params.id;
        const { title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url } = req.body;

        if (!title || !department || !location || !type || !description || !benefits || !what_youll_do || !what_youll_need || !requirements) {
            return res.status(400).json({ error: 'All fields except image_url are required' });
        }

        const finalImageUrl = image_url || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';

        const result = await pool.query(
            `UPDATE jobs SET title = $1, department = $2, location = $3, type = $4,
             description = $5, benefits = $6, what_youll_do = $7, what_youll_need = $8, requirements = $9, image_url = $10
             WHERE id = $11`,
            [title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, finalImageUrl, jobId]
        );

        if (result.rowCount === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json({
            message: 'Job updated successfully',
            job: { id: jobId, title, department, location, type, description, benefits, what_youll_do, what_youll_need, requirements, image_url: finalImageUrl }
        });
    } catch (err) {
        console.error('Error updating job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.delete('/api/admin/jobs/:id', requireAuth, async (req, res) => {
    try {
        const jobId = req.params.id;

        const result = await pool.query('DELETE FROM jobs WHERE id = $1', [jobId]);

        if (result.rowCount === 0) {
            res.status(404).json({ error: 'Job not found' });
            return;
        }

        res.json({ message: 'Job deleted successfully' });
    } catch (err) {
        console.error('Error deleting job:', err);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Job Application endpoint
app.post('/api/applications', upload.single('cv'), async (req, res) => {
    try {
        const { name, email, linkedinLink, jobId, jobTitle, jobDescription } = req.body;
        const cvFile = req.file;

        // Debug logging
        console.log('Application received:', {
            name,
            email,
            linkedinLink: linkedinLink || 'Not provided',
            jobId,
            jobTitle,
            hasCV: !!cvFile
        });

        // Validate required fields
        if (!name || !email || !jobId || !jobTitle || !cvFile) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Prepare email content
        const emailSubject = `Job Application: ${jobTitle} - ${name}`;
        const emailHtml = `
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
                            New Job Application Received
                        </h2>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Details</h3>
                            <p><strong>Position:</strong> ${jobTitle}</p>
                            <p><strong>Job ID:</strong> ${jobId}</p>
                        </div>

                        <div style="background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Applicant Information</h3>
                            <p><strong>Name:</strong> ${name}</p>
                            <p><strong>Email:</strong> ${email}</p>
                            ${linkedinLink ? `<p><strong>LinkedIn:</strong> <a href="${linkedinLink}" target="_blank">${linkedinLink}</a></p>` : ''}
                        </div>

                        <div style="background-color: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #495057;">Job Description</h3>
                            <p style="white-space: pre-wrap;">${jobDescription}</p>
                        </div>

                        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
                            <p>This application was submitted through the Ed-admin careers website.</p>
                            <p>CV/Resume is attached to this email.</p>
                        </div>
                    </div>
                </body>
            </html>
        `;

        // Email options
        const mailOptions = {
            from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
            to: '<EMAIL>', // Send to recruitment email
            subject: emailSubject,
            html: emailHtml,
            attachments: [
                {
                    filename: `${name}_CV_${jobTitle.replace(/\s+/g, '_')}.${cvFile.originalname.split('.').pop()}`,
                    content: cvFile.buffer,
                    contentType: cvFile.mimetype
                }
            ]
        };

        // Send email
        await transporter.sendMail(mailOptions);

        console.log(`Job application received for ${jobTitle} from ${name} (${email})`);

        res.json({
            success: true,
            message: 'Application submitted successfully'
        });

    } catch (error) {
        console.error('Error processing job application:', error);
        res.status(500).json({
            error: 'Failed to submit application. Please try again later.'
        });
    }
});

// Health check endpoint with database connectivity
app.get('/api/health', async (req, res) => {
    const health = {
        status: 'OK',
        message: 'Ed-admin Jobs API is running',
        timestamp: new Date().toISOString(),
        database: 'unknown'
    };

    try {
        // Test database connection
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        health.database = 'connected';
    } catch (error) {
        health.database = 'disconnected';
        health.status = 'DEGRADED';
        health.error = error.message;
    }

    res.json(health);
});

// Start server
app.listen(PORT, () => {
    console.log(`Ed-admin Jobs API server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
    console.log(`Jobs API: http://localhost:${PORT}/api/jobs`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down server...');
    try {
        await pool.end();
        console.log('Database connection pool closed.');
    } catch (err) {
        console.error('Error closing database pool:', err);
    }
    process.exit(0);
});