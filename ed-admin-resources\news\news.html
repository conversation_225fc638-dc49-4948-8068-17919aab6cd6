<!doctype html>
<html lang="zxx">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Ideas we like to share with you">
        <meta name="keywords" content="Ed-admin news">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="../../assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="../../assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="../../assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="../../assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="../../assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="../../assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="../../assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="../../assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="../../assets/css/responsive.css">

        <title>Ed-admin: Ideas we like to share with you</title>

        <link rel="icon" type="image/png" href="../../assets/img/favicon-Edadmin.ico">

        <!-- Calendly link widget begin -->
        <link href="https://assets.calendly.com/assets/external/widget.css" rel="stylesheet">
        <script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript" async></script>
        <!-- Calendly link widget end -->

        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>

        <style>
            .post-image img {
                height: 210px;
                width: 100%;
                object-fit: cover; /* Optional to maintain aspect ratio */
            }
            /* Trending Tags Styling */
            .tag {
                border: 1px solid black;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s, color 0.3s;
                display: inline-flex;
                align-items: center;
            }
            .tag:hover {
                background-color: #FFA616;
                color: white;
            }
            /* Highlight selected tag */
            .tag.selected {
                background-color: #FFA616;
                color: white;
            }
            /* The remove icon (SVG) – hidden by default, with extra left margin */
            .tag .remove-tag {
                display: none;
                margin-left: 10px;
                cursor: pointer;
            }
            /* Improve card hover effect */
            .news-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            }
            /* Pagination (Accordion) Styles */
            #pagination {
                margin-top: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                font-size: 16px;
                user-select: none;
            }
            #pagination .page-arrow,
            #pagination .page-number {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                transition: background-color 0.3s, color 0.3s;
            }
            #pagination .page-arrow:hover,
            #pagination .page-number:hover {
                background-color: #FFA616;
                color: #fff;
            }
            #pagination .page-number.active {
                background-color: #006EB3;
                color: #fff;
            }
            /* Ellipsis styling */
            #pagination .ellipsis {
                padding: 0 5px;
                cursor: default;
            }
        </style>
    </head>

    <body>
        <!-- Updated: data-menuid from Resources blog -> Resources news -->
        <span data-menuid="Resources news" class="d-none"></span>

        <!-- Start PopUps Area -->
        <div data-include="/popups/demonow"></div>
        <div data-include="/popups/bookdemo"></div>
        <div data-include="/popups/downloadnow"></div>
        <div data-include="/popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="/common/header2"></div>
        <!-- End Header Area -->

        <div style="width: 100%; text-align: center;">
            <h1 style="
                display: inline-block;
                background: linear-gradient(102.33deg, #006EB3 -17.27%, #FF9300 106.25%);
                background-size: 100%;
                background-position: center;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                color: transparent;">
              Ed-admin <br>News
            </h1>
        </div>

        <!-- Dropdown Menu for Tags -->
        <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
            <div style="width: 300px; margin-top: 20px;">
                <select id="tagDropdown"
                    onchange="handleDropdownChange(this.value)"
                    style="
                        width: 100%;
                        padding: 10px 40px 10px 12px;
                        font-size: 16px;
                        color: #555;
                        border: 1px solid #ccc;
                        border-radius: 8px;
                        background-color: #fff;
                        -webkit-appearance: none;
                        -moz-appearance: none;
                        appearance: none;
                        background: url('../../assets/arrow-down.png') no-repeat right 12px center / 10px 10px, #fff;
                    ">
                    <option value="All">Select Subject</option>
                    <!-- Additional options inserted here -->
                </select>
            </div>
        </div>

        <!-- Recently Added Section -->
        <div id="recently-added-section" style="width: 100%; text-align: center; margin-top: 40px;">
            <section style="width: 100%; text-align: center; margin-top: 40px;">
                <h2 style="margin-bottom: 80px;">Recently Added</h2>
                <div id="recently-added-1" style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap;">
                    <!-- Dynamic news cards for first 3 news will be injected here -->
                </div>
            </section>
        </div>
        <!-- End Recently Added Section -->

        <!-- Trending Tags Section -->
        <section style="width: 100%; text-align: center; margin-top: 60px; display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <h2 style="margin-bottom: 25px;">Trending Tags</h2>
            <div id="trending-tags" style="width: 70%;">
                <!-- Fixed trending tags will be injected here -->
            </div>
        </section>

        <!-- All News Section (was All Blogs Section) -->
        <section style="width: 100%; text-align: center; margin-top: 20px; display: flex; justify-content: center; align-items: center; flex-direction: column; margin-bottom: 40px;">
            <h2 style="margin-bottom: 50px;">All News</h2>
            <div id="recently-added-2" style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap; width: 60%;">
                <!-- Dynamic news cards for "All News" will be injected here -->
            </div>
            <!-- Pagination/Accordion Controls -->
            <div id="pagination"></div>
        </section>

        <!-- Start Free Trial Area -->
        <section class="free-trial-area ptb-100 bg-f4f5fe">
            <div class="container">
                <div class="free-trial-content video-box">
                    <h2>Like to see Ed-admin in action? Ask for a demo!</h2>
                    <a href="#" class="default-btn" style="padding-left: 25px;" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/ed-admin/free-demo'});return false;">
                        Free Demo <span></span>
                    </a>
                </div>
            </div>
            <div class="shape10"><img src="../../assets/img/shape/10.png" alt="image"></div>
            <div class="shape11"><img src="../../assets/img/shape/7.png" alt="image"></div>
            <div class="shape12"><img src="../../assets/img/shape/11.png" alt="image"></div>
            <div class="shape13"><img src="../../assets/img/shape/12.png" alt="image"></div>
        </section>
        <!-- End Free Trial Area -->

        <!-- Start Footer Area -->
        <div data-include="/common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="../../assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="../../assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="../../assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="../../assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="../../assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="../../assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="../../assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="../../assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="../../assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="../../assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="../../assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="../../assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="../../assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="../../assets/js/particles.min.js"></script>
        <script src="../../assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="../../assets/js/main.js"></script>

        <!-- Custom Script to Fetch and Display News and Tags with Pagination -->
        <script>
            // Global variables
            let allNews = [];
            let otherNews = []; // everything after the first 3
            let currentPage = 1; // used for the "All News" pagination
            const newsPerPage = 9; // show 9 per page in "All News"

            // Function to fetch news from WordPress API
            async function fetchNews() {
                const recentlyAdded1Container = document.getElementById('recently-added-1');
                const recentlyAdded2Container = document.getElementById('recently-added-2');
                const trendingTagsContainer = document.getElementById('trending-tags');
                const recentlyAddedSection = document.getElementById('recently-added-section');
                
                if (!recentlyAdded1Container || !recentlyAdded2Container || !trendingTagsContainer || !recentlyAddedSection) {
                    console.error('One or more containers not found');
                    return;
                }

                // Show temporary "loading" placeholders
                recentlyAdded1Container.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading news...
                    </div>
                `;
                recentlyAdded2Container.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading news...
                    </div>
                `;
                trendingTagsContainer.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        Loading tags...
                    </div>
                `;

                try {
                    // Increased per_page from 20 to 100 to fetch more posts
                    const response = await fetch(
                        'https://public-api.wordpress.com/wp/v2/sites/edadmin7.wordpress.com/posts?_embed=true&per_page=100'
                    );
                    if (!response.ok) throw new Error('Failed to fetch news');
                    const newsItems = await response.json();

                    // Filter posts so that only those in the category "News" are stored
                    allNews = newsItems.filter(news => {
                        if (news._embedded && news._embedded['wp:term']) {
                            return news._embedded['wp:term'].some(termGroup =>
                                termGroup.some(term => term.taxonomy === 'category' && term.name === 'News')
                            );
                        }
                        return false;
                    });

                    // Clear "loading" messages
                    recentlyAdded1Container.innerHTML = '';
                    recentlyAdded2Container.innerHTML = '';
                    trendingTagsContainer.innerHTML = '';

                    // Display first 3 news in the "Recently Added" section
                    const firstThreeNews = allNews.slice(0, 3);
                    displayNews(firstThreeNews, recentlyAdded1Container);

                    // The remaining news will be paginated
                    otherNews = allNews.slice(3);

                    // Display the first page (9 news) in "All News"
                    displayPaginatedNews(1);

                    // --- Fixed Trending Tags Section ---
                    const fixedTags = [
                        "Education Technology",
                        "School Management Software",
                        "Academic Management",
                        "Education Innovation",
                        "International Education Solutions",
                        "School Operations",
                        "Ed-admin Multi-Portal App",
                        "Student Support Services",
                        "Digital Management",
                        "Digital Learning"
                    ];
                    displayTags(fixedTags, trendingTagsContainer);

                    // --- Populate the dropdown with all unique tags from WordPress ---
                    const allTags = extractTags(allNews);
                    const uniqueTags = getUniqueTags(allTags);
                    populateTagDropdown(uniqueTags);

                } catch (error) {
                    console.error('Error fetching news:', error);
                    recentlyAdded1Container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load news. Please try again later.
                        </div>
                    `;
                    recentlyAdded2Container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load news. Please try again later.
                        </div>
                    `;
                    trendingTagsContainer.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Failed to load tags. Please try again later.
                        </div>
                    `;
                }
            }

            // Show the "All News" for a given page
            function displayPaginatedNews(page) {
                currentPage = page;

                const recentlyAdded2Container = document.getElementById('recently-added-2');
                if (!recentlyAdded2Container) return;

                // Slice the correct segment of otherNews
                const startIndex = (page - 1) * newsPerPage;
                const endIndex = startIndex + newsPerPage;
                const newsToShow = otherNews.slice(startIndex, endIndex);

                // Display these news items
                displayNews(newsToShow, recentlyAdded2Container);

                // Also update the pagination UI
                displayPagination();
            }

            // Build a pagination (accordion) UI
            function displayPagination() {
                const paginationContainer = document.getElementById('pagination');
                if (!paginationContainer) return;

                const totalPosts = otherNews.length;
                const totalPages = Math.ceil(totalPosts / newsPerPage);

                let paginationHTML = '';

                // Left arrow
                if (currentPage > 1) {
                    paginationHTML += `<span class="page-arrow" onclick="changePage(${currentPage - 1})">&larr;</span>`;
                } else {
                    paginationHTML += `<span class="page-arrow" style="opacity:0.5;cursor:not-allowed;">&larr;</span>`;
                }

                // Decide how to render page numbers
                const maxVisible = 5;
                if (totalPages <= maxVisible) {
                    for (let i = 1; i <= totalPages; i++) {
                        paginationHTML += getPageNumberHTML(i);
                    }
                } else {
                    if (currentPage <= 3) {
                        for (let i = 1; i <= 3; i++) {
                            paginationHTML += getPageNumberHTML(i);
                        }
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(totalPages - 1);
                        paginationHTML += getPageNumberHTML(totalPages);
                    } else if (currentPage >= totalPages - 2) {
                        paginationHTML += getPageNumberHTML(1);
                        paginationHTML += getPageNumberHTML(2);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        for (let i = totalPages - 2; i <= totalPages; i++) {
                            paginationHTML += getPageNumberHTML(i);
                        }
                    } else {
                        paginationHTML += getPageNumberHTML(1);
                        paginationHTML += getPageNumberHTML(2);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(currentPage);
                        paginationHTML += `<span class="ellipsis">...</span>`;
                        paginationHTML += getPageNumberHTML(totalPages - 1);
                        paginationHTML += getPageNumberHTML(totalPages);
                    }
                }

                // Right arrow
                if (currentPage < totalPages) {
                    paginationHTML += `<span class="page-arrow" onclick="changePage(${currentPage + 1})">&rarr;</span>`;
                } else {
                    paginationHTML += `<span class="page-arrow" style="opacity:0.5;cursor:not-allowed;">&rarr;</span>`;
                }

                paginationContainer.innerHTML = paginationHTML;
            }

            function getPageNumberHTML(pageNumber) {
                const activeClass = (pageNumber === currentPage) ? 'active' : '';
                return `
                    <span class="page-number ${activeClass}" onclick="changePage(${pageNumber})">
                        ${pageNumber}
                    </span>
                `;
            }

            function changePage(newPage) {
                const totalPages = Math.ceil(otherNews.length / newsPerPage);
                if (newPage < 1 || newPage > totalPages) return;
                displayPaginatedNews(newPage);
            }

            // Function to display an array of news objects in a given container with alternating title background colors
            function displayNews(newsItems, container) {
                if (!container) {
                    console.error('News container not found');
                    return;
                }
                try {
                    if (newsItems.length === 0) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                No news found.
                            </div>
                        `;
                        return;
                    }

                    // Map over news items. The inner title container will alternate its background color:
                    const newsHTML = newsItems.map((news, index) => {
                        // Determine the alternating background color for the title container.
                        const titleBgColor = (index % 2 === 0) ? "#FFA616" : "#3F51B5";
                        return `
                        <div class="news-card" 
                             style="width: 300px; height: auto; background-color: #EEEDED; border-radius: 30px; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 10px; cursor: pointer; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: transform 0.3s ease; margin-top: 50px;"
                             onclick="navigateToNews('${news.id}', '${getFeaturedImageUrl(news)}')">
                            <img src="${getFeaturedImageUrl(news)}" 
                                 style="width: 90%; height: 180px; border-radius: 30px; margin-top: -70px;" 
                                 alt="${news.title.rendered || 'News item'}" 
                                 onerror="this.src='../../assets/img/default-news-image.jpg';" />
                            <!-- The inner title container alternates background colors -->
                            <div style="width: 90%; background-color: ${titleBgColor}; border-radius: 15px; padding: 10px;">
                                <p style="color: white; font-weight: bold; margin: 0;">
                                    ${news.title.rendered || 'Untitled Post'}
                                </p>
                            </div>
                            <p style="text-align: left; width: 90%; color: #555; margin-top: 10px; margin-bottom: 10px">
                                ${formatDate(news.date)}
                            </p>
                        </div>
                        `;
                    }).join('');

                    container.innerHTML = newsHTML;
                } catch (error) {
                    console.error('Error displaying news:', error);
                    container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Error displaying news. Please try again later.
                        </div>
                    `;
                }
            }

            // Helper function to format date
            function formatDate(dateString) {
                try {
                    const date = new Date(dateString);
                    const options = { year: 'numeric', month: 'long', day: 'numeric' };
                    return date.toLocaleDateString('en-US', options);
                } catch (error) {
                    console.error('Error formatting date:', error);
                    return dateString;
                }
            }

            // Helper function to get a news item’s featured image
            function getFeaturedImageUrl(news) {
                try {
                    if (news._embedded && news._embedded['wp:featuredmedia'] && news._embedded['wp:featuredmedia'][0]) {
                        const media = news._embedded['wp:featuredmedia'][0];
                        if (media.media_details && media.media_details.sizes && media.media_details.sizes.medium) {
                            return media.media_details.sizes.medium.source_url;
                        }
                        if (media.source_url) {
                            return media.source_url;
                        }
                    }
                    if (news.jetpack_featured_media_url) {
                        return news.jetpack_featured_media_url;
                    }
                    return '../../assets/img/default-news-image.jpg';
                } catch (error) {
                    console.error('Error getting featured image:', error);
                    return '../../assets/img/default-news-image.jpg';
                }
            }

            // Extract all tags from the news array
            function extractTags(newsArray) {
                const tags = [];
                newsArray.forEach(news => {
                    if (news._embedded && news._embedded['wp:term']) {
                        news._embedded['wp:term'].forEach(termGroup => {
                            termGroup.forEach(term => {
                                if (term.taxonomy === 'post_tag') {
                                    tags.push(term.name);
                                }
                            });
                        });
                    }
                });
                return tags;
            }

            // Helper function to return a list of unique tags
            function getUniqueTags(tagsArray) {
                const uniqueTagsSet = new Set(tagsArray.map(tag => tag.trim()).filter(tag => tag));
                return Array.from(uniqueTagsSet);
            }

            // Display the tag buttons (limited to the first 10 tags) with the SVG remove icon
            function displayTags(tags, container) {
                if (!container) {
                    console.error('Tags container not found');
                    return;
                }
                try {
                    const limitedTags = tags.slice(0, 10);
                    // "All" option remains without a remove icon
                    const allTagHTML = `
                        <p class="tag" onclick="toggleTrendingTag(this, 'All')">
                            <span class="tag-text">All</span>
                        </p>
                    `;
                    const tagsHTML = limitedTags.map(tag => `
                        <p class="tag" onclick="toggleTrendingTag(this, '${tag}')">
                            <span class="tag-text">${tag}</span>
                            <span class="remove-tag">
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 50 50">
                                    <path fill="#ffffff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                                </svg>
                            </span>
                        </p>
                    `).join('');
                    container.innerHTML = allTagHTML + tagsHTML;
                } catch (error) {
                    console.error('Error displaying tags:', error);
                    container.innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            Error displaying tags. Please try again later.
                        </div>
                    `;
                }
            }

            // Populate the dropdown menu with all unique tags (without limiting)
            function populateTagDropdown(tags) {
                const dropdown = document.getElementById("tagDropdown");
                if (!dropdown) return;
                dropdown.innerHTML = '<option value="All">Select Subject</option>';
                tags.forEach(tag => {
                    dropdown.innerHTML += `<option value="${tag}">${tag}</option>`;
                });
            }

            // Function to perform filtering by tag (for both dropdown and trending tags)
            function doFilterByTag(tag) {
                const recentlyAddedSection = document.getElementById('recently-added-section');
                const recentlyAdded1Container = document.getElementById('recently-added-1');
                const recentlyAdded2Container = document.getElementById('recently-added-2');

                // If "All" is selected, show the default view
                if (tag === 'All') {
                    recentlyAddedSection.style.display = 'block';
                    const firstThree = allNews.slice(0, 3);
                    displayNews(firstThree, recentlyAdded1Container);
                    otherNews = allNews.slice(3);
                    currentPage = 1;
                    displayPaginatedNews(currentPage);
                    return;
                }

                // Hide the Recently Added section when filtering
                recentlyAddedSection.style.display = 'none';

                const filteredNews = allNews.filter(news => {
                    if (news._embedded && news._embedded['wp:term']) {
                        for (const termGroup of news._embedded['wp:term']) {
                            for (const term of termGroup) {
                                if (term.taxonomy === 'post_tag' && term.name === tag) {
                                    return true;
                                }
                            }
                        }
                    }
                    return false;
                });

                displayNews(filteredNews, recentlyAdded2Container);
                document.getElementById('pagination').innerHTML = '';
            }

            // Handle dropdown change event by filtering news based on the selected tag
            function handleDropdownChange(selectedTag) {
                clearTrendingTagSelection(); // Clear any trending tag selection when using dropdown
                doFilterByTag(selectedTag);
            }

            // Toggle trending tag selection
            function toggleTrendingTag(element, tag) {
                // If the clicked tag is already selected, clear the selection and show "All"
                if (element.classList.contains('selected')) {
                    clearTrendingTagSelection();
                    doFilterByTag('All');
                } else {
                    clearTrendingTagSelection();
                    element.classList.add('selected');
                    // Show the SVG remove icon by unhiding the remove-tag span
                    const removeSpan = element.querySelector('.remove-tag');
                    if (removeSpan) {
                        removeSpan.style.display = 'inline';
                    }
                    doFilterByTag(tag);
                }
            }

            // Clear any trending tag selection
            function clearTrendingTagSelection() {
                const trendingTagsContainer = document.getElementById('trending-tags');
                if (trendingTagsContainer) {
                    const tagElements = trendingTagsContainer.getElementsByClassName('tag');
                    Array.from(tagElements).forEach(el => {
                        el.classList.remove('selected');
                        const removeSpan = el.querySelector('.remove-tag');
                        if (removeSpan) {
                            removeSpan.style.display = 'none';
                        }
                    });
                }
            }

            // Navigate to read.html
            function navigateToNews(newsId, featuredImageUrl) {
                try {
                    localStorage.setItem('selectedNewsId', newsId);
                    localStorage.setItem('selectedNewsImage', featuredImageUrl);
                    window.location.href = 'read.html';
                } catch (error) {
                    console.error('Error navigating to news:', error);
                    alert('Error opening news item. Please try again.');
                }
            }

            // On DOM ready, fetch the news
            document.addEventListener('DOMContentLoaded', function() {
                fetchNews();
            });
        </script>
    </body>
</html>
