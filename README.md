# Ed-admin Website

This project contains the Ed-admin website with a separate Node.js backend for managing job listings and applications.

## Project Structure

```
├── backend/              # Backend API (separate deployment)
│   ├── index.js         # Main backend server
│   ├── package.json     # Backend dependencies
│   ├── .env.example     # Environment variables template
│   └── README.md        # Backend documentation
├── assets/              # Frontend assets
├── common/              # Shared HTML components
├── admin/               # Admin panel for job management
└── *.html              # Website pages
```

## Frontend Features

- **Responsive Design**: Mobile-friendly across all devices
- **Job Listings**: Dynamic job loading from backend API
- **Job Applications**: File upload with email notifications
- **Admin Panel**: Job management interface
- **Modern UI**: Professional design with smooth animations

## Backend Features

- **RESTful API**: Express.js server with PostgreSQL database
- **Job Management**: CRUD operations for job listings
- **File Upload**: CV/resume handling with email notifications
- **Email Integration**: Nodemailer for application notifications
- **CORS Enabled**: Cross-origin requests supported

## Quick Start

### Backend Setup
1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your PostgreSQL and email settings
   ```
   **Important:** Configure your `DATABASE_URL` and email credentials in the `.env` file.

4. Start the backend server:
   ```bash
   npm start
   ```

The frontend now uses the official API endpoint: `https://job-post-backend-zoku.onrender.com`

### API Endpoints

- `GET /api/health` - Health check endpoint
- `GET /api/jobs` - Get all job listings
- `GET /api/jobs/:id` - Get specific job by ID
- `POST /api/applications` - Submit job application
- `POST /api/admin/jobs` - Create new job (admin)
- `PUT /api/admin/jobs/:id` - Update job (admin)
- `DELETE /api/admin/jobs/:id` - Delete job (admin)

### Database

The backend uses PostgreSQL database with the following schema:

```sql
CREATE TABLE jobs (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    department TEXT NOT NULL,
    location TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT NOT NULL,
    benefits TEXT NOT NULL,
    what_youll_do TEXT NOT NULL,
    what_youll_need TEXT NOT NULL,
    requirements TEXT NOT NULL,
    image_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Demo Data

The system includes 6 demo job positions:
1. Senior Software Engineer (Engineering)
2. Product Manager (Product)
3. UX/UI Designer (Design)
4. Customer Success Manager (Customer Success)
5. DevOps Engineer (Engineering)
6. Marketing Specialist (Marketing)

Each job includes:
- Professional stock images from Unsplash
- Realistic job descriptions and requirements
- Department categorization
- Location information (Remote/Hybrid options)

## Frontend Integration

The `join-our-team.html` page includes:
- Jobs section with loading states
- Error handling for API failures
- Responsive job cards with hover effects
- Dynamic content loading via JavaScript

### Key Features:
- **Loading State**: Shows spinner while fetching jobs
- **Error Handling**: Displays user-friendly error messages
- **Responsive Design**: Works on all device sizes
- **Interactive Cards**: Hover effects and click handlers
- **Professional Styling**: Consistent with Ed-admin brand

## Usage

### Using Official API (Recommended)

1. Open `join-our-team.html` in a web browser

2. The page will automatically load job listings from the official API: `https://job-post-backend-zoku.onrender.com`

3. Test the API connection using `test-api-connection.html`

### Local Development (Optional)

1. Start the local backend server:
   ```bash
   npm start
   ```

2. Update API URLs in frontend files to point to `http://localhost:3001`

3. Open `join-our-team.html` in a web browser

## Customization

### Adding New Jobs
Use the admin panel at `admin/jobs.html` or modify the `demoJobs` array in `backend/index.js`.

### API Configuration
The frontend is now configured to use the official API endpoint: `https://job-post-backend-zoku.onrender.com`

To use a different backend, update the API URLs in these files:
- `join-our-team.html` (line 684)
- `Job-apply.html` (lines 388-389)
- `job-details.html` (line 341)
- `admin/jobs.html` (line 526)

### Styling
Job card styles are defined inline in the JavaScript. Modify the `createJobCard` function to change the appearance.

## Development

### File Structure
```
├── backend/
│   ├── index.js          # Main backend server
│   └── jobs.db           # SQLite database (auto-created)
├── join-our-team.html    # Main page with job listings
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

### Technologies Used
- **Backend**: Node.js, Express.js, PostgreSQL, CORS
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Bootstrap
- **Images**: Unsplash stock photos
- **Database**: PostgreSQL

## Deployment

### Frontend Deployment
Deploy the frontend to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- AWS S3
- Any web server

### Backend Deployment
Deploy the backend independently to:
- Heroku
- Railway
- Render
- DigitalOcean App Platform
- AWS Elastic Beanstalk
- Google Cloud Run

### Environment Variables
Configure these for the backend:
- `PORT` - Server port (default: 3001)
- `NODE_ENV` - Environment (development/production)
- `DATABASE_URL` - PostgreSQL connection string
- `EMAIL_USER` - Gmail address for notifications
- `EMAIL_PASS` - Gmail app password
- `EMAIL_FROM` - From email address (optional)

## Troubleshooting

### Backend Connection Issues
- Ensure backend is running on the correct port
- Check API URLs in frontend JavaScript files
- Verify CORS is enabled (included by default)

### Email Issues
- Use Gmail app passwords, not regular passwords
- Ensure 2FA is enabled on Gmail account
- Check environment variables are set correctly

### Database Issues
Ensure PostgreSQL database is accessible and credentials are correct. Check the DATABASE_URL environment variable.
