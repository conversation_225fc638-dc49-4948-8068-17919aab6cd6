# Ed-admin Backend API

This is the backend API for the Ed-admin job listings and application system.

## Features

- Job listings management (CRUD operations)
- Job application submission with file upload
- Email notifications for job applications
- PostgreSQL database for data storage
- CORS enabled for frontend integration

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Configure your environment variables in `.env`:
   - Set `DATABASE_URL` with your PostgreSQL connection string
   - Set `EMAIL_USER`, `EMAIL_PASS`, and `EMAIL_FROM` for job application notifications
   - Adjust `PORT` if needed (default: 3001)
   - Set `NODE_ENV` (development/production)

**Important:** The `.env` file contains sensitive credentials and is excluded from version control via `.gitignore`.

4. Start the server:
```bash
npm start
```

## API Endpoints

### Jobs
- `GET /api/jobs` - Get all jobs
- `GET /api/jobs/:id` - Get specific job
- `POST /api/admin/jobs` - Create new job (admin)
- `PUT /api/admin/jobs/:id` - Update job (admin)
- `DELETE /api/admin/jobs/:id` - Delete job (admin)

### Applications
- `POST /api/applications` - Submit job application with CV upload

### Health Check
- `GET /api/health` - Server health check

## Deployment

This backend can be deployed independently to any Node.js hosting platform:
- Heroku
- Railway
- Render
- DigitalOcean App Platform
- AWS Elastic Beanstalk
- Google Cloud Run

## Environment Variables

- `PORT` - Server port (default: 3001)
- `NODE_ENV` - Environment (development/production)
- `DATABASE_URL` - PostgreSQL connection string
- `EMAIL_USER` - Gmail address for sending notifications
- `EMAIL_PASS` - Gmail app password
- `EMAIL_FROM` - From email address (optional)

## Database

The application uses PostgreSQL for data storage. The database connection is configured via the `DATABASE_URL` environment variable. The jobs table is created automatically when the server starts, and demo data is inserted if the table is empty.

### Database Schema

```sql
CREATE TABLE jobs (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    department TEXT NOT NULL,
    location TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT NOT NULL,
    benefits TEXT NOT NULL,
    what_youll_do TEXT NOT NULL,
    what_youll_need TEXT NOT NULL,
    requirements TEXT NOT NULL,
    image_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## File Uploads

Job applications support CV/resume uploads in PDF, DOC, and DOCX formats with a 5MB size limit.
