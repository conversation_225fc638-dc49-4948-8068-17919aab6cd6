<!DOCTYPE html>
<html><head><meta name="GCD" content="YTk3ODQ3ZWZhN2I4NzZmMzBkNTEwYjJl6fb746e2eeef834885505564ab736fa8"/>
  <meta charset="utf-8">
  <meta name="generator" content="Google Web Designer 14.0.2.0928">
  <meta name="template" content="Banner 3.0.0">
  <meta name="environment" content="gwd-dv360">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>gwd-page{display:block}.gwd-inactive{visibility:hidden}</style>
  <style>.gwd-pagedeck{position:relative;display:block}.gwd-pagedeck>.gwd-page.transparent{opacity:0}.gwd-pagedeck>.gwd-page{position:absolute;top:0;left:0;-webkit-transition-property:-webkit-transform,opacity;-moz-transition-property:transform,opacity;transition-property:transform,opacity}.gwd-pagedeck>.gwd-page.linear{transition-timing-function:linear}.gwd-pagedeck>.gwd-page.ease-in{transition-timing-function:ease-in}.gwd-pagedeck>.gwd-page.ease-out{transition-timing-function:ease-out}.gwd-pagedeck>.gwd-page.ease{transition-timing-function:ease}.gwd-pagedeck>.gwd-page.ease-in-out{transition-timing-function:ease-in-out}.linear *,.ease-in *,.ease-out *,.ease *,.ease-in-out *{-webkit-transform:translateZ(0);transform:translateZ(0)}</style>
  <style>gwd-page.fs{border:none}</style>
  <style>gwd-image.scaled-proportionally>div.intermediate-element>img{background-repeat:no-repeat;background-position:center}gwd-image{display:inline-block}gwd-image>div.intermediate-element{width:100%;height:100%}gwd-image>div.intermediate-element>img{display:block;width:100%;height:100%}</style>
  <style type="text/css" id="gwd-lightbox-style">.gwd-lightbox{overflow:hidden}</style>
  <style type="text/css" id="gwd-text-style">p{margin:0px}h1{margin:0px}h2{margin:0px}h3{margin:0px}</style>
  <style type="text/css">html,body{width:100%;height:100%;margin:0px}.gwd-page-container{position:relative;width:100%;height:100%}.gwd-page-content{background-color:transparent;transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);-webkit-transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);-moz-transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);perspective:1400px;-webkit-perspective:1400px;-moz-perspective:1400px;transform-style:preserve-3d;-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;position:absolute}.gwd-page-wrapper{background-color:#fff;position:absolute;transform:translateZ(0);-webkit-transform:translateZ(0);-moz-transform:translateZ(0)}.gwd-page-size{width:450px;height:350px}.gwd-image-15js{position:absolute;transform-origin:50% 50% 0px;-webkit-transform-origin:50% 50% 0px;-moz-transform-origin:50% 50% 0px;width:317px;height:608px;left:71px;top:-8px}.gwd-rect-j3wj{position:absolute;width:185px;height:85px;box-sizing:border-box;border-style:solid;border-color:#000;background-image:none;background-color:#4a4a4a;border-radius:13px;border-width:0px;top:102px;transform-origin:50% 50% 0px;-webkit-transform-origin:50% 50% 0px;-moz-transform-origin:50% 50% 0px;left:123px;transform-style:preserve-3d;-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-webkit-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-moz-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0}.gwd-image-12i5{position:absolute;transform-origin:50% 50% 0px;-webkit-transform-origin:50% 50% 0px;-moz-transform-origin:50% 50% 0px;left:54px;top:211px;width:117px;height:22px;opacity:0}.gwd-span-1j6a{position:absolute;font-family:Poppins;font-weight:500;color:#fff;font-size:9px;left:148px;top:128px}.gwd-span-wgc0{font-weight:300;transform-origin:50% 50% 0px;-webkit-transform-origin:50% 50% 0px;-moz-transform-origin:50% 50% 0px;text-align:left;left:60px;height:76px;width:334px;font-size:15px;top:270px;opacity:0}.gwd-span-1vcn{height:22px;left:60px;width:344px;transform-origin:50% 50% 0px;-webkit-transform-origin:50% 50% 0px;-moz-transform-origin:50% 50% 0px;font-size:19px;top:236px;opacity:0}.gwd-p-1pyg{position:absolute;font-family:Poppins;color:#fff;left:343px;font-size:11px;top:210px;opacity:0}@keyframes gwd-gen-iey7gwdanimation_gwd-keyframes{0%{transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-webkit-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-moz-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}21.7391%{transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);-webkit-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);-moz-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}30.4348%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}56.5217%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-iey7gwdanimation_gwd-keyframes{0%{-webkit-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;-webkit-animation-timing-function:linear}21.7391%{-webkit-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;-webkit-animation-timing-function:linear}30.4348%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}56.5217%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}100%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-iey7gwdanimation_gwd-keyframes{0%{-moz-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;-moz-animation-timing-function:linear}21.7391%{-moz-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;-moz-animation-timing-function:linear}30.4348%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}56.5217%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}100%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .gwd-gen-iey7gwdanimation{animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes;-webkit-animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes;-moz-animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes}@keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}75%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes{0%{opacity:0;-webkit-animation-timing-function:linear}75%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes{0%{opacity:0;-moz-animation-timing-function:linear}75%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .gwd-gen-fj8dgwdanimation{animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes;-webkit-animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes;-moz-animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes}@keyframes gwd-gen-7n0igwdanimation_gwd-keyframes{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}75%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-7n0igwdanimation_gwd-keyframes{0%{opacity:0;-webkit-animation-timing-function:linear}75%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-7n0igwdanimation_gwd-keyframes{0%{opacity:0;-moz-animation-timing-function:linear}75%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .gwd-gen-7n0igwdanimation{animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes;-webkit-animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes;-moz-animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes}@keyframes gwd-gen-14jwgwdanimation_gwd-keyframes{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}77.7778%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-14jwgwdanimation_gwd-keyframes{0%{opacity:0;-webkit-animation-timing-function:linear}77.7778%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-14jwgwdanimation_gwd-keyframes{0%{opacity:0;-moz-animation-timing-function:linear}77.7778%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .gwd-gen-14jwgwdanimation{animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes;-webkit-animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes;-moz-animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes}@keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}77.7778%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes{0%{opacity:0;-webkit-animation-timing-function:linear}77.7778%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes{0%{opacity:0;-moz-animation-timing-function:linear}77.7778%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .gwd-gen-1cm0gwdanimation{animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes;-webkit-animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes;-moz-animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes}</style><style type="text/css" data-gwd-secondary-animations="">#page1.start .gwd-gen-iey7gwdanimation{animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes_start;-webkit-animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes_start;-moz-animation:4.6s linear 0s 1 normal forwards gwd-gen-iey7gwdanimation_gwd-keyframes_start}@keyframes gwd-gen-iey7gwdanimation_gwd-keyframes_start{0%{transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-webkit-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);-moz-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}21.7391%{transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);-webkit-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);-moz-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}30.4348%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}56.5217%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-iey7gwdanimation_gwd-keyframes_start{0%{-webkit-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;-webkit-animation-timing-function:linear}21.7391%{-webkit-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;-webkit-animation-timing-function:linear}30.4348%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}56.5217%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}100%{-webkit-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-iey7gwdanimation_gwd-keyframes_start{0%{-moz-transform:translate3d(10px, 114px, 0) scale3d(1.94661, 1.57588, 1);opacity:0;-moz-animation-timing-function:linear}21.7391%{-moz-transform:translate3d(10px, 114px, 0) scale3d(1, 1, 1);opacity:0;-moz-animation-timing-function:linear}30.4348%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}56.5217%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}100%{-moz-transform:translate3d(10px, 123px, 0) scale3d(1.9648, 1.70333, 1);opacity:0.7;-moz-animation-timing-function:linear}}#page1.start .gwd-gen-fj8dgwdanimation{animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes_start;-webkit-animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes_start;-moz-animation:1.6s linear 0s 1 normal forwards gwd-gen-fj8dgwdanimation_gwd-keyframes_start}@keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes_start{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}75%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes_start{0%{opacity:0;-webkit-animation-timing-function:linear}75%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-fj8dgwdanimation_gwd-keyframes_start{0%{opacity:0;-moz-animation-timing-function:linear}75%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.start .gwd-gen-7n0igwdanimation{animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes_start;-webkit-animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes_start;-moz-animation:1.6s linear 0s 1 normal forwards gwd-gen-7n0igwdanimation_gwd-keyframes_start}@keyframes gwd-gen-7n0igwdanimation_gwd-keyframes_start{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}75%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-7n0igwdanimation_gwd-keyframes_start{0%{opacity:0;-webkit-animation-timing-function:linear}75%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-7n0igwdanimation_gwd-keyframes_start{0%{opacity:0;-moz-animation-timing-function:linear}75%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.start .gwd-gen-14jwgwdanimation{animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes_start;-webkit-animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes_start;-moz-animation:1.8s linear 0s 1 normal forwards gwd-gen-14jwgwdanimation_gwd-keyframes_start}@keyframes gwd-gen-14jwgwdanimation_gwd-keyframes_start{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}77.7778%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-14jwgwdanimation_gwd-keyframes_start{0%{opacity:0;-webkit-animation-timing-function:linear}77.7778%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-14jwgwdanimation_gwd-keyframes_start{0%{opacity:0;-moz-animation-timing-function:linear}77.7778%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.start .gwd-gen-1cm0gwdanimation{animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes_start;-webkit-animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes_start;-moz-animation:1.8s linear 0s 1 normal forwards gwd-gen-1cm0gwdanimation_gwd-keyframes_start}@keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes_start{0%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}77.7778%{opacity:0;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}100%{opacity:1;animation-timing-function:linear;-webkit-animation-timing-function:linear;-moz-animation-timing-function:linear}}@-webkit-keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes_start{0%{opacity:0;-webkit-animation-timing-function:linear}77.7778%{opacity:0;-webkit-animation-timing-function:linear}100%{opacity:1;-webkit-animation-timing-function:linear}}@-moz-keyframes gwd-gen-1cm0gwdanimation_gwd-keyframes_start{0%{opacity:0;-moz-animation-timing-function:linear}77.7778%{opacity:0;-moz-animation-timing-function:linear}100%{opacity:1;-moz-animation-timing-function:linear}}#page1.gwd-play-animation .event-1-animation{animation:4.6s linear 0s 1 normal forwards gwd-empty-animation;-webkit-animation:4.6s linear 0s 1 normal forwards gwd-empty-animation;-moz-animation:4.6s linear 0s 1 normal forwards gwd-empty-animation}@keyframes gwd-empty-animation{0%{opacity:0.001}100%{opacity:0}}@-webkit-keyframes gwd-empty-animation{0%{opacity:0.001}100%{opacity:0}}@-moz-keyframes gwd-empty-animation{0%{opacity:0.001}100%{opacity:0}}#page1.start .event-1-animation{animation:4.6s linear 0s 1 normal forwards gwd-empty-animation_start;-webkit-animation:4.6s linear 0s 1 normal forwards gwd-empty-animation_start;-moz-animation:4.6s linear 0s 1 normal forwards gwd-empty-animation_start}@keyframes gwd-empty-animation_start{0%{opacity:0.001}100%{opacity:0}}@-webkit-keyframes gwd-empty-animation_start{0%{opacity:0.001}100%{opacity:0}}@-moz-keyframes gwd-empty-animation_start{0%{opacity:0.001}100%{opacity:0}}</style>
  <link href="https://fonts.googleapis.com/css?family=Roboto:regular|Open+Sans:regular|Noto+Sans+JP:regular|Montserrat:regular|Poppins:500,300,regular" rel="stylesheet" type="text/css">
  <script data-source="googbase_min.js" data-version="4" data-exports-type="googbase">(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
(window.goog=window.goog||{}).inherits=function(a,c){function d(){}d.prototype=c.prototype;a.h=c.prototype;a.prototype=new d;a.prototype.constructor=a;a.g=function(f,g,h){for(var e=Array(arguments.length-2),b=2;b<arguments.length;b++)e[b-2]=arguments[b];return c.prototype[g].apply(f,e)}};}).call(this);
</script>
  <script data-source="gwd_webcomponents_v1_min.js" data-version="2" data-exports-type="gwd_webcomponents_v1">/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
(function(){if(void 0!==window.Reflect&&void 0!==window.customElements&&!window.customElements.polyfillWrapFlushCallback){var BuiltInHTMLElement=HTMLElement;window.HTMLElement=function(){return Reflect.construct(BuiltInHTMLElement,[],this.constructor)};HTMLElement.prototype=BuiltInHTMLElement.prototype;HTMLElement.prototype.constructor=HTMLElement;Object.setPrototypeOf(HTMLElement,BuiltInHTMLElement)}})();
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var n;/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
var p=window.Document.prototype.createElement,q=window.Document.prototype.createElementNS,aa=window.Document.prototype.importNode,ba=window.Document.prototype.prepend,ca=window.Document.prototype.append,da=window.DocumentFragment.prototype.prepend,ea=window.DocumentFragment.prototype.append,r=window.Node.prototype.cloneNode,t=window.Node.prototype.appendChild,u=window.Node.prototype.insertBefore,v=window.Node.prototype.removeChild,w=window.Node.prototype.replaceChild,x=Object.getOwnPropertyDescriptor(window.Node.prototype,
"textContent"),z=window.Element.prototype.attachShadow,A=Object.getOwnPropertyDescriptor(window.Element.prototype,"innerHTML"),B=window.Element.prototype.getAttribute,C=window.Element.prototype.setAttribute,D=window.Element.prototype.removeAttribute,E=window.Element.prototype.getAttributeNS,F=window.Element.prototype.setAttributeNS,G=window.Element.prototype.removeAttributeNS,H=window.Element.prototype.insertAdjacentElement,fa=window.Element.prototype.insertAdjacentHTML,ha=window.Element.prototype.prepend,
ia=window.Element.prototype.append,ja=window.Element.prototype.before,ka=window.Element.prototype.after,la=window.Element.prototype.replaceWith,ma=window.Element.prototype.remove,na=window.HTMLElement,I=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML"),oa=window.HTMLElement.prototype.insertAdjacentElement,pa=window.HTMLElement.prototype.insertAdjacentHTML;var qa=function(){var a=new Set;"annotation-xml color-profile font-face font-face-src font-face-uri font-face-format font-face-name missing-glyph".split(" ").forEach(function(b){return a.add(b)});return a}();function ra(a){var b=qa.has(a);a=/^[a-z][.0-9_a-z]*-[-.0-9_a-z]*$/.test(a);return!b&&a}var sa=document.contains?document.contains.bind(document):document.documentElement.contains.bind(document.documentElement);
function J(a){var b=a.isConnected;if(void 0!==b)return b;if(sa(a))return!0;for(;a&&!(a.__CE_isImportDocument||a instanceof Document);)a=a.parentNode||(window.ShadowRoot&&a instanceof ShadowRoot?a.host:void 0);return!(!a||!(a.__CE_isImportDocument||a instanceof Document))}function K(a){var b=a.children;if(b)return Array.prototype.slice.call(b);b=[];for(a=a.firstChild;a;a=a.nextSibling)a.nodeType===Node.ELEMENT_NODE&&b.push(a);return b}
function L(a,b){for(;b&&b!==a&&!b.nextSibling;)b=b.parentNode;return b&&b!==a?b.nextSibling:null}
function M(a,b,c){for(var e=a;e;){if(e.nodeType===Node.ELEMENT_NODE){var d=e;b(d);var f=d.localName;if("link"===f&&"import"===d.getAttribute("rel")){e=d.import;void 0===c&&(c=new Set);if(e instanceof Node&&!c.has(e))for(c.add(e),e=e.firstChild;e;e=e.nextSibling)M(e,b,c);e=L(a,d);continue}else if("template"===f){e=L(a,d);continue}if(d=d.__CE_shadowRoot)for(d=d.firstChild;d;d=d.nextSibling)M(d,b,c)}e=e.firstChild?e.firstChild:L(a,e)}};function ta(){var a=!(null===N||void 0===N||!N.noDocumentConstructionObserver),b=!(null===N||void 0===N||!N.shadyDomFastWalk);this.j=[];this.B=[];this.i=!1;this.shadyDomFastWalk=b;this.O=!a}function O(a,b,c,e){var d=window.ShadyDOM;if(a.shadyDomFastWalk&&d&&d.inUse){if(b.nodeType===Node.ELEMENT_NODE&&c(b),b.querySelectorAll)for(a=d.nativeMethods.querySelectorAll.call(b,"*"),b=0;b<a.length;b++)c(a[b])}else M(b,c,e)}function ua(a,b){a.i=!0;a.j.push(b)}function va(a,b){a.i=!0;a.B.push(b)}
function P(a,b){a.i&&O(a,b,function(c){return Q(a,c)})}function Q(a,b){if(a.i&&!b.__CE_patched){b.__CE_patched=!0;for(var c=0;c<a.j.length;c++)a.j[c](b);for(c=0;c<a.B.length;c++)a.B[c](b)}}function R(a,b){var c=[];O(a,b,function(d){return c.push(d)});for(b=0;b<c.length;b++){var e=c[b];1===e.__CE_state?a.connectedCallback(e):S(a,e)}}function T(a,b){var c=[];O(a,b,function(d){return c.push(d)});for(b=0;b<c.length;b++){var e=c[b];1===e.__CE_state&&a.disconnectedCallback(e)}}
function U(a,b,c){c=void 0===c?{}:c;var e=c.P,d=c.upgrade||function(g){return S(a,g)},f=[];O(a,b,function(g){a.i&&Q(a,g);if("link"===g.localName&&"import"===g.getAttribute("rel")){var h=g.import;h instanceof Node&&(h.__CE_isImportDocument=!0,h.__CE_registry=document.__CE_registry);h&&"complete"===h.readyState?h.__CE_documentLoadHandled=!0:g.addEventListener("load",function(){var k=g.import;if(!k.__CE_documentLoadHandled){k.__CE_documentLoadHandled=!0;var l=new Set;e&&(e.forEach(function(m){return l.add(m)}),
l.delete(k));U(a,k,{P:l,upgrade:d})}})}else f.push(g)},e);for(b=0;b<f.length;b++)d(f[b])}function S(a,b){try{var c=a.K(b.ownerDocument,b.localName);c&&a.M(b,c)}catch(e){V(e)}}n=ta.prototype;
n.M=function(a,b){if(void 0===a.__CE_state){b.constructionStack.push(a);try{try{if(new b.constructorFunction!==a)throw Error("The custom element constructor did not produce the element being upgraded.");}finally{b.constructionStack.pop()}}catch(f){throw a.__CE_state=2,f;}a.__CE_state=1;a.__CE_definition=b;if(b.attributeChangedCallback&&a.hasAttributes()){b=b.observedAttributes;for(var c=0;c<b.length;c++){var e=b[c],d=a.getAttribute(e);null!==d&&this.attributeChangedCallback(a,e,null,d,null)}}J(a)&&
this.connectedCallback(a)}};n.connectedCallback=function(a){var b=a.__CE_definition;if(b.connectedCallback)try{b.connectedCallback.call(a)}catch(c){V(c)}};n.disconnectedCallback=function(a){var b=a.__CE_definition;if(b.disconnectedCallback)try{b.disconnectedCallback.call(a)}catch(c){V(c)}};n.attributeChangedCallback=function(a,b,c,e,d){var f=a.__CE_definition;if(f.attributeChangedCallback&&-1<f.observedAttributes.indexOf(b))try{f.attributeChangedCallback.call(a,b,c,e,d)}catch(g){V(g)}};
n.K=function(a,b){var c=a.__CE_registry;if(c&&(a.defaultView||a.__CE_isImportDocument))return W(c,b)};
function wa(a,b,c,e){var d=b.__CE_registry;if(d&&(null===e||"http://www.w3.org/1999/xhtml"===e)&&(d=W(d,c)))try{var f=new d.constructorFunction;if(void 0===f.__CE_state||void 0===f.__CE_definition)throw Error("Failed to construct '"+c+"': The returned value was not constructed with the HTMLElement constructor.");if("http://www.w3.org/1999/xhtml"!==f.namespaceURI)throw Error("Failed to construct '"+c+"': The constructed element's namespace must be the HTML namespace.");if(f.hasAttributes())throw Error("Failed to construct '"+
c+"': The constructed element must not have any attributes.");if(null!==f.firstChild)throw Error("Failed to construct '"+c+"': The constructed element must not have any children.");if(null!==f.parentNode)throw Error("Failed to construct '"+c+"': The constructed element must not have a parent node.");if(f.ownerDocument!==b)throw Error("Failed to construct '"+c+"': The constructed element's owner document is incorrect.");if(f.localName!==c)throw Error("Failed to construct '"+c+"': The constructed element's local name is incorrect.");
return f}catch(g){return V(g),b=null===e?p.call(b,c):q.call(b,e,c),Object.setPrototypeOf(b,HTMLUnknownElement.prototype),b.__CE_state=2,b.__CE_definition=void 0,Q(a,b),b}b=null===e?p.call(b,c):q.call(b,e,c);Q(a,b);return b}
function V(a){var b=a.message,c=a.sourceURL||a.fileName||"",e=a.line||a.lineNumber||0,d=a.column||a.columnNumber||0,f=void 0;void 0===ErrorEvent.prototype.initErrorEvent?f=new ErrorEvent("error",{cancelable:!0,message:b,filename:c,lineno:e,colno:d,error:a}):(f=document.createEvent("ErrorEvent"),f.initErrorEvent("error",!1,!0,b,c,e),f.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{configurable:!0,get:function(){return!0}})});void 0===f.error&&Object.defineProperty(f,"error",
{configurable:!0,enumerable:!0,get:function(){return a}});window.dispatchEvent(f);f.defaultPrevented||console.error(a)};function xa(){var a=this;this.I=void 0;this.H=new Promise(function(b){a.L=b})}xa.prototype.resolve=function(a){if(this.I)throw Error("Already resolved.");this.I=a;this.L(a)};function X(a){var b=document;this.u=void 0;this.g=a;this.l=b;U(this.g,this.l);"loading"===this.l.readyState&&(this.u=new MutationObserver(this.J.bind(this)),this.u.observe(this.l,{childList:!0,subtree:!0}))}X.prototype.disconnect=function(){this.u&&this.u.disconnect()};X.prototype.J=function(a){var b=this.l.readyState;"interactive"!==b&&"complete"!==b||this.disconnect();for(b=0;b<a.length;b++)for(var c=a[b].addedNodes,e=0;e<c.length;e++)U(this.g,c[e])};function Y(a){this.o=new Map;this.s=new Map;this.D=new Map;this.A=!1;this.C=new Map;this.m=function(b){return b()};this.h=!1;this.v=[];this.g=a;this.F=a.O?new X(a):void 0}n=Y.prototype;n.N=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructor getters must be functions.");ya(this,a);this.o.set(a,b);this.v.push(a);this.h||(this.h=!0,this.m(function(){return c.G()}))};
n.define=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructors must be functions.");ya(this,a);za(this,a,b);this.v.push(a);this.h||(this.h=!0,this.m(function(){return c.G()}))};function ya(a,b){if(!ra(b))throw new SyntaxError("The element name '"+b+"' is not valid.");if(W(a,b))throw Error("A custom element with name '"+(b+"' has already been defined."));if(a.A)throw Error("A custom element is already being defined.");}
function za(a,b,c){a.A=!0;var e;try{var d=c.prototype;if(!(d instanceof Object))throw new TypeError("The custom element constructor's prototype is not an object.");var f=function(m){var y=d[m];if(void 0!==y&&!(y instanceof Function))throw Error("The '"+m+"' callback must be a function.");return y};var g=f("connectedCallback");var h=f("disconnectedCallback");var k=f("adoptedCallback");var l=(e=f("attributeChangedCallback"))&&c.observedAttributes||[]}catch(m){throw m;}finally{a.A=!1}c={localName:b,
constructorFunction:c,connectedCallback:g,disconnectedCallback:h,adoptedCallback:k,attributeChangedCallback:e,observedAttributes:l,constructionStack:[]};a.s.set(b,c);a.D.set(c.constructorFunction,c);return c}n.upgrade=function(a){U(this.g,a)};
n.G=function(){var a=this;if(!1!==this.h){this.h=!1;for(var b=[],c=this.v,e=new Map,d=0;d<c.length;d++)e.set(c[d],[]);U(this.g,document,{upgrade:function(k){if(void 0===k.__CE_state){var l=k.localName,m=e.get(l);m?m.push(k):a.s.has(l)&&b.push(k)}}});for(d=0;d<b.length;d++)S(this.g,b[d]);for(d=0;d<c.length;d++){for(var f=c[d],g=e.get(f),h=0;h<g.length;h++)S(this.g,g[h]);(f=this.C.get(f))&&f.resolve(void 0)}c.length=0}};n.get=function(a){if(a=W(this,a))return a.constructorFunction};
n.whenDefined=function(a){if(!ra(a))return Promise.reject(new SyntaxError("'"+a+"' is not a valid custom element name."));var b=this.C.get(a);if(b)return b.H;b=new xa;this.C.set(a,b);var c=this.s.has(a)||this.o.has(a);a=-1===this.v.indexOf(a);c&&a&&b.resolve(void 0);return b.H};n.polyfillWrapFlushCallback=function(a){this.F&&this.F.disconnect();var b=this.m;this.m=function(c){return a(function(){return b(c)})}};
function W(a,b){var c=a.s.get(b);if(c)return c;if(c=a.o.get(b)){a.o.delete(b);try{return za(a,b,c())}catch(e){V(e)}}}window.CustomElementRegistry=Y;Y.prototype.define=Y.prototype.define;Y.prototype.upgrade=Y.prototype.upgrade;Y.prototype.get=Y.prototype.get;Y.prototype.whenDefined=Y.prototype.whenDefined;Y.prototype.polyfillDefineLazy=Y.prototype.N;Y.prototype.polyfillWrapFlushCallback=Y.prototype.polyfillWrapFlushCallback;function Z(a,b,c){function e(d){return function(f){for(var g=[],h=0;h<arguments.length;++h)g[h-0]=arguments[h];h=[];for(var k=[],l=0;l<g.length;l++){var m=g[l];m instanceof Element&&J(m)&&k.push(m);if(m instanceof DocumentFragment)for(m=m.firstChild;m;m=m.nextSibling)h.push(m);else h.push(m)}d.apply(this,g);for(g=0;g<k.length;g++)T(a,k[g]);if(J(this))for(g=0;g<h.length;g++)k=h[g],k instanceof Element&&R(a,k)}}void 0!==c.prepend&&(b.prepend=e(c.prepend));void 0!==c.append&&(b.append=e(c.append))};function Aa(a){Document.prototype.createElement=function(b){return wa(a,this,b,null)};Document.prototype.importNode=function(b,c){b=aa.call(this,b,!!c);this.__CE_registry?U(a,b):P(a,b);return b};Document.prototype.createElementNS=function(b,c){return wa(a,this,c,b)};Z(a,Document.prototype,{prepend:ba,append:ca})};function Ba(a){function b(e){return function(d){for(var f=[],g=0;g<arguments.length;++g)f[g-0]=arguments[g];g=[];for(var h=[],k=0;k<f.length;k++){var l=f[k];l instanceof Element&&J(l)&&h.push(l);if(l instanceof DocumentFragment)for(l=l.firstChild;l;l=l.nextSibling)g.push(l);else g.push(l)}e.apply(this,f);for(f=0;f<h.length;f++)T(a,h[f]);if(J(this))for(f=0;f<g.length;f++)h=g[f],h instanceof Element&&R(a,h)}}var c=Element.prototype;void 0!==ja&&(c.before=b(ja));void 0!==ka&&(c.after=b(ka));void 0!==
la&&(c.replaceWith=function(e){for(var d=[],f=0;f<arguments.length;++f)d[f-0]=arguments[f];f=[];for(var g=[],h=0;h<d.length;h++){var k=d[h];k instanceof Element&&J(k)&&g.push(k);if(k instanceof DocumentFragment)for(k=k.firstChild;k;k=k.nextSibling)f.push(k);else f.push(k)}h=J(this);la.apply(this,d);for(d=0;d<g.length;d++)T(a,g[d]);if(h)for(T(a,this),d=0;d<f.length;d++)g=f[d],g instanceof Element&&R(a,g)});void 0!==ma&&(c.remove=function(){var e=J(this);ma.call(this);e&&T(a,this)})};function Ca(a){function b(d,f){Object.defineProperty(d,"innerHTML",{enumerable:f.enumerable,configurable:!0,get:f.get,set:function(g){var h=this,k=void 0;J(this)&&(k=[],O(a,this,function(y){y!==h&&k.push(y)}));f.set.call(this,g);if(k)for(var l=0;l<k.length;l++){var m=k[l];1===m.__CE_state&&a.disconnectedCallback(m)}this.ownerDocument.__CE_registry?U(a,this):P(a,this);return g}})}function c(d,f){d.insertAdjacentElement=function(g,h){var k=J(h);g=f.call(this,g,h);k&&T(a,h);J(g)&&R(a,h);return g}}function e(d,
f){function g(h,k){for(var l=[];h!==k;h=h.nextSibling)l.push(h);for(k=0;k<l.length;k++)U(a,l[k])}d.insertAdjacentHTML=function(h,k){h=h.toLowerCase();if("beforebegin"===h){var l=this.previousSibling;f.call(this,h,k);g(l||this.parentNode.firstChild,this)}else if("afterbegin"===h)l=this.firstChild,f.call(this,h,k),g(this.firstChild,l);else if("beforeend"===h)l=this.lastChild,f.call(this,h,k),g(l||this.firstChild,null);else if("afterend"===h)l=this.nextSibling,f.call(this,h,k),g(this.nextSibling,l);
else throw new SyntaxError("The value provided ("+String(h)+") is not one of 'beforebegin', 'afterbegin', 'beforeend', or 'afterend'.");}}z&&(Element.prototype.attachShadow=function(d){d=z.call(this,d);if(a.i&&!d.__CE_patched){d.__CE_patched=!0;for(var f=0;f<a.j.length;f++)a.j[f](d)}return this.__CE_shadowRoot=d});A&&A.get?b(Element.prototype,A):I&&I.get?b(HTMLElement.prototype,I):va(a,function(d){b(d,{enumerable:!0,configurable:!0,get:function(){return r.call(this,!0).innerHTML},set:function(f){var g=
"template"===this.localName,h=g?this.content:this,k=q.call(document,this.namespaceURI,this.localName);for(k.innerHTML=f;0<h.childNodes.length;)v.call(h,h.childNodes[0]);for(f=g?k.content:k;0<f.childNodes.length;)t.call(h,f.childNodes[0])}})});Element.prototype.setAttribute=function(d,f){if(1!==this.__CE_state)return C.call(this,d,f);var g=B.call(this,d);C.call(this,d,f);f=B.call(this,d);a.attributeChangedCallback(this,d,g,f,null)};Element.prototype.setAttributeNS=function(d,f,g){if(1!==this.__CE_state)return F.call(this,
d,f,g);var h=E.call(this,d,f);F.call(this,d,f,g);g=E.call(this,d,f);a.attributeChangedCallback(this,f,h,g,d)};Element.prototype.removeAttribute=function(d){if(1!==this.__CE_state)return D.call(this,d);var f=B.call(this,d);D.call(this,d);null!==f&&a.attributeChangedCallback(this,d,f,null,null)};Element.prototype.removeAttributeNS=function(d,f){if(1!==this.__CE_state)return G.call(this,d,f);var g=E.call(this,d,f);G.call(this,d,f);var h=E.call(this,d,f);g!==h&&a.attributeChangedCallback(this,f,g,h,d)};
oa?c(HTMLElement.prototype,oa):H&&c(Element.prototype,H);pa?e(HTMLElement.prototype,pa):fa&&e(Element.prototype,fa);Z(a,Element.prototype,{prepend:ha,append:ia});Ba(a)};var Da={};function Ea(a){function b(){var c=this.constructor;var e=document.__CE_registry.D.get(c);if(!e)throw Error("Failed to construct a custom element: The constructor was not registered with `customElements`.");var d=e.constructionStack;if(0===d.length)return d=p.call(document,e.localName),Object.setPrototypeOf(d,c.prototype),d.__CE_state=1,d.__CE_definition=e,Q(a,d),d;var f=d.length-1,g=d[f];if(g===Da)throw Error("Failed to construct '"+e.localName+"': This element was already constructed.");d[f]=Da;
Object.setPrototypeOf(g,c.prototype);Q(a,g);return g}b.prototype=na.prototype;Object.defineProperty(HTMLElement.prototype,"constructor",{writable:!0,configurable:!0,enumerable:!1,value:b});window.HTMLElement=b};function Fa(a){function b(c,e){Object.defineProperty(c,"textContent",{enumerable:e.enumerable,configurable:!0,get:e.get,set:function(d){if(this.nodeType===Node.TEXT_NODE)e.set.call(this,d);else{var f=void 0;if(this.firstChild){var g=this.childNodes,h=g.length;if(0<h&&J(this)){f=Array(h);for(var k=0;k<h;k++)f[k]=g[k]}}e.set.call(this,d);if(f)for(d=0;d<f.length;d++)T(a,f[d])}}})}Node.prototype.insertBefore=function(c,e){if(c instanceof DocumentFragment){var d=K(c);c=u.call(this,c,e);if(J(this))for(e=
0;e<d.length;e++)R(a,d[e]);return c}d=c instanceof Element&&J(c);e=u.call(this,c,e);d&&T(a,c);J(this)&&R(a,c);return e};Node.prototype.appendChild=function(c){if(c instanceof DocumentFragment){var e=K(c);c=t.call(this,c);if(J(this))for(var d=0;d<e.length;d++)R(a,e[d]);return c}e=c instanceof Element&&J(c);d=t.call(this,c);e&&T(a,c);J(this)&&R(a,c);return d};Node.prototype.cloneNode=function(c){c=r.call(this,!!c);this.ownerDocument.__CE_registry?U(a,c):P(a,c);return c};Node.prototype.removeChild=function(c){var e=
c instanceof Element&&J(c),d=v.call(this,c);e&&T(a,c);return d};Node.prototype.replaceChild=function(c,e){if(c instanceof DocumentFragment){var d=K(c);c=w.call(this,c,e);if(J(this))for(T(a,e),e=0;e<d.length;e++)R(a,d[e]);return c}d=c instanceof Element&&J(c);var f=w.call(this,c,e),g=J(this);g&&T(a,e);d&&T(a,c);g&&R(a,c);return f};x&&x.get?b(Node.prototype,x):ua(a,function(c){b(c,{enumerable:!0,configurable:!0,get:function(){for(var e=[],d=this.firstChild;d;d=d.nextSibling)d.nodeType!==Node.COMMENT_NODE&&
e.push(d.textContent);return e.join("")},set:function(e){for(;this.firstChild;)v.call(this,this.firstChild);null!=e&&""!==e&&t.call(this,document.createTextNode(e))}})})};var N=window.customElements;function Ga(){var a=new ta;Ea(a);Aa(a);Z(a,DocumentFragment.prototype,{prepend:da,append:ea});Fa(a);Ca(a);a=new Y(a);document.__CE_registry=a;Object.defineProperty(window,"customElements",{configurable:!0,enumerable:!0,value:a})}N&&!N.forcePolyfill&&"function"==typeof N.define&&"function"==typeof N.get||Ga();window.__CE_installPolyfill=Ga;/*

Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
})();
(function(){var b=window.document;window.WebComponents=window.WebComponents||{};var a=function(){window.removeEventListener("DOMContentLoaded",a);window.WebComponents.ready=!0;var c=b.createEvent("CustomEvent");c.initEvent("WebComponentsReady",!0,!0);setTimeout(function(){window.document.dispatchEvent(c)},0)};"complete"===b.readyState?a():window.addEventListener("DOMContentLoaded",a)})();
</script>
  <script data-source="gwdpage_min.js" data-version="13" data-exports-type="gwd-page">(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var d,e="function"==typeof Object.create?Object.create:function(a){function c(){}c.prototype=a;return new c},f;if("function"==typeof Object.setPrototypeOf)f=Object.setPrototypeOf;else{var g;a:{var h={a:!0},l={};try{l.__proto__=h;g=l.a;break a}catch(a){}g=!1}f=g?function(a,c){a.__proto__=c;if(a.__proto__!==c)throw new TypeError(a+" is not extensible");return a}:null}var m=f;function n(a,c){var b=void 0===b?null:b;var k=document.createEvent("CustomEvent");k.initCustomEvent(a,!0,!0,b);c.dispatchEvent(k)};function p(){var a=HTMLElement.call(this)||this;a.s=a.u.bind(a);a.g=[];a.l=!1;a.j=!1;a.h=!1;a.o=-1;a.m=-1;a.i=!1;return a}var q=HTMLElement;p.prototype=e(q.prototype);p.prototype.constructor=p;if(m)m(p,q);else for(var r in q)if("prototype"!=r)if(Object.defineProperties){var t=Object.getOwnPropertyDescriptor(q,r);t&&Object.defineProperty(p,r,t)}else p[r]=q[r];d=p.prototype;
d.connectedCallback=function(){var a=this;this.o=parseInt(this.getAttribute("data-gwd-width"),10)||this.clientWidth;this.m=parseInt(this.getAttribute("data-gwd-height"),10)||this.clientHeight;this.addEventListener("ready",this.s,!1);this.style.visibility="hidden";setTimeout(function(){a.g=Array.prototype.slice.call(a.querySelectorAll("*")).filter(function(c){return"function"!=typeof c.gwdLoad||"function"!=typeof c.gwdIsLoaded||c.gwdIsLoaded()?!1:!0},a);a.l=!0;0<a.g.length?a.j=!1:u(a);a.h=!0;n("attached",
a)},0)};d.disconnectedCallback=function(){this.removeEventListener("ready",this.s,!1);this.classList.remove("gwd-play-animation");n("detached",this)};d.gwdActivate=function(){this.classList.remove("gwd-inactive");Array.prototype.slice.call(this.querySelectorAll("*")).forEach(function(a){"function"==typeof a.gwdActivate&&"function"==typeof a.gwdIsActive&&0==a.gwdIsActive()&&a.gwdActivate()});this.i=!0;this.h?this.h=!1:n("attached",this);n("pageactivated",this)};
d.gwdDeactivate=function(){this.classList.add("gwd-inactive");this.classList.remove("gwd-play-animation");var a=Array.prototype.slice.call(this.querySelectorAll("*"));a.push(this);for(var c=0;c<a.length;c++){var b=a[c];if(b.classList&&(b.classList.remove("gwd-pause-animation"),b.hasAttribute("data-gwd-current-label"))){var k=b.getAttribute("data-gwd-current-label");b.classList.remove(k);b.removeAttribute("data-gwd-current-label")}delete b.gwdGotoCounters;b!=this&&"function"==typeof b.gwdDeactivate&&
"function"==typeof b.gwdIsActive&&1==b.gwdIsActive()&&b.gwdDeactivate()}this.i=!1;n("pagedeactivated",this);n("detached",this)};d.gwdIsActive=function(){return this.i};d.gwdIsLoaded=function(){return this.l&&0==this.g.length};d.gwdLoad=function(){if(this.gwdIsLoaded())u(this);else for(var a=this.g.length-1;0<=a;a--)this.g[a].gwdLoad()};d.u=function(a){a=this.g.indexOf(a.target);-1<a&&(this.g.splice(a,1),0==this.g.length&&u(this))};
function u(a){a.style.visibility="";a.j||(n("ready",a),n("pageload",a));a.j=!0}d.gwdPresent=function(){n("pagepresenting",this);this.classList.add("gwd-play-animation")};d.isPortrait=function(){return this.m>=this.o};customElements.define("gwd-page",p);}).call(this);
</script>
  <script data-source="gwdpagedeck_min.js" data-version="14" data-exports-type="gwd-pagedeck">(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var f;function l(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var m=l(this),n="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},p;
if("function"==typeof Object.setPrototypeOf)p=Object.setPrototypeOf;else{var q;a:{var r={a:!0},t={};try{t.__proto__=r;q=t.a;break a}catch(a){}q=!1}p=q?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var u=p,v=this||self;function w(a,b){a=a.split(".");var c=v;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};var x="center top bottom left right transparent".split(" ");var y=["-ms-","-moz-","-webkit-",""];function z(a,b){var c=void 0===c?!1:c;for(var d,e,g=0;g<y.length;g++)d=y[g]+"transition-duration",e=(c?y[g]:"")+b,a.style.setProperty(d,e)}function A(a){var b=document,c=b.getElementsByTagName("head")[0];if(!c){var d=b.getElementsByTagName("body")[0];c=b.createElement("head");d.parentNode.insertBefore(c,d)}b=b.createElement("style");b.textContent=a;c.appendChild(b);return b};function B(a,b,c){c=void 0===c?null:c;var d=document.createEvent("CustomEvent");d.initCustomEvent(a,!0,!0,c);b.dispatchEvent(d)}function C(a,b,c){function d(e){a.removeEventListener(b,d);c(e)}a.addEventListener(b,d)};function D(a){for(var b=0;b<x.length;b++)a.classList.remove(x[b])}function E(a,b){function c(){a.removeEventListener("webkitTransitionEnd",c);a.removeEventListener("transitionend",c);b()}a.addEventListener("webkitTransitionEnd",c);a.addEventListener("transitionend",c)}function F(a,b,c,d){c="transform: matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,"+c+","+d+",0,1);";return a+"."+b+"{-webkit-"+c+"-moz-"+c+"-ms-"+c+c+"}"}
function G(a,b,c){a=(a&&"#")+a+".gwd-pagedeck > .gwd-page";return F(a,"center",0,0)+F(a,"top",0,c)+F(a,"bottom",0,-c)+F(a,"left",b,0)+F(a,"right",-b,0)}
function H(a,b,c,d,e,g,h){d=void 0===d?"none":d;this.g=a;this.h=b;this.m=c;this.i="none"==d?0:void 0===e?1E3:e;this.j=void 0===g?"linear":g;this.l=[];if(this.i){a=d;h=void 0===h?"top":h;if(this.g){this.g.classList.add("gwd-page");this.g.classList.add("center");b="center";if("push"==a)switch(h){case "top":b="top";break;case "bottom":b="bottom";break;case "left":b="left";break;case "right":b="right"}this.l.push(b);"fade"==a&&this.l.push("transparent")}b="center";if("none"!=a&&"fade"!=a)switch(h){case "top":b=
"bottom";break;case "bottom":b="top";break;case "left":b="right";break;case "right":b="left"}this.h.classList.add(b);this.h.classList.add("gwd-page");"fade"==a&&this.h.classList.add("transparent")}}
H.prototype.start=function(){if(this.i){E(this.h,this.s.bind(this));this.g&&(z(this.g,this.i+"ms"),this.g.classList.add(this.j));z(this.h,this.i+"ms");this.h.classList.add(this.j);var a=this.h;a.setAttribute("gwd-reflow",a.offsetWidth);if(this.g)for(a=0;a<this.l.length;a++)this.g.classList.add(this.l[a]);D(this.h)}else this.m()};H.prototype.s=function(){this.g&&(D(this.g),z(this.g,0),this.g.classList.remove(this.j));z(this.h,0);this.h.classList.remove(this.j);this.m()};var I={K:{value:!0,configurable:!0}};var J=Object,K=J.freeze,L=[];Array.isArray(L)&&!Object.isFrozen(L)&&Object.defineProperties(L,I);K.call(J,L);function N(a,b){var c=v.performance;a={label:a,type:9,value:c&&c.now&&c.timing?Math.floor(c.now()+c.timing.navigationStart):Date.now()};b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];2048>b.length&&b.push(a)};var O={},P=!1,Q=!1;O.I=function(a){P||(P=!0,N("11",a))};O.v=function(a){Q||(Q=!0,N("12",a))};O.J=function(a,b,c){var d=b;d=void 0===d?v:d;if(d=(d=d.performance)&&d.now?d.now():null)a={label:a,type:void 0===c?0:c,value:d},b=b.google_js_reporting_queue=b.google_js_reporting_queue||[],2048>b.length&&b.push(a)};O.reset=function(a){Q=P=!1;(a.google_js_reporting_queue=a.google_js_reporting_queue||[]).length=0};w("gwd.rumUtil",O);w("gwd.rumUtil.logContentLoading",O.I);
w("gwd.rumUtil.logContentRendered",O.v);w("gwd.rumUtil.logTimingEvent",O.J);w("gwd.rumUtil.reset",O.reset);function R(){var a=HTMLElement.call(this)||this;C(window,"WebComponentsReady",a.H.bind(a));a.s=a.o.bind(a,"shake");a.A=a.o.bind(a,"tilt");a.m=a.o.bind(a,"rotatetoportrait");a.l=a.o.bind(a,"rotatetolandscape");a.g=[];a.B=a.G.bind(a);a.D=a.F.bind(a);a.C=null;a.i=null;a.h=-1;a.j=!1;return a}var S=HTMLElement;R.prototype=n(S.prototype);R.prototype.constructor=R;
if(u)u(R,S);else for(var T in S)if("prototype"!=T)if(Object.defineProperties){var U=Object.getOwnPropertyDescriptor(S,T);U&&Object.defineProperty(R,T,U)}else R[T]=S[T];f=R.prototype;f.connectedCallback=function(){this.addEventListener("pageload",this.B,!1);document.body.addEventListener("shake",this.s,!0);document.body.addEventListener("tilt",this.A,!0);document.body.addEventListener("rotatetoportrait",this.m,!0);document.body.addEventListener("rotatetolandscape",this.l,!0)};
f.disconnectedCallback=function(){this.removeEventListener("pageload",this.B,!1);document.body&&(document.body.removeEventListener("shake",this.s,!0),document.body.removeEventListener("tilt",this.A,!0),document.body.removeEventListener("rotatetoportrait",this.m,!0),document.body.removeEventListener("rotatetolandscape",this.l,!0))};
f.H=function(){this.classList.add("gwd-pagedeck");this.C||(this.C=A(G(this.id,this.offsetWidth,this.offsetHeight)));this.g=Array.prototype.slice.call(this.querySelectorAll("gwd-page"));this.g.forEach(function(a){a.classList.add("gwd-page")});for(B("pagesregistered",this,{pages:this.g.slice()});this.firstChild;)this.removeChild(this.firstChild);-1==this.h&&void 0!==this.u&&this.goToPage(this.u)};
function V(a,b,c,d,e,g){if(!(a.h==b||0>b||b>a.g.length-1||a.i)){var h=a.g[a.h],k=a.g[b];a.h=b;a.i=new H(h,k,a.D,c,d,e,g);var M=k.gwdLoad&&!k.gwdIsLoaded();a.j=M;C(k,"attached",function(){k.gwdActivate();M?k.gwdLoad():W(a)});a.appendChild(k)}}f.G=function(a){this.j&&a.target.parentNode==this&&(W(this),this.j=!1)};function W(a){(0,O.v)(window);a.i.start();B("pagetransitionstart",a)}
f.F=function(){if(this.i){var a=this.i.g,b=this.i.h;this.i=null;B("pagetransitionend",this,{outgoingPage:a?a:null,incomingPage:b});a&&a.gwdDeactivate();b.gwdPresent()}};f.findPageIndexByAttributeValue=function(a,b){for(var c=this.g.length,d,e=0;e<c;e++)if(d=this.g[e],"boolean"==typeof b){if(d.hasAttribute(a))return e}else if(d.getAttribute(a)==b)return e;return-1};f.goToNextPage=function(a,b,c,d,e){var g=this.h,h=g+1;h>=this.g.length&&(h=a?0:g);V(this,h,b,c,d,e)};
f.goToPreviousPage=function(a,b,c,d,e){var g=this.h,h=this.g.length,k=g-1;0>k&&(k=a?h-1:g);V(this,k,b,c,d,e)};f.goToPage=function(a,b,c,d,e){this.g.length?(a="number"==typeof a?a:this.findPageIndexByAttributeValue("id",a),0<=a&&V(this,a,b,c,d,e)):this.u=a};f.getPages=function(){return this.g};f.getPage=function(a){if("number"!=typeof a){if(!a)return null;a=this.findPageIndexByAttributeValue("id",a)}return 0>a||a>this.g.length-1?null:this.g[a]};f.getCurrentPage=function(){return this.getPage(this.h)};
f.getDefaultPage=function(){var a=this.getAttribute("default-page");return a?this.getPage(this.findPageIndexByAttributeValue("id",a)):this.getPage(0)};f.getOrientationSpecificPage=function(a,b){b=this.getPage(b);var c=b.getAttribute("alt-orientation-page");if(!c)return b;var d=b.isPortrait();a=1==a;c=this.getPage(c);return a==d?b:c};f.o=function(a,b){if(b.target==document.body){var c=this.getPage(this.h);B(a,c,b.detail)}};
f.getElementById=function(a){for(var b=this.g.length,c=0;c<b;c++){var d=this.g[c].querySelector("#"+a);if(d)return d}return null};f.getElementsBySelector=function(a){for(var b=this.g.length,c=[],d=0;d<b;d++){var e=this.g[d].querySelectorAll(a);e&&(c=c.concat(Array.prototype.slice.call(e)))}return c};m.Object.defineProperties(R.prototype,{currentIndex:{configurable:!0,enumerable:!0,get:function(){return 0<=this.h?this.h:void 0}}});customElements.define("gwd-pagedeck",R);}).call(this);
</script>
  <script data-source="Enabler.js" data-exports-type="gwd-google-ad" src="Enabler.js"></script>
  <script data-source="gwdgooglead_min.js" data-version="8" data-exports-type="gwd-google-ad">(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var g,k="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},l;if("function"==typeof Object.setPrototypeOf)l=Object.setPrototypeOf;else{var m;a:{var n={a:!0},p={};try{p.__proto__=n;m=p.a;break a}catch(a){}m=!1}l=m?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var q=l,r=this||self;
function t(a,b){a=a.split(".");var c=r;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function u(){this.g={}}u.prototype.add=function(a,b){a="string"===typeof a?a:a.getString();this.g[a]||(this.g[a]=[]);this.g[a].push(b)};function v(a){var b=[],c="object"==typeof gwd&&"GwdId"in gwd,d;for(d in a.g)b.push(c?new gwd.GwdId(d):d);return b}function w(a,b){return b?a.g["string"===typeof b?b:b.getString()]||[]:[]};function x(a,b){if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a}function y(a){return"gwd-page"==a.tagName.toLowerCase()||"gwd-page"==a.getAttribute("is")}function z(a){if(y(a))return a;for(;a&&9!=a.nodeType;)if((a=a.parentElement)&&y(a))return a;return null};function A(a,b){this.g=a;this.j=b;this.h=this.i.bind(this)}A.prototype.i=function(a){this.j(a)};function C(a){return"string"===typeof a?document.getElementById(a):a.getElement(document)};function D(a,b,c){c=void 0===c?null:c;var d=document.createEvent("CustomEvent");d.initCustomEvent(a,!0,!0,c);b.dispatchEvent(d);return d};var E={ta:{value:!0,configurable:!0}};var F=Object,G=F.freeze,H=[];Array.isArray(H)&&!Object.isFrozen(H)&&Object.defineProperties(H,E);G.call(F,H);function I(a,b){var c=r.performance;a={label:a,type:9,value:c&&c.now&&c.timing?Math.floor(c.now()+c.timing.navigationStart):Date.now()};b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];2048>b.length&&b.push(a)};var J={},K=!1,L=!1;J.$=function(a){K||(K=!0,I("11",a))};J.oa=function(a){L||(L=!0,I("12",a))};J.pa=function(a,b,c){var d=b;d=void 0===d?r:d;if(d=(d=d.performance)&&d.now?d.now():null)a={label:a,type:void 0===c?0:c,value:d},b=b.google_js_reporting_queue=b.google_js_reporting_queue||[],2048>b.length&&b.push(a)};J.reset=function(a){L=K=!1;(a.google_js_reporting_queue=a.google_js_reporting_queue||[]).length=0};t("gwd.rumUtil",J);t("gwd.rumUtil.logContentLoading",J.$);
t("gwd.rumUtil.logContentRendered",J.oa);t("gwd.rumUtil.logTimingEvent",J.pa);t("gwd.rumUtil.reset",J.reset);function M(){var a=HTMLElement.call(this)||this;a.U=a.ja.bind(a);a.M=a.la.bind(a);a.V=a.ka.bind(a);a.H=a.ia.bind(a);a.G=a.ga.bind(a);a.I=D.bind(null,"expandfinish",a);a.F=D.bind(null,"collapsefinish",a);a.T=a.ha.bind(a);a.s=a.na.bind(a);a.da=a.ba.bind(a);a.Y=a.ma.bind(a);a.Z=a.ca.bind(a);a.m=null;a.i=null;a.D=!1;a.C=!1;a.N=[];a.u=!1;a.L=!1;a.B=null;a.v=!1;a.J=!1;a.K=window.innerHeight>=window.innerWidth?1:2;a.h=null;a.l=null;a.o=null;a.W=!1;a.S=!1;return a}var N=HTMLElement;M.prototype=k(N.prototype);
M.prototype.constructor=M;if(q)q(M,N);else for(var O in N)if("prototype"!=O)if(Object.defineProperties){var P=Object.getOwnPropertyDescriptor(N,O);P&&Object.defineProperty(M,O,P)}else M[O]=N[O];g=M.prototype;
g.connectedCallback=function(){var a=this;this.W||(this.L=this.hasAttribute("fullscreen"),document.body.style.opacity="0",this.W=!0);Enabler.addEventListener(studio.events.StudioEvent.EXPAND_START,this.H);Enabler.addEventListener(studio.events.StudioEvent.COLLAPSE_START,this.G);Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_EXPAND_START,this.H);Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_COLLAPSE_START,this.G);Enabler.addEventListener(studio.events.StudioEvent.EXPAND_FINISH,
this.I);Enabler.addEventListener(studio.events.StudioEvent.COLLAPSE_FINISH,this.F);Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_EXPAND_FINISH,this.I);Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_COLLAPSE_FINISH,this.F);Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_DIMENSIONS,this.T);window.addEventListener("resize",this.Z,!1);(0,J.$)(window);setTimeout(function(){a.g=a.querySelector("gwd-pagedeck");a.g.addEventListener("pagetransitionend",a.Y,!1);
a.h=document.getElementById(a.getAttribute("data-provider"));a.l=document.querySelector("gwd-data-binder");a.o=document.querySelector("gwd-responsive-attributes-helper");a.o&&a.o.applyOverrides();var b=a.querySelector("gwd-metric-configuration"),c=new u;if(b){b=Array.prototype.slice.call(b.getElementsByTagName("gwd-metric-event"));for(var d=0;d<b.length;d++){var e=b[d],f=e.getAttribute("source");if(f){var h=e.getAttribute("exit");e={event:e.getAttribute("event"),qa:e.getAttribute("metric")||h,ea:e.hasAttribute("cumulative"),
exit:h};c.add(Q(f),e)}}}a.R=c;a.X=new A(a.R,a.da)},0)};
g.disconnectedCallback=function(){Enabler.removeEventListener(studio.events.StudioEvent.INIT,this.U);Enabler.removeEventListener(studio.events.StudioEvent.VISIBLE,this.M);Enabler.removeEventListener(studio.events.StudioEvent.PAGE_LOADED,this.V);Enabler.removeEventListener(studio.events.StudioEvent.EXPAND_START,this.H);Enabler.removeEventListener(studio.events.StudioEvent.COLLAPSE_START,this.G);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_EXPAND_START,this.H);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_COLLAPSE_START,
this.G);Enabler.removeEventListener(studio.events.StudioEvent.EXPAND_FINISH,this.I);Enabler.removeEventListener(studio.events.StudioEvent.COLLAPSE_FINISH,this.F);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_EXPAND_FINISH,this.I);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_COLLAPSE_FINISH,this.F);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_DIMENSIONS,this.T);this.g.removeEventListener("pagetransitionend",this.Y,!1);window.removeEventListener("resize",
this.Z,!1);this.h&&this.m&&this.h.removeEventListener("ready",this.m);this.l&&this.i&&this.l.removeEventListener("bindingfinished",this.i);Enabler.removeEventListener(studio.events.StudioEvent.HOSTPAGE_SCROLL,this.s,!1);window.removeEventListener("message",this.s,!1)};g.initAd=function(){this.u=!1;var a=this.U;Enabler.removeEventListener(studio.events.StudioEvent.INIT,a);Enabler.addEventListener(studio.events.StudioEvent.INIT,a);Enabler.isInitialized()&&a()};
g.exit=function(a,b,c,d,e){c=void 0===c?!1:c;d=void 0===d?!0:d;Enabler.exit(a,b);d&&R(this);c&&this.goToPage(e)};g.exitOverride=function(a,b,c,d,e){c=void 0===c?!1:c;d=void 0===d?!0:d;Enabler.exitOverride(a,b);d&&R(this);c&&this.goToPage(e)};g.incrementCounter=function(a,b){Enabler.counter(a,b)};g.startTimer=function(a){Enabler.startTimer(a)};g.stopTimer=function(a){Enabler.stopTimer(a)};g.reportManualClose=function(){Enabler.reportManualClose()};
g.ba=function(a){var b=a.target,c=Q(b),d=c+": "+a.type;a:{var e=w(this.R,c);for(var f=0;f<e.length;f++)if(e[f].event==a.type){e=e[f];break a}e=void 0}e.exit&&a.detail&&a.detail.url?(d=c+": "+e.exit,a.detail["exit-id"]&&(d=a.detail["exit-id"]),b="",null!=a.detail["product-index"]&&(b=a.detail["product-index"]),this.h&&0==(this.h.getAttribute("gwd-schema-id")||"").indexOf("dynamic_remarketing")?(c=a.detail["action-event"],e={},c&&(e.clickX=c.clientX||c.changedTouches[0].clientX,e.clickY=c.clientY||
c.changedTouches[0].clientY),Enabler.dynamicExit(d,a.detail.url,b,void 0,e)):Enabler.exitOverride(d,a.detail.url),a.detail.handled=!0,a.detail.collapse&&this.goToPage()):(a=z(b))&&a.gwdIsActive()&&this.incrementCounter(e.qa||d,e.ea)};
g.ja=function(){function a(){if(b.hasAttribute("polite-load")){var d=b.V;Enabler.isPageLoaded()?d():Enabler.addEventListener(studio.events.StudioEvent.PAGE_LOADED,d)}else d=b.M,Enabler.isVisible()?d():Enabler.addEventListener(studio.events.StudioEvent.VISIBLE,d)}var b=this;Enabler.removeEventListener(studio.events.StudioEvent.HOSTPAGE_SCROLL,this.s,!1);window.removeEventListener("message",this.s,!1);Enabler.isServingInLiveEnvironment()?Enabler.addEventListener(studio.events.StudioEvent.HOSTPAGE_SCROLL,
this.s,!1):window.addEventListener("message",this.s,!1);if(this.L){var c=function(d){b.B=!!d.supported;b.B&&D("fullscreensupport",b);Enabler.removeEventListener(studio.events.StudioEvent.FULLSCREEN_SUPPORT,c);a()};Enabler.addEventListener(studio.events.StudioEvent.FULLSCREEN_SUPPORT,c);Enabler.queryFullscreenSupport()}else a()};
g.la=function(a){var b=this;if(this.u)this.h&&S(this,null);else{var c;a&&(c=a.detail);var d=this.sa.bind(this,c);this.h&&(d=this.fa.bind(this,d));if(this.L){Enabler.setResponsiveExpanding(!0);var e=function(f){b.J=f;d()};Enabler.loadModule(studio.module.ModuleId.GDN,function(){var f=studio.sdk.gdn.getConfig();f.isInCreativeToolsetContext()?f.isInterstitial(e):d()})}else d()}};g.ka=function(){var a=this.M;Enabler.isVisible()?a():Enabler.addEventListener(studio.events.StudioEvent.VISIBLE,a)};
g.fa=function(a){var b=this;this.h?(this.m&&this.h.removeEventListener("ready",this.m),this.m=function(){b.o&&b.S&&b.o.applyOverrides();S(b,a)},this.h.isDataLoaded()&&this.m(),this.h.addEventListener("ready",this.m)):a()};
function S(a,b){var c=!!b;if(a.l){a.i&&(a.l.removeEventListener("bindingfinished",a.i),a.i=null);var d=a.h.getData();d&&(c=a.g.getElementsBySelector("*"),c=c.concat(a.g.getPages()),a.l.bindData(d,c)?a.aa(b):(a.i=a.aa.bind(a,b),a.l.addEventListener("bindingfinished",a.i)),c=!1)}c&&b()}
g.aa=function(a){this.S=!0;this.i&&(this.l.removeEventListener("bindingfinished",this.i),this.i=null);if(this.u){var b=document.getElementsByTagName("gwd-text-helper");0<b.length&&b[0].refitAll()}D("dynamicelementsready",this);a&&a()};g.sa=function(a){this.u||(this.u=!0,document.body.style.opacity="",D("adinitialized",this,a),this.J?(a=this.g.getPage(this.g.findPageIndexByAttributeValue("expanded",!0)),this.goToPage(a.id)):this.goToPage())};
g.goToPage=function(a,b,c,d,e){var f=this.g.getPage(this.g.currentIndex);if(a=a?this.g.getPage(a):this.g.getDefaultPage()){var h=!!f&&!!a&&!this.D&&!this.J&&!f.hasAttribute("expanded")&&a.hasAttribute("expanded");f=!!f&&!!a&&!this.C&&!this.J&&f.hasAttribute("expanded")&&!a.hasAttribute("expanded");h&&this.C||f&&this.D||((this.j=a.id,b&&(this.A={transition:b,duration:c,easing:d,direction:e}),h)?this.L&&!1!==this.B?this.B&&(this.v=!0,Enabler.requestFullscreenExpand()):Enabler.requestExpand():f?this.v?
Enabler.requestFullscreenCollapse():Enabler.requestCollapse():(this.C=this.D=!1,this.P()))}};g.ia=function(){D("expandstart",this);this.B?(this.v=!0,Enabler.finishFullscreenExpand()):Enabler.finishExpand();if(!this.j){var a=this.g.getPage(this.g.findPageIndexByAttributeValue("expanded",!0));a&&(this.j=a.id)}a=this.g.getPage(this.g.currentIndex);this.D=!!a&&this.j!=a.id;window.setTimeout(this.P.bind(this),30)};
g.ga=function(){D("collapsestart",this);this.v?(Enabler.finishFullscreenCollapse(),this.v=!1):Enabler.finishCollapse();this.j||(this.reportManualClose(),this.j=this.g.getDefaultPage().id);var a=this.g.getPage(this.g.currentIndex);this.C=!!a&&this.j!=a.id;window.setTimeout(this.P.bind(this),30)};
g.na=function(a){if(Enabler.isServingInLiveEnvironment())var b=a;else{if(!a.data||"string"!==typeof a.data)return;try{b=JSON.parse(a.data)}catch(c){return}if(b.eventType!==studio.events.StudioEvent.HOSTPAGE_SCROLL)return}D("hostpagescroll",this,b)};g.ca=function(){this.u&&this.o&&(this.o.applyOverrides(),S(this,null));if(!this.j){var a=window.innerHeight>=window.innerWidth?1:2;this.K!=a&&(this.K=a,(a=this.g.getPage(this.g.currentIndex))&&window.setTimeout(this.goToPage.bind(this,a.id),0))}};
g.ha=function(a){a&&Enabler.setResponsiveSize(a.width,a.height)};g.P=function(){if(this.j){this.K=window.innerHeight>=window.innerWidth?1:2;var a=this.g.getOrientationSpecificPage(this.K,this.j);this.A?this.g.goToPage(a.id,this.A.transition,this.A.duration,this.A.easing,this.A.direction):this.g.goToPage(a.id);this.v&&a.classList.add("fs")}this.A=this.j=void 0};
g.ma=function(a){this.C=this.D=!1;if(a.target==this.g){a=a.detail;var b=a.outgoingPage;a=a.incomingPage;if(b){var c=this.X;if(b.nodeType==Node.ELEMENT_NODE)for(var d=v(c.g),e=0;e<d.length;e++){var f=C(d[e]);if(f&&x(b,f))for(var h=w(c.g,d[e]),B=0;B<h.length;B++)f.removeEventListener(h[B].event,c.h,!1)}if((b=b.querySelectorAll("video, gwd-video"))&&0<b.length)for(this.O=[];this.N.length;)studio.video.Reporter.detach(this.N.shift())}b=this.X;if(a.nodeType==Node.ELEMENT_NODE)for(c=v(b.g),d=0;d<c.length;d++)if((e=
C(c[d]))&&x(a,e)&&(f=w(b.g,c[d])))for(h=0;h<f.length;h++)e.addEventListener(f[h].event,b.h,!1);(a=a.querySelectorAll("video, gwd-video"))&&0<a.length&&(b=studio.video&&studio.video.Reporter,c=this.ra.bind(this),this.O=Array.prototype.slice.call(a),b?c():Enabler.loadModule(studio.module.ModuleId.VIDEO,c))}};g.ra=function(){for(var a,b;this.O.length;)if(b=this.O.shift(),a=Q(b))studio.video.Reporter.attach(a,"gwd-video"==b.tagName.toLowerCase()?b.nativeElement:b,b.autoplay),this.N.push(a)};
function Q(a){return"object"==typeof gwd&&"GwdId"in gwd?(new gwd.GwdId(a)).getString():"string"==typeof a?a:a.id}function R(a){a=Array.prototype.slice.call(a.g.querySelectorAll("audio, video, gwd-video, gwd-youtube, gwd-youtube-livestream, gwd-audio"));for(var b=0;b<a.length;b++)a[b].pause()}customElements.define("gwd-google-ad",M);}).call(this);
</script>
  <script data-source="gwdimage_min.js" data-version="16" data-exports-type="gwd-image">(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var d,e="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b};function h(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var k=h(this),n;
if("function"==typeof Object.setPrototypeOf)n=Object.setPrototypeOf;else{var p;a:{var q={a:!0},r={};try{r.__proto__=q;p=r.a;break a}catch(a){}p=!1}n=p?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var u=n;var v=/^\d*\.?\d+\s\d*\.?\d+$/;function w(a){var b=!1,c=a.getAttribute("focalpoint"),g=a.getAttribute("scaling");"cover"!==g&&"none"!==g||a.hasAttribute("disablefocalpoint")||!c||!v.test(c)||(b=!0);return b};function x(a){return"gwd-page"==a.tagName.toLowerCase()||"gwd-page"==a.getAttribute("is")}function y(a){if(x(a))return a;for(;a&&9!=a.nodeType;)if((a=a.parentElement)&&x(a))return a;return null};function z(a){var b=!1,c=null;return function(){b=!0;c||(b&&(a(),b=!1),c=window.setTimeout(function(){c=null;b&&(a(),b=!1)},250))}};var A=["alignment","alt","focalpoint","scaling","source"];function B(){var a=HTMLElement.call(this)||this;a.g=document.createElement("img");a.l=a.s.bind(a);a.o=z(a.u.bind(a));a.h=0;a.i=-1;a.j=-1;a.m=!1;a.g.addEventListener("load",a.l,!1);a.g.addEventListener("error",a.l,!1);return a}var C=HTMLElement;B.prototype=e(C.prototype);B.prototype.constructor=B;
if(u)u(B,C);else for(var D in C)if("prototype"!=D)if(Object.defineProperties){var E=Object.getOwnPropertyDescriptor(C,D);E&&Object.defineProperty(B,D,E)}else B[D]=C[D];d=B.prototype;
d.connectedCallback=function(){if(!this.m){for(;this.firstChild;)this.removeChild(this.firstChild);var a=this.getAttribute("src");a&&(this.setAttribute("source",a),this.removeAttribute("src"));a=this.ownerDocument.createElement("div");a.classList.add("intermediate-element");a.appendChild(this.g);this.appendChild(a);this.m=!0}this.gwdIsLoaded()||((a=y(this))?a.gwdIsLoaded()&&this.gwdLoad():this.gwdLoad());w(this)&&window.addEventListener("resize",this.o,!1)};
d.disconnectedCallback=function(){window.removeEventListener("resize",this.o,!1)};d.attributeChangedCallback=function(a){if("source"==a)0!==this.h&&this.gwdLoad();else if("scaling"==a)F(this);else if("alignment"==a)G(this);else if("focalpoint"==a)H(this);else if("alt"==a){var b=this.g;if(this.hasAttribute(a)){var c=this.getAttribute(a);b.setAttribute(a,c)}else b.removeAttribute(a)}};
d.s=function(a){if(2!=this.h){a&&"error"==a.type?(this.h=3,this.i=this.j=-1,this.g.style.backgroundImage=""):(-1!=this.j&&-1!=this.i||!this.getAttribute("source")||(this.j=this.naturalWidth,this.i=this.naturalHeight),this.h=2);F(this);w(this)?H(this):G(this);var b=void 0===b?null:b;a=document.createEvent("CustomEvent");a.initCustomEvent("ready",!0,!0,b);this.dispatchEvent(a)}};d.u=function(){w(this)&&H(this)};
d.gwdLoad=function(){this.h=1;this.i=this.j=-1;var a=this.getAttribute("source")||"data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==";this.g.setAttribute("src",a)};d.gwdIsLoaded=function(){return 2==this.h||3==this.h};
function F(a){if(2==a.h){var b=a.getAttribute("source"),c=a.getAttribute("scaling")||"stretch";"stretch"==c?(a.classList.remove("scaled-proportionally"),a.g.style.backgroundImage="",a=a.g,b=b||"data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==",b!=a.getAttribute("src")&&a.setAttribute("src",b)):(a.classList.add("scaled-proportionally"),a.g.style.backgroundImage=b?"url("+JSON.stringify(b)+")":"",a.g.style.backgroundSize="none"!=c?c:"auto",b=a.g,"data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="!=
b.getAttribute("src")&&b.setAttribute("src","data:image/gif;base64,R0lGODlhAQABAPAAAAAAAAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="))}}function G(a){var b=a.getAttribute("alignment")||"center";a.g.style.backgroundPosition=b}
function H(a){var b=a.width,c=a.height,g=a.j,t=a.i,l=1;"cover"==a.getAttribute("scaling")&&(l=c/b>t/g?c/t:b/g);var f=a.getAttribute("focalpoint").split(" "),m=parseFloat(f[0])*l-b/2;f=parseFloat(f[1])*l-c/2;m=0<m?Math.min(m,g*l-b):0;f=0<f?Math.min(f,t*l-c):0;a.g.style.backgroundPositionX=-m+"px";a.g.style.backgroundPositionY=-f+"px"}
k.Object.defineProperties(B.prototype,{nativeElement:{configurable:!0,enumerable:!0,get:function(){return this.g}},assetHeight:{configurable:!0,enumerable:!0,get:function(){return this.i}},assetWidth:{configurable:!0,enumerable:!0,get:function(){return this.j}},naturalHeight:{configurable:!0,enumerable:!0,get:function(){return this.g.naturalHeight}},naturalWidth:{configurable:!0,enumerable:!0,get:function(){return this.g.naturalWidth}},height:{configurable:!0,enumerable:!0,get:function(){return this.g.height},
set:function(a){this.g.height=a}},width:{configurable:!0,enumerable:!0,get:function(){return this.g.width},set:function(a){this.g.width=a}},alt:{configurable:!0,enumerable:!0,get:function(){return this.g.alt},set:function(a){this.g.alt=a}},src:{configurable:!0,enumerable:!0,get:function(){return this.g.src}}});k.Object.defineProperties(B,{observedAttributes:{configurable:!0,enumerable:!0,get:function(){return A}}});customElements.define("gwd-image",B);}).call(this);
</script>
  <script type="text/javascript" gwd-events="support">var gwd=gwd||{};gwd.actions=gwd.actions||{};gwd.actions.events=gwd.actions.events||{};gwd.actions.events.getElementById=function(id){var element=document.getElementById(id);if(!element){var pageDeck=document.querySelector("gwd-pagedeck");if(pageDeck){if(typeof pageDeck.getElementById==="function"){element=pageDeck.getElementById(id)}}}if(!element){switch(id){case"document.body":element=document.body;break;case"document":element=document;break;case"window":element=window;break;default:break}}return element};gwd.actions.events.addHandler=function(eventTarget,eventName,eventHandler,useCapture){var targetElement=gwd.actions.events.getElementById(eventTarget);if(targetElement){targetElement.addEventListener(eventName,eventHandler,useCapture)}};gwd.actions.events.removeHandler=function(eventTarget,eventName,eventHandler,useCapture){var targetElement=gwd.actions.events.getElementById(eventTarget);if(targetElement){targetElement.removeEventListener(eventName,eventHandler,useCapture)}};gwd.actions.events.setInlineStyle=function(id,styles){var element=gwd.actions.events.getElementById(id);if(!element||!styles){return}var transitionProperty=element.style.transition!==undefined?"transition":"-webkit-transition";var prevTransition=element.style[transitionProperty];var splitStyles=styles.split(/\s*;\s*/);var nameValue;splitStyles.forEach(function(splitStyle){if(splitStyle){var regex=new RegExp("[:](?![/]{2})");nameValue=splitStyle.split(regex);nameValue[1]=nameValue[1]?nameValue[1].trim():null;if(!(nameValue[0]&&nameValue[1])){return}element.style.setProperty(nameValue[0],nameValue[1])}});function restoreTransition(event){var el=event.target;el.style.transition=prevTransition;el.removeEventListener(event.type,restoreTransition,false)}element.addEventListener("transitionend",restoreTransition,false);element.addEventListener("webkitTransitionEnd",restoreTransition,false)};gwd.actions.timeline=gwd.actions.timeline||{};gwd.actions.timeline.dispatchTimedEvent=function(event){var customEventTarget=event.target;if(customEventTarget){var customEventName=customEventTarget.getAttribute("data-event-name");if(customEventName){event.stopPropagation();var event=document.createEvent("CustomEvent");event.initCustomEvent(customEventName,true,true,null);customEventTarget.dispatchEvent(event)}}};gwd.actions.timeline.captureAnimationEnd=function(element){if(!element){return}var animationEndEvents=["animationend","webkitAnimationEnd"];for(var i=0;i<animationEndEvents.length;i++){element.addEventListener(animationEndEvents[i],gwd.actions.timeline.dispatchTimedEvent,true)}};gwd.actions.timeline.releaseAnimationEnd=function(element){if(!element){return}var animationEndEvents=["animationend","webkitAnimationEnd"];for(var i=0;i<animationEndEvents.length;i++){element.removeEventListener(animationEndEvents[i],gwd.actions.timeline.dispatchTimedEvent,true)}};gwd.actions.timeline.pauseAnimationClassName="gwd-pause-animation";gwd.actions.timeline.CURRENT_LABEL_ANIMATION="data-gwd-current-label";gwd.actions.timeline.reflow=function(el){el.offsetWidth=el.offsetWidth};gwd.actions.timeline.pause=function(id){var el=gwd.actions.events.getElementById(id);el&&el.classList&&el.classList.add(gwd.actions.timeline.pauseAnimationClassName)};gwd.actions.timeline.play=function(id){var el=gwd.actions.events.getElementById(id);el&&el.classList&&el.classList.remove(gwd.actions.timeline.pauseAnimationClassName)};gwd.actions.timeline.togglePlay=function(id){var el=gwd.actions.events.getElementById(id);el&&el.classList&&el.classList.toggle(gwd.actions.timeline.pauseAnimationClassName)};gwd.actions.timeline.gotoAndPlay=function(id,animClass){var el=gwd.actions.events.getElementById(id);if(!(el&&el.classList&&id&&animClass)){return false}var currentLabelAnimClass=el.getAttribute(gwd.actions.timeline.CURRENT_LABEL_ANIMATION);if(currentLabelAnimClass){el.classList.remove(currentLabelAnimClass);el.removeAttribute(gwd.actions.timeline.CURRENT_LABEL_ANIMATION)}gwd.actions.timeline.play(id);if(currentLabelAnimClass==animClass){gwd.actions.timeline.reflow(el)}el.classList.add(animClass);el.setAttribute(gwd.actions.timeline.CURRENT_LABEL_ANIMATION,animClass);return true};gwd.actions.timeline.gotoAndPause=function(id,animClass){var el=gwd.actions.events.getElementById(id);if(!(el&&el.classList)){return false}if(gwd.actions.timeline.gotoAndPlay(id,animClass)){var timeoutId=window.setTimeout(function(){el.classList.add(gwd.actions.timeline.pauseAnimationClassName)},40)}return!!timeoutId};gwd.actions.timeline.gotoAndPlayNTimes=function(id,animClass,count,eventName){var el=gwd.actions.events.getElementById(id);el.gwdGotoCounters=el.gwdGotoCounters||{};var counters=el.gwdGotoCounters;var counterName=eventName+"_"+animClass+"_counter";if(typeof counters[counterName]=="undefined"){counters[counterName]=0}if(counters[counterName]<count){gwd.actions.timeline.gotoAndPlay(id,animClass)}counters[counterName]++}</script>
  <script type="text/javascript" gwd-events="handlers">gwd.auto_Page1Event_1=function(event){gwd.actions.timeline.gotoAndPlayNTimes("page1","start",50,"counter")};gwd.auto_Page1Event_11=function(event){gwd.actions.timeline.gotoAndPlayNTimes("page1","start",50,"counter")};gwd.auto_Page1Event_12=function(event){gwd.actions.timeline.gotoAndPlayNTimes("page1","start",50,"counter")};gwd.auto_Page1Event_13=function(event){gwd.actions.timeline.gotoAndPlayNTimes("page1","start",50,"counter")};gwd.auto_Page1Event_14=function(event){gwd.actions.timeline.gotoAndPlay("page1","start")}</script>
  <script type="text/javascript" gwd-events="registration">gwd.actions.events.registerEventHandlers=function(event){gwd.actions.events.addHandler("page1","event-1",gwd.auto_Page1Event_1,false);gwd.actions.events.addHandler("page1","event-1",gwd.auto_Page1Event_11,false);gwd.actions.events.addHandler("page1","event-1",gwd.auto_Page1Event_12,false);gwd.actions.events.addHandler("page1","event-1",gwd.auto_Page1Event_13,false);gwd.actions.events.addHandler("page1","event-1",gwd.auto_Page1Event_14,false);gwd.actions.timeline.captureAnimationEnd(document.body)};gwd.actions.events.deregisterEventHandlers=function(event){gwd.actions.events.removeHandler("page1","event-1",gwd.auto_Page1Event_1,false);gwd.actions.events.removeHandler("page1","event-1",gwd.auto_Page1Event_11,false);gwd.actions.events.removeHandler("page1","event-1",gwd.auto_Page1Event_12,false);gwd.actions.events.removeHandler("page1","event-1",gwd.auto_Page1Event_13,false);gwd.actions.events.removeHandler("page1","event-1",gwd.auto_Page1Event_14,false);gwd.actions.timeline.releaseAnimationEnd(document.body)};document.addEventListener("DOMContentLoaded",gwd.actions.events.registerEventHandlers);document.addEventListener("unload",gwd.actions.events.deregisterEventHandlers)</script>
</head>

<body>
  <gwd-google-ad id="gwd-ad" polite-load="">
    <gwd-metric-configuration></gwd-metric-configuration>
    <gwd-pagedeck class="gwd-page-container" id="pagedeck">
      <gwd-page id="page1" class="gwd-page-wrapper gwd-page-size gwd-lightbox" data-gwd-width="450px" data-gwd-height="350px">
        <div class="gwd-page-content gwd-page-size">
          <gwd-image id="Three_Smartphone_Screens_Mockup" source="Three_Smartphone_Screens_Mockup.png" scaling="stretch" class="gwd-image-15js"></gwd-image>
          <svg data-gwd-shape="rectangle" class="gwd-rect-j3wj gwd-gen-iey7gwdanimation"></svg>
          <gwd-image id="Ed-admin-svg-w" source="Ed-admin-svg-w.svg" scaling="stretch" class="gwd-image-12i5 gwd-gen-fj8dgwdanimation"></gwd-image><span class="gwd-span-1j6a gwd-span-1vcn gwd-gen-7n0igwdanimation">Emergency Meeting Tomorrow</span><span class="gwd-span-1j6a gwd-span-wgc0 gwd-gen-14jwgwdanimation">Dear Colleagues. An emergency meeting<br> is scheduled for tomorrow afternoon at<br> 4 PM.</span>
          <p class="gwd-p-1pyg gwd-gen-1cm0gwdanimation">NOW</p>
          <div class="gwd-animation-event event-1-animation" data-event-name="event-1" data-event-time="4600"></div>
        </div>
      </gwd-page>
    </gwd-pagedeck>
  </gwd-google-ad>
  <script type="text/javascript" id="gwd-init-code">
    (function() {
      var gwdAd = document.getElementById('gwd-ad');

      /**
       * Handles the DOMContentLoaded event. The DOMContentLoaded event is
       * fired when the document has been completely loaded and parsed.
       */
      function handleDomContentLoaded(event) {

      }

      /**
       * Handles the WebComponentsReady event. This event is fired when all
       * custom elements have been registered and upgraded.
       */
      function handleWebComponentsReady(event) {
        // Start the Ad lifecycle.
        setTimeout(function() {
          gwdAd.initAd();
        }, 0);
      }

      /**
       * Handles the event that is dispatched after the Ad has been
       * initialized and before the default page of the Ad is shown.
       */
      function handleAdInitialized(event) {}

      window.addEventListener('DOMContentLoaded',
        handleDomContentLoaded, false);
      window.addEventListener('WebComponentsReady',
        handleWebComponentsReady, false);
      window.addEventListener('adinitialized',
        handleAdInitialized, false);
    })();
  </script>


<script data-exports-type="gwd-studio-registration">function StudioExports() {
}</script><script type="text/gwd-admetadata">{"version":1,"type":"GoogleAd","format":"","template":"Banner 3.0.0","politeload":true,"fullscreen":false,"counters":[],"timers":[],"exits":[],"creativeProperties":{"minWidth":450,"minHeight":350,"maxWidth":450,"maxHeight":350},"components":["gwd-google-ad","gwd-image","gwd-page","gwd-pagedeck"],"responsive":false}</script></body></html>
